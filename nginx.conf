worker_processes auto;

pid /var/run/nginx.pid;

events {}

http {

  include /etc/nginx/mime.types;
  
  # Gzip 压缩设置
  gzip on;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
  gzip_proxied any;
  gzip_min_length 1024;
  gzip_comp_level 6;
  gzip_vary on;

  server {

    listen 4173;

    server_name localhost;

    location /{

      root /app/dist;

      index index.html;

    }

  }
}