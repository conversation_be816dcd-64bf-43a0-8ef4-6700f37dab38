services:
  web:
    build: .
    container_name: VIP-web
    restart: always
    # network_mode: host
    networks:
      - backend
    # ipv4_address: *************
    ports:
      - "4174:4173"
    # environment:
    #   - http_proxy=http://*************:7890
    #   - https_proxy=https://*************:7890

networks:
  backend:
    external: true
    name: followupsystemapi_default
    # followup_web:
    #   ipam:
    #     driver: bridge
    #     config:
    #       - subnet: "**********/16"
