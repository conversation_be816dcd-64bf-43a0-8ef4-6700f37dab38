{"name": "vip-physicalexamination-system", "version": "1.1.1", "scripts": {"mock": "vite --mode mock", "lcy": "vite --mode lcy", "mcl": "vite --mode mcl", "mclServer": "vite --mode  mclServer", "devServer": "vite --mode devServer", "dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "publish": "vite build --mode production", "check": "pnpm prettier --write .&& vue-tsc --noEmit --diagnostics", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@microsoft/signalr": "^6.0.25", "@types/node": "22.10.7", "@vueuse/core": "^12.4.0", "@wangeditor/core": "1.1.19", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "@wangeditor/table-module": "^1.1.4", "axios": "^1.7.9", "date-fns": "^4.1.0", "echarts": "^5.6.0", "element-plus": "^2.9.3", "exceljs": "^4.4.0", "jsdom": "^26.0.0", "jspdf": "^2.5.2", "konva": "^9.3.18", "less-loader": "^12.2.0", "mitt": "^3.0.1", "moment": "2.30.1", "prettier": "^3.4.2", "slate": "^0.72.8", "vue": "3.5.13", "vue-router": "4.4.5", "vue3-print-nb": "0.1.4", "vuex": "^4.1.0", "vxe-pc-ui": "1.9.5", "vxe-table": "4.7.1", "vxe-table-plugin-export-pdf": "4.0.2", "vxe-table-plugin-export-xlsx": "4.0.2", "xe-utils": "^3.5.32"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "less": "4.2.2", "typescript": "5.7.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "6.0.10", "vite-plugin-vue-devtools": "^7.7.0", "vitest": "^3.0.2", "vue-tsc": "2.2.0"}, "engines": {"node": ">=22.12.0", "npm": ">=10.9.0", "pnpm": ">=9.15.4"}}