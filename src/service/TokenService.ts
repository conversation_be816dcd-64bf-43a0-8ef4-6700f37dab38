import { requestRefreshCertificate } from "@/apis/account";
import moment from "moment";

export const refresh = async (refreshToken: string) => {
  try {
    const res = await requestRefreshCertificate(refreshToken);
    const { access_token, refresh_token, expires_in } = res.data;
    return {
      accessToken: access_token,
      refreshToken: refresh_token,
      expiration: moment().add(expires_in, "s")
    };
  } catch {
    throw new Error("刷新token失败");
  }
};
