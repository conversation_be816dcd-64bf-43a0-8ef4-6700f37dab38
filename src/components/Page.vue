<script lang="ts" setup>
import { computed, toRefs } from "vue";
import { useRoute } from "vue-router";

const props = defineProps<{
  childRouteName: string;
}>();

const route = useRoute();
const { childRouteName } = toRefs(props);

const isParentPage = computed(() => {
  return childRouteName.value != route.name;
});
</script>
<template>
  <div>
    <slot v-if="isParentPage"> </slot>
    <router-view v-slot="{ Component }" v-else>
      <component :is="Component" />
    </router-view>
  </div>
</template>
