<script setup lang="ts">
const props = defineProps<{
  modelValue: string;
}>();
defineEmits<{
  "update:modelValue": [value: string];
}>();

const content = computed(() => props.modelValue);
</script>

<template>
  <div class="html-container" style="margin-top: 10px">
    <div v-html="content"></div>
  </div>
</template>

<style lang="less" scoped>
.html-container {
  margin-top: 10px;

  :deep(table) {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
    margin-bottom: 1em;

    th,
    td {
      border: 1px solid #d9d9d9;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: #fafafa;
      font-weight: 500;
    }

    tr:nth-child(even) {
      background-color: #fafafa;
    }

    tr:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>
