<script lang="ts" setup>
// import { FollowUpUserItem } from "@/composables/useFollowUpUsers";
import { VxeTableInstance } from "vxe-table";

// interface User extends FollowUpUserItem {}
interface TableHeader {
  field: string;
  title: string;
}

const props = defineProps<{
  tableHeader: TableHeader[]; //*表头字段
  data: any[]; //*表格数据
  topHtml?: string; //头部内容
  bottomHtml?: string; //底部内容
  printStyle?: string; //样式，一般控制头部内容和底部内容
}>();

const { tableHeader, data } = toRefs(props);
const xTable = ref<VxeTableInstance>();

const onPrintClick = () => {
  const $table = xTable.value;
  const _printTopHtml: string = props?.topHtml ?? "";
  const _bottomHtml: string = props?.bottomHtml ?? "";
  const _style: string = props?.printStyle ?? "";
  $table?.print({
    style: _style,
    columns: tableHeader.value,
    beforePrintMethod: ({ content }) => {
      // 拦截打印之前，返回自定义的 html 内容
      return _printTopHtml + content + _bottomHtml;
    }
  });
};
</script>
<template>
  <div class="print-table-component">
    <div class="tools">
      <vxe-toolbar class="vxe-toolbar">
        <template #buttons>
          <el-button type="primary" @click="onPrintClick">打印</el-button>
        </template>
      </vxe-toolbar>
    </div>

    <vxe-table :data="data" ref="xTable" class="table" height="600">
      <vxe-column type="seq" width="60"></vxe-column>
      <vxe-column v-for="header in tableHeader" :field="header.field" :title="header.title" />
    </vxe-table>
  </div>
</template>
