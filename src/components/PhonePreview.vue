<script lang="ts" setup>
import { Move as vMove } from "@/utils/move";
import { Close } from "@element-plus/icons-vue";

const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    html: string;
    height?: string;
    width?: string;
    zIndex?: number;
  }>(),
  {
    height: "50%",
    width: "30%",
    zIndex: 9
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", v: boolean): void;
}>();

const { modelValue, html, height, width } = toRefs(props);

const innerHtml = ref(html.value);

watch(html, () => {
  let result = handleImage(html.value);
  result = handleVideo(result);
  innerHtml.value = result;
});

const handleImage = (html: string) => {
  let result = html.replace(/style="*?"(?<=<img.+)/g, `style="width:100%; height:auto;"`);
  return result;
};

const handleVideo = (html: string) => {
  let result = html.replace(/(?<=<video.+)width=".*?"/g, `width="100%"`);
  result = result.replace(/(?<=<video.+)height=".*?"/g, `height="142px"`);
  return result;
};

const handleTable = (html: string) => {
  let result = html.replace(
    /<table/g,
    `<table style="border-collapse: collapse; border: 1px solid #ccc; min-width: 50px; height: 20px; background-color: #f1f1f1;">`
  );
  return result;
};

const onCloseClick = () => {
  emit("update:modelValue", false);
};

onMounted(() => {
  let handleHtml = handleImage(html.value);
  handleHtml = handleVideo(handleHtml);
  handleHtml = handleTable(handleHtml);
  innerHtml.value = handleHtml;
});
</script>
<template>
  <div
    v-if="modelValue"
    v-move="null"
    class="phone-warp"
    :style="{ 'height': height, 'width': width, 'z-index': zIndex }"
  >
    <div class="title">
      <el-icon class="icon" @click="onCloseClick"><Close /></el-icon>
    </div>

    <div class="content" v-html="innerHtml"></div>
  </div>
</template>
<style lang="less" scoped>
.phone-warp {
  display: flex;
  flex-direction: column;
  position: absolute;
  overflow: hidden;
  background-color: white;
  border-radius: 8px;
  user-select: none;
  border: 1px #ccc solid;
  .title {
    height: 30px;
    line-height: 30px;
    background-color: aliceblue;
    .icon {
      position: absolute;
      right: 3px;
      top: 5px;
      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
  .content {
    flex: 1;
    padding: 10px;
    overflow: auto;

    :deep(table) {
      border-collapse: collapse;
      border-spacing: 0;
      width: 100%;
      margin-bottom: 1em;

      th,
      td {
        border: 1px solid #d9d9d9;
        padding: 8px;
        text-align: left;
        min-width: 60px;
      }

      th {
        background-color: #fafafa;
        font-weight: 500;
      }

      tr:nth-child(even) {
        background-color: #fafafa;
      }

      tr:hover {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
