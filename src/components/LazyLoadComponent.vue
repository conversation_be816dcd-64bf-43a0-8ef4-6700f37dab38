<script lang="ts" setup>
defineOptions({ name: "LazyLoadComponent" });

const props = defineProps<{
  asyncComponent: Component | string;
  skeletonRows?: number;
}>();

const skeletonRows = computed(() => props.skeletonRows || 3);
const isLoaded = ref(false);
const containerRef = ref<HTMLElement | null>(null);

let observer: IntersectionObserver;

onMounted(() => {
  observer = new IntersectionObserver(
    entries => {
      if (entries[0].isIntersecting) {
        isLoaded.value = true;
        observer.disconnect();
      }
    },
    { threshold: 0.1 }
  );

  if (containerRef.value) {
    observer.observe(containerRef.value);
  }
});

// 组件卸载时清理
onBeforeUnmount(() => {
  stop();
  isLoaded.value = false;
});
</script>

<template>
  <div ref="containerRef" class="lazy-load-component">
    <component v-if="isLoaded" :is="asyncComponent" v-bind="$attrs" />
    <el-skeleton v-else :rows="skeletonRows" animated />
  </div>
</template>
<style lang="less" scoped>
.lazy-load-component {
  height: 100%;
  width: 100%;
}
</style>
