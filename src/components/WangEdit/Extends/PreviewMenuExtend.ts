import { <PERSON>, Transforms, Node, Element } from "slate";
import { I<PERSON>uttonMenu, IDomE<PERSON>or, DomEditor, t } from "@wangeditor/core";

export type StyleOfImage = {
  height: string;
  width: string;
  float: string;
};

class TextAroundMenuExtend implements IButtonMenu {
  readonly title = t("手机预览");

  readonly iconSvg = `<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-78e17ca8=""><path fill="currentColor" d="M224 768v96.064a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V768H224zm0-64h576V160a64 64 0 0 0-64-64H288a64 64 0 0 0-64 64v544zm32 288a96 96 0 0 1-96-96V128a96 96 0 0 1 96-96h512a96 96 0 0 1 96 96v768a96 96 0 0 1-96 96H256zm304-144a48 48 0 1 1-96 0 48 48 0 0 1 96 0z"></path></svg>`;

  readonly tag = "button";

  getValue = (editor: IDomEditor) => {
    return "";
  };

  isActive = (editor: IDomEditor) => {
    const node = DomEditor.getSelectedNodeByType(editor, "blockquote");
    return !!node;
  };

  isDisabled = (editor: IDomEditor) => {
    return false;
  };

  exec = (editor: IDomEditor, value: string | boolean) => {
    editor.emit("review");
  };
}

export default TextAroundMenuExtend;
