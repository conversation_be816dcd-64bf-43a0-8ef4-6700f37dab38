import { <PERSON>, Transforms, Node, Element } from "slate";
import { IButtonMenu, IDomEditor, DomEditor, t } from "@wangeditor/core";

export type StyleOfImage = {
  height: string;
  width: string;
  float: string;
};

class TextAroundMenuExtend implements IButtonMenu {
  readonly title = t("文字环绕");

  readonly iconSvg = `<svg viewBox="0 0 1024 1024"><path d="M192 128v768h640V128H192zm-32-64h704a32 32 0 0 1 32 32v832a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32zm160 448h384v64H320v-64zm0-192h192v64H320v-64zm0 384h384v64H320v-64z"></path></svg>`;

  readonly tag = "button";

  //css转对象
  cssToObj = (css: string) => {
    const styleArr = css.split(";").filter(s => s);
    const styleObj: any = {};
    styleArr.forEach(item => {
      const splitOfStyle = item.split(":");
      if (splitOfStyle.length == 2) {
        const styleName = splitOfStyle[0].trim();
        const styleValue = splitOfStyle[1].trim();
        styleObj[styleName] = styleValue;
      }
    });
    return styleObj;
  };

  //对象转css
  toCssStr(obj: Object) {
    let cssString = "";
    for (const [key, value] of Object.entries(obj)) cssString += `${key}:${value};`;
    return cssString;
  }

  getValue = (editor: IDomEditor) => {
    return "";
  };

  isActive = (editor: IDomEditor) => {
    const node = DomEditor.getSelectedNodeByType(editor, "blockquote");
    return !!node;
  };

  isDisabled = (editor: IDomEditor) => {
    if (editor.selection == null) return true;

    const [nodeEntry] = Editor.nodes(editor, {
      match: n => {
        const type = DomEditor.getNodeType(n);

        // 只可用于 p 和 blockquote
        // if (type === "paragraph") return true;
        // if (type === "blockquote") return true;

        return type === "paragraph" || type === "blockquote";

        // return false;
      },
      universal: true,
      mode: "highest" // 匹配最高层级
    });

    // 匹配到 p blockquote ，不禁用
    if (nodeEntry) {
      return false;
    }
    // 未匹配到，则禁用
    return true;
  };

  exec = (editor: IDomEditor, value: string | boolean) => {
    if (this.isDisabled(editor)) return;
    const nodeEntries = Editor.nodes(editor, {
      match: (node: Node) => {
        if (Element.isElement(node)) {
          //@ts-ignore
          if (node.type === "image") {
            return true; // 匹配图片
          }
        }
        return false;
      },
      universal: true
    });

    if (nodeEntries) {
      for (let nodeEntry of nodeEntries) {
        const [node] = nodeEntry;
        const imgDom = editor.toDOMNode(node).children[0];
        const style = imgDom.getAttribute("style");
        const styleObj = style ? this.cssToObj(style) : {};
        let { float } = styleObj;
        if (!float) {
          float = "left";
        } else if (float == "left") {
          float = "right";
        } else {
          float = "";
        }
        styleObj["float"] = float;
        imgDom.setAttribute("style", this.toCssStr(styleObj));
        const orginHtml = imgDom.children[0].outerHTML;
        const imageSrc = imgDom.children[0].getAttribute("src");
        let imageHtml = orginHtml;
        imageHtml = imageHtml.replace(/style.+"/g, `style="${this.toCssStr(styleObj)}"`);
        const replaceHtml = editor
          .getHtml()
          .replace(new RegExp("<img.+" + imageSrc + ".*?>", "g"), imageHtml);
        console.log(editor.getParentNode(node));
      }
    }
  };
}

export default TextAroundMenuExtend;
