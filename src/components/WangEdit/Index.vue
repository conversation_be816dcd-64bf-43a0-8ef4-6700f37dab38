<script lang="ts">
import token from "@/utils/token";
import { Boot, SlateTransforms } from "@wangeditor/editor";
import { PreviewMenuConfig } from "@/components/WangEdit/Extends";
// import tableModule from "@wangeditor/table-module";

// const apiUrl = window.config.apiUrl;
const apiUrl = import.meta.env.VITE_APP_API_URL;
const WangEditApiUrl = window.config.WangEditApiUrl;

export interface UploadFileResponse {
  createTime: Date;
  format: string;
  id: number;
  name: string;
  src: string;
}

Boot.registerMenu(PreviewMenuConfig);
// Boot.registerModule(tableModule);

type InsertFnType = (url: string, alt: string, href: string) => void;

let editor: any;

const TOKENT = token.accessToken;
</script>
<script lang="ts" setup>
import PhonePreview from "@/components/PhonePreview.vue";
import "@wangeditor/editor/dist/css/style.css";
import {
  createEditor,
  createToolbar,
  IEditorConfig,
  IToolbarConfig,
  IDomEditor
} from "@wangeditor/editor";
import { generateUniqueId } from "@/utils/tools";

const props = withDefaults(
  defineProps<{
    modelValue: string;
    excludeKeys?: string[];
  }>(),
  {
    modelValue: "",
    excludeKeys: () => []
  }
);

const emit = defineEmits<{ (e: "update:modelValue", v: string): void }>();

const { modelValue, excludeKeys } = toRefs(props);

const UNIQUE_ID = generateUniqueId();

const EDITOR_CONTAINER_ID = `editor-${UNIQUE_ID}-container`;

const TOOLBAR_CONTAINER_ID = `toolbar-${UNIQUE_ID}-container`;

const isShowPreview = ref(false);

const editorConfig: Partial<IEditorConfig> = { MENU_CONF: {} };

editorConfig.scroll = true;

const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ["fullScreen", "redo", "undo", "codeBlock", ...excludeKeys.value],
  insertKeys: {
    index: -1,
    keys: ["previewMenu"]
  }
};

editorConfig.placeholder = "请输入内容";

//配置图片上传
editorConfig.MENU_CONF!["uploadImage"] = {
  server: `${apiUrl}/api/articles/images`,
  maxFileSize: 5 * 1024 * 1024,
  maxNumberOfFiles: 1,
  allowedFileTypes: ["image/*"],
  fieldName: "formFile",

  headers: {
    Authorization: `Bearer ${TOKENT}`
  },
  timeout: 5 * 1000,
  onSuccess(file: File, res: any) {
    console.log(`${file.name} 上传成功`, res);
  },
  onError(file: File, err: any, res: any) {
    console.log(`${file.name} 上传出错`, file, err, res);
  },
  customInsert(res: any, insertFn: InsertFnType) {
    // const { data } = res;
    const { url } = res;
    console.log(res);

    if (url) {
      // insertFn(`${WangEditApiUrl}${url}`, "", `${WangEditApiUrl}${url}`);
      insertFn(`${apiUrl}${url}`, "", `${apiUrl}${url}`);
    }
    // if (data.length > 0) {
    //   data.forEach((item: UploadFileResponse) => {
    //     insertFn(
    //       `${WangEditApiUrl}${item.src}`,
    //       "",
    //       `${WangEditApiUrl}${item.src}`
    //     );
    //   });
    // }
  }
};

//配置视频上传
editorConfig.MENU_CONF!["uploadVideo"] = {
  server: `${apiUrl}/api/articles/videos`,

  maxNumberOfFiles: 1,

  fieldName: "formFile",

  allowedFileTypes: ["video/*"],

  headers: {
    Authorization: `Bearer ${TOKENT}`
  },

  maxFileSize: 1024 * 1024 * 1024,
  // maxFileSize: 1024 * 1024,

  // timeout: 5 * 1000,

  onSuccess(file: File, res: any) {
    console.log(`${file.name} 上传成功`, res);
  },

  onError(file: File, err: any, res: any) {
    console.log(`${file.name} 上传出错`, file, err, res);
  },

  customInsert(res: any, insertFn: InsertFnType) {
    // const { data } = res;

    const { url } = res;
    if (url) {
      insertFn(`${apiUrl}${url}`, "", `${apiUrl}${url}`);
    }

    // if (data.length > 0) {
    //   data.forEach((item: UploadFileResponse) => {
    //     insertFn(
    //       `${WangEditApiUrl}${item.src}`,
    //       "",
    //       `${WangEditApiUrl}${item.src}`
    //     );
    //   });
    // }
  }
};

// 配置表格功能
// editorConfig.MENU_CONF!["insertTable"] = {
//   maxRow: 20,
//   maxCol: 6
// };

editorConfig.onChange = (editor: IDomEditor) => {
  const html = editor.getHtml();
  emit("update:modelValue", html);
};

watch(modelValue, v => {
  nextTick(() => {
    if (editor?.getHtml() != v) editor?.setHtml(v);
  });
});

onMounted(() => {
  editor = createEditor({
    html: modelValue.value,
    selector: `#${EDITOR_CONTAINER_ID}`,
    config: editorConfig,
    mode: "default"
  });

  const toolbar = createToolbar({
    editor,
    selector: `#${TOOLBAR_CONTAINER_ID}`,
    config: toolbarConfig,
    mode: "default"
  });

  //监听编辑器事件
  editor.on("review", () => {
    isShowPreview.value = true;
  });
});
</script>
<template>
  <div class="wang-edit-panel">
    <div :id="TOOLBAR_CONTAINER_ID" style="width: 100%; border-bottom: 1px #c5c5c5 solid"></div>
    <div :id="EDITOR_CONTAINER_ID" style="flex: 1; min-height: 50rem"></div>
    <phone-preview v-model="isShowPreview" :html="modelValue" height="80%" width="28rem" />
  </div>
</template>
<style lang="less" scoped>
:deep(video) {
  width: 6rem;
}
.wang-edit-panel {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border: 1px #c5c5c5 solid;
  border-radius: 8px;
  overflow: hidden;
}
:deep(.w-e-text-container) {
  p {
    margin: 0;
  }
  .w-e-image-container {
    margin: 1rem;
  }
}
:deep(.w-e-textarea-video-container) {
  overflow-x: scroll;
}
</style>
