<script lang="ts" setup>
defineOptions({
  name: "UploadPDFComponent"
});

const props = defineProps<{
  modelValue: string;
  // PDFId: number;
  emptyText?: string;
  isReadOnly?: boolean;
}>();
const emit = defineEmits<{
  "update:modelValue": [modelValue: string];
  "fileChange": [file: File];
}>();
const PDFUrl = computed({
  get() {
    return props.modelValue;
  },
  set(v) {
    emit("update:modelValue", v);
  }
});
// const PDFUrl = ref("");

const awaitUploadFile = ref<File | null>(null);
function selectPDF() {
  const input = document.createElement("input");
  input.setAttribute("type", "file");
  input.setAttribute("accept", "application/pdf");
  input.click();
  input.onchange = () => {
    const _file = input.files?.item(0);
    if (!_file) {
      return;
    }
    if (_file.type !== "application/pdf") {
      ElMessage.warning("文件格式错误!");
      return;
    }
    awaitUploadFile.value = _file;
    emit("fileChange", awaitUploadFile.value);
    PDFUrl.value = URL.createObjectURL(awaitUploadFile.value);
    input.remove();
  };
}

function removePDF() {
  if (!PDFUrl.value) {
    return;
  }
  URL.revokeObjectURL(PDFUrl.value);
  PDFUrl.value = "";
}

onUnmounted(() => {
  removePDF();
});
</script>

<template>
  <div class="upload-PDF-component">
    <div class="upload-PDF-component__tool">
      <el-button type="primary" text bg @click="selectPDF" :disabled="isReadOnly"
        >选择文件</el-button
      >
      <el-button type="primary" text bg @click="removePDF" :disabled="PDFUrl == '' || isReadOnly"
        >清除文件</el-button
      >
    </div>
    <div class="pdf-container" v-if="PDFUrl">
      <embed :src="PDFUrl" type="application/pdf" width="100%" height="100%" />
    </div>
    <div class="pdf-container" v-else>
      {{ emptyText || "暂无文件，请选择文件" }}
    </div>
  </div>
</template>

<style lang="less" scoped>
.upload-PDF-component {
  height: 100%;
  width: 100%;
  display: grid;
  grid-template-rows: 24px minmax(400px, 60vh);
  gap: 10px;
  .pdf-container {
    // width: 450px;
    width: 100%;
    height: 100%;
    // height: 450px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #bfbfbf;
    font-size: 24px;
    font-weight: 400;
    border: 1px solid #e4e4e4;
    border-radius: 4px;
  }
}
</style>
