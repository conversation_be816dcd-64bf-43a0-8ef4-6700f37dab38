import { createGlobalState } from "@vueuse/core";
import { ExamineSortRule, SortRule, SortRuleDetail } from "@/types/reportSortRules";
import { ExaminesForPrint, useStoreExaminesForPrint } from "../storeExaminesForPrint";
import { OrderingRulesApi } from "@/apis/orderingRules";

export const useStoreExaminesSort = createGlobalState(() => {
  const storeExaminesSort = ref<SortRule | null>(null);
  const { storeExaminesForPrint } = useStoreExaminesForPrint();

  watch(
    storeExaminesForPrint,
    () => {
      storeExaminesForPrintToInspectionSort();
    },
    { deep: true }
  );

  async function loadData() {
    storeExaminesSort.value = await OrderingRulesApi.getExaminesRules();
    storeExaminesForPrintToInspectionSort();
  }

  async function save() {
    if (!storeExaminesSort.value?.id) return;
    await OrderingRulesApi.saveExaminesRules(storeExaminesSort.value as ExamineSortRule);
  }

  function orderUp(item: SortRuleDetail) {
    if (!storeExaminesSort.value) return;
    const detail = storeExaminesSort.value.detail;
    const index = detail.findIndex(d => d.itemCode === item.itemCode);
    if (index === -1 || index === 0) return;

    const prevSort = detail[index - 1].sort;
    const nextSort = index < detail.length - 1 ? detail[index + 1].sort : prevSort - 100;

    // 计算新的排序值：要比前一项大
    const newSort = prevSort + Math.max(1, Math.floor((prevSort - nextSort) / 2));
    detail[index].sort = newSort;

    // 重新排序数组：数字大的在前
    detail.sort((a, b) => b.sort - a.sort);
    storeExaminesForPrintToInspectionSort();
  }

  function orderDown(item: SortRuleDetail) {
    if (!storeExaminesSort.value) return;
    const detail = storeExaminesSort.value.detail;
    const index = detail.findIndex(d => d.itemCode === item.itemCode);
    if (index === -1 || index === detail.length - 1) return;

    const nextSort = detail[index + 1].sort;
    const prevSort = index > 0 ? detail[index - 1].sort : nextSort + 100;

    // 计算新的排序值：要比下一项小
    const newSort = nextSort - Math.max(1, Math.floor((prevSort - nextSort) / 2));
    detail[index].sort = newSort;

    // 重新排序数组：数字大的在前
    detail.sort((a, b) => b.sort - a.sort);
    storeExaminesForPrintToInspectionSort();
  }

  function storeExaminesForPrintToInspectionSort() {
    if (!storeExaminesSort.value) return;
    const details = mapToSortRuleDetails(storeExaminesForPrint.value);
    const mergedDetails = diffAndMergeRuleDetails(details);
    if (mergedDetails) {
      storeExaminesSort.value.detail = mergedDetails;
    }
  }
  function diffAndMergeRuleDetails(details: SortRuleDetail[]) {
    if (!storeExaminesSort.value) return;
    const detail = storeExaminesSort.value.detail;

    // 标记现有项为旧项
    const existingDetails = detail.map(item => ({
      ...item
    }));

    // 找出新项（不在现有规则中的项）
    const newDetails = details.filter(
      item => !existingDetails.some(d => d.itemCode === item.itemCode)
    );

    // 合并旧项和新项
    return [...existingDetails, ...newDetails];
  }

  function mapToSortRuleDetails(examinesForPrint: ExaminesForPrint): SortRuleDetail[] {
    if (examinesForPrint.size == 0) return [];
    return Array.from(examinesForPrint.entries()).map(([itemClass, innerMap]) => ({
      itemCode: itemClass,
      itemName: Array.from(innerMap.values())[0][0]?.itemClassName ?? "",
      sort: 0,
      isOld: false // 新项目默认为 false
    }));
  }
  return {
    storeExaminesSort,
    loadData,
    save,
    orderUp,
    orderDown,
    diffAndMergeRuleDetails,
    storeExaminesForPrintToInspectionSort
  };
});
