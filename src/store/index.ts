import { InjectionKey } from "vue";
import { createStore, useStore as baseUseStore, Store } from "vuex";
import type { InspectionDetailForReport } from "@/types/inspection";
import type { ExamineDetailForReport } from "@/types/examine";

export interface State {
  notifications: {
    id: number;
    title: string;
    content: string;
    readed: boolean;
    createTime: Date;
  }[];
  doctor: {
    id: number;
    userName: string;
    name: string;
    headPortrait: string;
    roleId: number;
    isAdmin: boolean;
  };

  patientOrganData: PatientOrganData[];
  summaryRecords: SummaryRecordsData[];
  needPrintInspectionDetails: InspectionDetailForReport[];
  needPrintExamineDetails: ExamineDetailForReport[];
}

interface SummaryRecordsData {
  id: number;
  content: string;
  time: Date;
}

interface PatientOrganData {
  name: string;
  abnormalInformation?: string;
}
const state = {
  notifications: [],
  doctor: {
    id: 0,
    userName: "",
    name: "",
    headPortrait: "",
    roleId: 0,
    isAdmin: false
  },
  patientOrganData: [], // 病人异常部位数据
  summaryRecords: [], // 病人小结数据
  needPrintInspectionDetails: [],
  needPrintExamineDetails: []
};

const mutations = {
  setNotifications(state: State, notifications: State["notifications"]) {
    state.notifications = notifications;
  },
  addNotification(state: State, notification: State["notifications"][number]) {
    state.notifications.unshift(notification);
  },
  readNotification(state: State, id: number) {
    const notification = state.notifications.find(item => item.id == id);
    if (notification) notification.readed = true;
  },
  readAllNotification(state: State) {
    for (const notification of state.notifications) {
      notification.readed = true;
    }
  },
  setDoctor(state: State, doctor: State["doctor"]) {
    state.doctor = doctor;
  },

  setSummaryRecords(state: State, summaryRecords: SummaryRecordsData[]) {
    state.summaryRecords = summaryRecords;
  },
  setPatientOrganData(state: State, patientOrganData: State["patientOrganData"]) {
    state.patientOrganData = patientOrganData;
  }
};

export const key: InjectionKey<Store<State>> = Symbol();

export const store = createStore<State>({
  state,
  mutations
});

export const useStore = () => {
  return baseUseStore(key);
};
