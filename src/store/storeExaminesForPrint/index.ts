import { createGlobalState } from "@vueuse/core";
import { Examine } from "@/types/examine";

export type ExaminesForPrint = Map<Examine["itemClass"], Map<Examine["itemCode"], Examine[]>>;

export const useStoreExaminesForPrint = createGlobalState(() => {
  const storeExaminesForPrint = ref<ExaminesForPrint>(new Map());

  function checkedToStoreExaminesForPrint(data: ExaminesForPrint) {
    data.forEach((value, key) => {
      storeExaminesForPrint.value.set(key, value);
    });

    triggerRef(storeExaminesForPrint);
  }

  function setExaminesForPrint(data: ExaminesForPrint) {
    checkedToStoreExaminesForPrint(data);
  }

  function clearExaminesForPrint() {
    storeExaminesForPrint.value.clear();
    triggerRef(storeExaminesForPrint);
  }

  return {
    storeExaminesForPrint,
    setExaminesForPrint,
    clearExaminesForPrint
  };
});
