import { Patient } from "@/types/patient";
import { useStorage } from "@vueuse/core";
import { deepClone } from "@/utils/collection";
import { getPatientById } from "@/apis/patients";

enum PersistKey {
  patientId = "patientId",
  patient = "patient"
}

export const useStorePatient = () => {
  const storePatientId = useStorage<number>(PersistKey.patientId, 0);
  const storePatient = useStorage<Patient>(PersistKey.patient, null, undefined, {
    serializer: {
      read: (v: string) => (v ? JSON.parse(v) : null),
      write: (v: Patient) => JSON.stringify(v)
    }
  });

  function setStorePatient(patient: Patient) {
    if (!patient || typeof patient.id !== "number") {
      throw new Error("无效的病人信息");
    }
    storePatientId.value = patient.id;
    storePatient.value = deepClone(patient);
  }

  async function loadStorePatient(patientId: Patient["id"]) {
    try {
      const patient = await getPatientById(patientId);
      setStorePatient(patient);
      return patient;
    } catch (error) {
      console.error("加载病人信息失败:", error);
    }
  }

  function clearStorePatient() {
    storePatient.value = null;
    storePatientId.value = 0;
  }

  return {
    storePatientId,
    storePatient,
    setStorePatient,
    loadStorePatient,
    clearStorePatient
  };
};
