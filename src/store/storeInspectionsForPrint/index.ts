import { createGlobalState } from "@vueuse/core";
import { Inspection } from "@/types/inspection";

export type InspectionsForPrint = Map<
  Inspection["itemCode"],
  Map<Inspection["itemNameCode"], Inspection[]>
>;

export const useStoreInspectionsForPrint = createGlobalState(() => {
  const storeInspectionsForPrint = ref<InspectionsForPrint>(new Map());

  function checkedToStoreInspectionsForPrint(data: InspectionsForPrint) {
    data.forEach((value, key) => {
      storeInspectionsForPrint.value.set(key, value);
    });

    triggerRef(storeInspectionsForPrint);
  }

  function setInspectionsForPrint(data: InspectionsForPrint) {
    // checkedToStoreInspectionsForPrint(data);
    storeInspectionsForPrint.value = data;
  }

  function clearInspectionsForPrint() {
    storeInspectionsForPrint.value.clear();
    triggerRef(storeInspectionsForPrint);
  }

  return {
    storeInspectionsForPrint,
    setInspectionsForPrint,
    checkedToStoreInspectionsForPrint,
    clearInspectionsForPrint
  };
});
