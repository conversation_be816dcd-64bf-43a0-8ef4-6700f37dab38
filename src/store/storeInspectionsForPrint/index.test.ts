import { describe, it, expect, beforeEach } from 'vitest';
import { useStoreInspectionsForPrint, type InspectionsForPrint } from './index';
import type { Inspection } from '@/types/inspection';

describe('useStoreInspectionsForPrint', () => {
  const { storeInspectionsForPrint, setInspectionsForPrint, checkedToStoreInspectionsForPrint, clearInspectionsForPrint } = useStoreInspectionsForPrint();

  beforeEach(() => {
    // Clear the store before each test
    clearInspectionsForPrint();
  });

  it('should initialize with an empty Map', () => {
    expect(storeInspectionsForPrint.value).toBeInstanceOf(Map);
    expect(storeInspectionsForPrint.value.size).toBe(0);
  });

  it('should set inspections correctly', () => {
    const mockInspection: Inspection = {
      id: 1,
      compositeItemSn: 'sn1',
      execDeptName: 'Test Department',
      highValue: '100',
      inpatientOrdNo: null,
      itemCode: 'item1',
      itemName: 'Test Item',
      itemNameCn: 'Test Item CN',
      itemNameCode: 'name1',
      itemNameEn: 'Test Item EN',
      itemUnit: 'units',
      itemValue: '50',
      labReportLid: 'report1',
      labReportSn: 1,
      lowValue: '0',
      normalFlagName: 'Normal',
      normalRefValueText: '0-100',
      orderLid: 'order1',
      orderPersonName: 'Dr. Test',
      patientDomain: 'domain1',
      patientLid: 'patient1',
      patientName: 'Test Patient',
      phenomenonPerformance: 'Normal',
      reportDate: '2025-02-14',
      reporterId: 'reporter1',
      reporterName: 'Dr. Reporter',
      requestDate: '2025-02-14',
      requestNo: 'REQ001',
      reviewDate: '2025-02-14',
      reviewerId: 'reviewer1',
      reviewerName: 'Dr. Reviewer',
      sampleNo: 'SAMPLE001',
      sampleTypeName: 'Blood',
      submittingPersonId: 'submitter1',
      submittingPersonName: 'Dr. Submitter',
      testResults: null,
      visitOrdNo: 'VISIT001',
      visitTimes: 1,
      visitTypeCode: 'OPD',
      visitTypeName: 'Outpatient',
      warnHighValue: null,
      warnLowValue: null
    };

    const innerMap = new Map<string, Inspection[]>();
    innerMap.set('name1', [mockInspection]);

    const mockData: InspectionsForPrint = new Map();
    mockData.set('item1', innerMap);

    setInspectionsForPrint(mockData);

    expect(storeInspectionsForPrint.value).toEqual(mockData);
    expect(storeInspectionsForPrint.value.get('item1')?.get('name1')?.[0]).toEqual(mockInspection);
  });

  it('should check and merge inspections correctly', () => {
    const mockInspection1: Inspection = {
      id: 1,
      compositeItemSn: 'sn1',
      execDeptName: 'Test Department',
      highValue: '100',
      inpatientOrdNo: null,
      itemCode: 'item1',
      itemName: 'Test Item',
      itemNameCn: 'Test Item CN',
      itemNameCode: 'name1',
      itemNameEn: 'Test Item EN',
      itemUnit: 'units',
      itemValue: '50',
      labReportLid: 'report1',
      labReportSn: 1,
      lowValue: '0',
      normalFlagName: 'Normal',
      normalRefValueText: '0-100',
      orderLid: 'order1',
      orderPersonName: 'Dr. Test',
      patientDomain: 'domain1',
      patientLid: 'patient1',
      patientName: 'Test Patient',
      phenomenonPerformance: 'Normal',
      reportDate: '2025-02-14',
      reporterId: 'reporter1',
      reporterName: 'Dr. Reporter',
      requestDate: '2025-02-14',
      requestNo: 'REQ001',
      reviewDate: '2025-02-14',
      reviewerId: 'reviewer1',
      reviewerName: 'Dr. Reviewer',
      sampleNo: 'SAMPLE001',
      sampleTypeName: 'Blood',
      submittingPersonId: 'submitter1',
      submittingPersonName: 'Dr. Submitter',
      testResults: null,
      visitOrdNo: 'VISIT001',
      visitTimes: 1,
      visitTypeCode: 'OPD',
      visitTypeName: 'Outpatient',
      warnHighValue: null,
      warnLowValue: null
    };

    const mockInspection2: Inspection = {
      id: 2,
      compositeItemSn: 'sn2',
      execDeptName: 'Test Department 2',
      highValue: '200',
      inpatientOrdNo: null,
      itemCode: 'item2',
      itemName: 'Test Item 2',
      itemNameCn: 'Test Item CN 2',
      itemNameCode: 'name2',
      itemNameEn: 'Test Item EN 2',
      itemUnit: 'units 2',
      itemValue: '100',
      labReportLid: 'report2',
      labReportSn: 2,
      lowValue: '0',
      normalFlagName: 'Normal',
      normalRefValueText: '0-200',
      orderLid: 'order2',
      orderPersonName: 'Dr. Test 2',
      patientDomain: 'domain2',
      patientLid: 'patient2',
      patientName: 'Test Patient 2',
      phenomenonPerformance: 'Normal',
      reportDate: '2025-02-14',
      reporterId: 'reporter2',
      reporterName: 'Dr. Reporter 2',
      requestDate: '2025-02-14',
      requestNo: 'REQ002',
      reviewDate: '2025-02-14',
      reviewerId: 'reviewer2',
      reviewerName: 'Dr. Reviewer 2',
      sampleNo: 'SAMPLE002',
      sampleTypeName: 'Blood',
      submittingPersonId: 'submitter2',
      submittingPersonName: 'Dr. Submitter 2',
      testResults: null,
      visitOrdNo: 'VISIT002',
      visitTimes: 1,
      visitTypeCode: 'OPD',
      visitTypeName: 'Outpatient',
      warnHighValue: null,
      warnLowValue: null
    };

    // Create first set of data
    const innerMap1 = new Map<string, Inspection[]>();
    innerMap1.set('name1', [mockInspection1]);
    const mockData1: InspectionsForPrint = new Map();
    mockData1.set('item1', innerMap1);

    // Create second set of data
    const innerMap2 = new Map<string, Inspection[]>();
    innerMap2.set('name2', [mockInspection2]);
    const mockData2: InspectionsForPrint = new Map();
    mockData2.set('item2', innerMap2);

    // Set initial data
    setInspectionsForPrint(mockData1);
    // Check/merge additional data
    checkedToStoreInspectionsForPrint(mockData2);

    expect(storeInspectionsForPrint.value.size).toBe(2);
    expect(storeInspectionsForPrint.value.get('item1')?.get('name1')?.[0]).toEqual(mockInspection1);
    expect(storeInspectionsForPrint.value.get('item2')?.get('name2')?.[0]).toEqual(mockInspection2);
  });

  it('should clear inspections correctly', () => {
    const mockInspection: Inspection = {
      id: 1,
      compositeItemSn: 'sn1',
      execDeptName: 'Test Department',
      highValue: '100',
      inpatientOrdNo: null,
      itemCode: 'item1',
      itemName: 'Test Item',
      itemNameCn: 'Test Item CN',
      itemNameCode: 'name1',
      itemNameEn: 'Test Item EN',
      itemUnit: 'units',
      itemValue: '50',
      labReportLid: 'report1',
      labReportSn: 1,
      lowValue: '0',
      normalFlagName: 'Normal',
      normalRefValueText: '0-100',
      orderLid: 'order1',
      orderPersonName: 'Dr. Test',
      patientDomain: 'domain1',
      patientLid: 'patient1',
      patientName: 'Test Patient',
      phenomenonPerformance: 'Normal',
      reportDate: '2025-02-14',
      reporterId: 'reporter1',
      reporterName: 'Dr. Reporter',
      requestDate: '2025-02-14',
      requestNo: 'REQ001',
      reviewDate: '2025-02-14',
      reviewerId: 'reviewer1',
      reviewerName: 'Dr. Reviewer',
      sampleNo: 'SAMPLE001',
      sampleTypeName: 'Blood',
      submittingPersonId: 'submitter1',
      submittingPersonName: 'Dr. Submitter',
      testResults: null,
      visitOrdNo: 'VISIT001',
      visitTimes: 1,
      visitTypeCode: 'OPD',
      visitTypeName: 'Outpatient',
      warnHighValue: null,
      warnLowValue: null
    };

    const innerMap = new Map<string, Inspection[]>();
    innerMap.set('name1', [mockInspection]);

    const mockData: InspectionsForPrint = new Map();
    mockData.set('item1', innerMap);

    setInspectionsForPrint(mockData);
    expect(storeInspectionsForPrint.value.size).toBe(1);

    clearInspectionsForPrint();
    expect(storeInspectionsForPrint.value.size).toBe(0);
  });
});
