# 检验排序存储模块 (Store Inspection Sort)

## 功能说明
该模块是一个全局状态管理模块，主要用于处理检验项目的排序规则管理和排序执行。使用 `@vueuse/core` 的 `createGlobalState` 实现全局状态管理。

## 主要功能
1. 排序规则的加载和存储
2. 根据规则对检验项目进行排序

## 核心方法
### loadData()
- 异步加载排序规则数据
- 通过 `OrderingRulesApi.get()` 获取规则
- 筛选并存储检验类型（RuleType.检验）的规则

### sortInspections(inspectionsMap)
- 输入：`InspectionsForPrint` 类型的 Map 对象
- 根据已加载的排序规则对检验项目进行排序
- 如果没有排序规则，返回原始数据
- 排序逻辑：
  - 创建排序顺序查找表
  - 使用 itemCode 作为键，sort 值作为排序依据
  - 未定义排序值的项目排在最后

## 状态数据
- `storeInspectionSort`: 存储当前的排序规则（SortRule 类型）

## 依赖关系
- @vueuse/core
- OrderingRulesApi
- RuleType, SortRule 类型定义
- InspectionsForPrint 类型

## 使用示例

``` typescript
const store = useStoreInspectionSort();
// 加载排序规则
await store.loadData();
// 对检验项目进行排序
const sortedInspections = store.sortInspections(inspectionsMap);
```
## 注意事项
1. 使用前需要先调用 loadData() 加载排序规则
2. 排序规则为空时不会改变原有顺序
3. 未在规则中定义的项目会被排在最后