import { describe, test, expect, beforeEach, vi } from "vitest";
import { useStoreInspectionSort } from "./index";
import { RuleType, SortRuleDetail } from "@/types/reportSortRules";
import { OrderingRulesApi } from "@/apis/orderingRules";
 

// Mock OrderingRulesApi
vi.doMock("@/apis/orderingRules", () => {
  const mockData = {
    id: 1,
    ruleType: RuleType.检验,
    detail: [
      { itemCode: "item2", itemName: "项目2", sort: 1, isOld: true },
      { itemCode: "item1", itemName: "项目1", sort: 2, isOld: true },
      { itemCode: "item3", itemName: "项目3", sort: 3, isOld: true }
    ]
  };

  return {
    OrderingRulesApi: {
      get: vi.fn().mockResolvedValue([{
        id: 1,
        ruleType: 1,
        detail: [
          { itemCode: "item2", itemName: "项目2", sort: 1 },
          { itemCode: "item1", itemName: "项目1", sort: 2 },
          { itemCode: "item3", itemName: "项目3", sort: 3 }
        ]
      }]),
      getInspectionsRules: vi.fn().mockResolvedValue(mockData),
      saveInspectionsRules: vi.fn().mockResolvedValue({})
    }
  };
});

// Mock Vue
vi.mock("vue", () => {
  return {
    ref: vi.fn(x => ({
      value: x
    })),
    computed: vi.fn(getter => ({
      value: getter()
    })),
    watch: vi.fn((source, callback, options = {}) => {
      return () => {};
    })
  };
});

// Mock storeInspectionsForPrint
vi.mock("../storeInspectionsForPrint", () => ({
  useStoreInspectionsForPrint: vi.fn().mockReturnValue({
    storeInspectionsForPrint: { value: new Map() }
  })
}));
 
// 重置所有mock
beforeEach(() => {
  vi.clearAllMocks();
});

describe("useStoreInspectionSort", () => {
  
  const {storeInspectionSort, loadData,save,orderUp,orderDown,storeInspectionsForPrintToInspectionSort} = useStoreInspectionSort();
  
  describe.only("初始化", () => {
    test("应该正确初始化默认值", () => { 
      expect(storeInspectionSort.value).toBeNull();
      expect(loadData).toBeDefined();
      expect(save).toBeDefined();
      expect(orderUp).toBeDefined();
      expect(orderDown).toBeDefined();
    });

    test("loadData应该正确获取并设置数据", async () => {
      const { loadData, storeInspectionSort } = useStoreInspectionSort();
      await loadData();
      expect(OrderingRulesApi.getInspectionsRules).toHaveBeenCalled();
      // expect(storeInspectionSort.value).toEqual({
      //   id: 1,
      //   ruleType: RuleType.检验,
      //   detail: [
      //     { itemCode: "item2", itemName: "项目2", sort: 1, isOld: true },
      //     { itemCode: "item1", itemName: "项目1", sort: 2, isOld: true },
      //     { itemCode: "item3", itemName: "项目3", sort: 3, isOld: true }
      //   ]
      // });
    });
  });
})