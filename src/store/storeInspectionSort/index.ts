import { createGlobalState } from "@vueuse/core";
import { OrderingRulesApi } from "@/apis/orderingRules";
import { InspectionsSortRule, RuleType, SortRule, SortRuleDetail } from "@/types/reportSortRules";
import { InspectionsForPrint, useStoreInspectionsForPrint } from "../storeInspectionsForPrint";

export const useStoreInspectionSort = createGlobalState(() => {
  const storeInspectionSort = ref<SortRule | null>(null);
  const { storeInspectionsForPrint } = useStoreInspectionsForPrint();

  watch(
    storeInspectionsForPrint,
    () => {
      storeInspectionsForPrintToInspectionSort();
    },
    { deep: true }
  );

  async function loadData() {
    storeInspectionSort.value = await OrderingRulesApi.getInspectionsRules();
    storeInspectionsForPrintToInspectionSort();
  }

  async function save() {
    if (!storeInspectionSort.value?.id) return;
    await OrderingRulesApi.saveInspectionsRules(storeInspectionSort.value as InspectionsSortRule);
  }

  function orderUp(item: SortRuleDetail) {
    if (!storeInspectionSort.value) return;
    const detail = storeInspectionSort.value.detail;
    const index = detail.findIndex(d => d.itemCode === item.itemCode);
    if (index === -1 || index === 0) return;

    const prevSort = detail[index - 1].sort;
    const nextSort = index < detail.length - 1 ? detail[index + 1].sort : prevSort - 100;

    // 计算新的排序值：要比前一项大
    const newSort = prevSort + Math.max(1, Math.floor((prevSort - nextSort) / 2));
    detail[index].sort = newSort;

    // 重新排序数组：数字大的在前
    detail.sort((a, b) => b.sort - a.sort);
    storeInspectionsForPrintToInspectionSort();
  }

  function orderDown(item: SortRuleDetail) {
    if (!storeInspectionSort.value) return;
    const detail = storeInspectionSort.value.detail;
    const index = detail.findIndex(d => d.itemCode === item.itemCode);
    if (index === -1 || index === detail.length - 1) return;

    const nextSort = detail[index + 1].sort;
    const prevSort = index > 0 ? detail[index - 1].sort : nextSort + 100;

    // 计算新的排序值：要比下一项小
    const newSort = nextSort - Math.max(1, Math.floor((prevSort - nextSort) / 2));
    detail[index].sort = newSort;

    // 重新排序数组：数字大的在前
    detail.sort((a, b) => b.sort - a.sort);
    storeInspectionsForPrintToInspectionSort();
  }

  function storeInspectionsForPrintToInspectionSort() {
    if (!storeInspectionSort.value) return;
    const details = mapToSortRuleDetails(storeInspectionsForPrint.value);
    const mergedDetails = diffAndMergeRuleDetails(details);
    if (mergedDetails) {
      storeInspectionSort.value.detail = mergedDetails;
    }
  }
  function diffAndMergeRuleDetails(details: SortRuleDetail[]) {
    if (!storeInspectionSort.value) return;
    const detail = storeInspectionSort.value.detail;

    // 标记现有项为旧项
    const existingDetails = detail.map(item => ({
      ...item
    }));

    // 找出新项（不在现有规则中的项）
    const newDetails = details.filter(
      item => !existingDetails.some(d => d.itemCode === item.itemCode)
    );

    // 合并旧项和新项
    return [...existingDetails, ...newDetails];
  }

  function mapToSortRuleDetails(inspectionsForPrint: InspectionsForPrint): SortRuleDetail[] {
    if (inspectionsForPrint.size == 0) return [];
    return Array.from(inspectionsForPrint.entries()).map(([itemCode, innerMap]) => ({
      itemCode,
      itemName: Array.from(innerMap.values())[0][0]?.itemName ?? "",
      sort: 0,
      isOld: false // 新项目默认为 false
    }));
  }
  return {
    storeInspectionSort,
    loadData,
    save,
    orderUp,
    orderDown,
    diffAndMergeRuleDetails,
    storeInspectionsForPrintToInspectionSort
  };
});
