<script lang="ts">
import type { Menu } from "./components/Menu/Index.vue";
import type { RouteRecordRaw } from "vue-router";
</script>
<script lang="ts" setup>
import {
  LayoutMenu,
  LayoutTab,
  LayoutBreadcrumb,
  LayoutNotification,
  LayoutAccount
} from "./components";
import { Expand, Fold } from "@element-plus/icons-vue";
import { socket } from "@/utils/socket";
import { requestGetAccountMenus } from "@/apis/account";
import logo from "@/assets/logo_green.png";
import logo_circle from "@/assets/logo.png";
const router = useRouter();
const menuCollapse = ref(false);
const reload = ref(true);
const menus = ref<Menu[]>([
  {
    id: 0,
    name: "首页",
    url: "/home",
    path: "/Home.vue",
    icon: "HomeFilled",
    sort: 0,
    type: 2,
    children: []
  }
]);

const updateRoutes = (menus: Menu[]) => {
  const viewModules = import.meta.glob<any>("../views/**/*.vue", { eager: true });

  const mapRoutes = (menus: Menu[]): RouteRecordRaw[] => {
    return menus
      .filter(m => {
        switch (m.type) {
          case 1:
            return m.children.length > 0;
          case 2:
            return true;
          case 4:
            return true;
          default:
            return false;
        }
      })
      .map(menu => {
        switch (menu.type) {
          case 1:
            return {
              path: menu.routeName,
              name: menu.routeName,
              meta: { title: menu.name, type: menu.type },
              redirect: { name: menu.children[0].name },
              children: mapRoutes(menu.children),
              component: () => import("./Empty.vue")
            } as RouteRecordRaw;
          case 2:
            return {
              path: menu.url,
              name: menu.routeName,
              meta: { title: menu.name, type: menu.type },
              component: viewModules[`../views${menu.path}`]?.default,
              children: mapRoutes(menu.children)
            } as RouteRecordRaw;
          case 4:
            return {
              path: menu.url,
              name: menu.routeName,
              meta: { title: menu.name, type: menu.type },
              component: viewModules[`../views${menu.path}`]?.default
            } as RouteRecordRaw;
          default:
            throw new Error("");
        }
      });
  };

  // console.log("menus", menus);

  const routes = mapRoutes(menus);
  // console.log("routes", routes);

  for (const item of routes) {
    router.addRoute("layout", item);
  }
  console.log(router.currentRoute.value.fullPath)
  router.replace(router.currentRoute.value.fullPath);
};
const onTabRefresh = () => {
  reload.value = false;
  nextTick(() => {
    reload.value = true;
  });
};

onMounted(() => {
  const initMenuPromise = requestGetAccountMenus().then(res => {
    menus.value = [...menus.value, ...res.data];
    console.log("menus.value", menus.value);

    updateRoutes(menus.value);
  });

  const initSocketPromise = socket.start();
  Promise.all([initMenuPromise, initSocketPromise]).then(() => (reload.value = true));
});
</script>
<template>
  <div class="layout-page" :class="{ 'layout-page-menu-collapse': menuCollapse }">
    <div class="side">
      <!-- <span class="title">VIP体检系统</span> -->
      <div style="padding: 0 5px">
        <el-image style="height: 100%" :src="logo" fit="contain" v-if="!menuCollapse" />
        <el-avatar shape="circle" :size="50" fit="fill" :src="logo_circle" v-if="menuCollapse" />
      </div>

      <el-scrollbar height="100%">
        <layout-menu class="menu" :menu-collapse="menuCollapse" :menus="menus" />
      </el-scrollbar>
    </div>
    <div class="container">
      <div class="header">
        <el-icon class="collapse-icon" @click="menuCollapse = !menuCollapse" size="30">
          <expand v-if="menuCollapse" />
          <fold v-else />
        </el-icon>
        <layout-breadcrumb class="navs" />
        <layout-notification />
        <el-divider direction="vertical" />
        <layout-account />
      </div>
      <layout-tab class="tabs" @refresh="onTabRefresh" />
      <div class="main">
        <router-view v-if="reload" class="main__view" #default="{ Component }">
          <component class="tab_page" :is="Component" />
        </router-view>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.layout-page {
  height: 100%;
  display: grid;
  grid-template-columns: 225px auto;
  .side {
    height: 100%;
    display: grid;
    grid-template-rows: 50px auto;
    overflow: hidden;
    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
      font-weight: 600;
    }
    .menu {
      overflow-y: auto;
    }
  }
  .container {
    display: grid;
    grid-template-rows: 50px 30px auto;
    overflow: hidden;
    .header {
      align-items: center;
      display: flex;
      margin-right: 20px;
      .collapse-icon {
        cursor: pointer;
      }
      .navs {
        flex: 1;
        margin: 0 20px;
      }
    }
    .tabs {
      margin-left: 1px;
    }
    .main {
      padding: 20px;
      background: #f5f5f5;
      overflow: hidden;
      position: relative;
      &__view {
        background: white;
        padding: 20px;
        box-sizing: border-box;
      }
      .tab_page {
        position: absolute;
        top: 20px;
        left: 20px;
        right: 20px;
        bottom: 20px;
      }
    }
  }
}
.layout-page-menu-collapse {
  grid-template-columns: 60px auto;
}

.v-leave-to {
  opacity: 0;
  // transform: scale(0.8);
  transform: translateX(-30px);
}
.v-enter-from {
  opacity: 0;
  // transform: scale(0.8);
  transform: translateX(-30px);
}
.v-enter-to,
.v-leave-from {
  opacity: 1;
  transform: translateX(0);
  // transform: scale(1);
}
.v-enter-active,
.v-leave-active {
  transition: all 0.3s ease;
}
</style>
