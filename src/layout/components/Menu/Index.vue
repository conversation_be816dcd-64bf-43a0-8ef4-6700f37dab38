<script lang="ts">
export interface Menu {
  id: number;
  name: string;
  routeName?: string;
  url?: string;
  path?: string;
  type: number;
  icon?: string;
  sort?: number;
  children: <PERSON>u[];
}
</script>
<script lang="ts" setup>
import MenuTree from "./MenuTree.vue";

defineProps<{
  menuCollapse: boolean;
  menus: Menu[];
}>();

const route = useRoute();

const activePath = computed(() => route.path);
</script>
<template>
  <el-menu
    class="menu"
    :collapse="menuCollapse"
    :collapse-transition="false"
    :router="true"
    :default-active="activePath"
  >
    <menu-tree :menus="menus"></menu-tree>
  </el-menu>
</template>
<style lang="less" scoped>
.menu {
  border-right: 0;
}
</style>
