<script lang="ts">
import type { Menu } from "./Index.vue";
import * as Icons from "@element-plus/icons-vue";
</script>

<script lang="ts" setup>
import { toRefs } from "vue";
const props = defineProps<{
  menus: Menu[];
}>();

const { menus } = toRefs(props);
</script>

<template>
  <template v-for="menu in menus">
    <el-sub-menu v-if="menu.type == 1" :index="menu.id.toString()">
      <template #title>
        <el-icon><component :is="Icons[menu.icon as keyof typeof Icons]" /></el-icon>
        <span>{{ menu.name }}</span>
      </template>
      <menu-tree v-if="menu.children.length > 0" :menus="menu.children"></menu-tree>
    </el-sub-menu>
    <el-menu-item v-if="menu.type == 2" :index="menu.url"
      ><el-icon><component :is="Icons[menu.icon as keyof typeof Icons]" /></el-icon>
      <template #title>
        <span>{{ menu.name }}</span>
      </template>
    </el-menu-item>
  </template>
</template>
