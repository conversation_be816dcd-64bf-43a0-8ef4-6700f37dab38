<script lang="ts">
export interface Tab {
  id: string;
  name: string;
  path: string;
  closed: boolean;
}
</script>

<script lang="ts" setup>
import { ArrowDown, RefreshRight } from "@element-plus/icons-vue";
import TabDropdown from "./TabDropdown.vue";
import { ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";

const HOME_TAB = {
  id: "/home",
  name: "首页",
  path: "/home",
  closed: false
};

const emit = defineEmits<{
  (e: "refresh"): void;
}>();

const router = useRouter();
const route = useRoute();

const activeTabId = ref(HOME_TAB.id);
const tabs = ref<Tab[]>([HOME_TAB]);

const dropdownItemDisabled = computed(() => {
  return (tab: Tab) => {
    return [
      false,
      tab.id == HOME_TAB.id,
      (tab.id == HOME_TAB.id && tabs.value.length <= 1) || tabs.value.length <= 2,
      (tab.id == HOME_TAB.id && tabs.value.length <= 1) || tabs.value.length <= 1
    ] as [boolean, boolean, boolean, boolean];
  };
});
const activeTab = computed(() => {
  return tabs.value.find(item => item.id == activeTabId.value) || HOME_TAB;
});

watch(
  route,
  () => {
    const routeName = route.name as string;
    const path = route.fullPath;
    if (routeName == "404") {
      activeTabId.value = "";
      return;
    }
    if (!tabs.value.some(item => item.path == path)) {
      const name = (route.query.pageName ?? route.meta.title ?? "未命名") as string;
      tabs.value.push({
        id: path,
        name,
        path,
        closed: true
      });
    }
    activeTabId.value = path;
  },
  {
    immediate: true
  }
);

const closeTab = (tab: Tab) => {
  const id = tab.id;
  if (id == activeTabId.value) {
    tabs.value.forEach((item, index) => {
      if (item.id === id) {
        const nextTab = tabs.value[index + 1] || tabs.value[index - 1];
        if (nextTab) {
          activeTabId.value = nextTab.id;
          router.push(nextTab.path);
        }
      }
    });
  }
  const index = tabs.value.findIndex(item => item.id == id);
  if (index != -1) {
    tabs.value.splice(index, 1);
  }
};
const closeOtherTab = (tab: Tab) => {
  tabs.value = tabs.value.filter(item => [tab.id, HOME_TAB.id].includes(item.id));
  router.push(tab.path);
};
const closeAllTab = () => {
  tabs.value = tabs.value.filter(item => item.id == HOME_TAB.id);
  router.push({
    name: "首页"
  });
};

const onTabClick = (tab: any) => {
  const path: string = tab.props.name;
  router.push(path);
};
const onTabRemove = (id: any) => {
  const tab = tabs.value.find(item => item.id == id);
  if (tab) closeTab(tab);
};
const onTabDropdownItemClick = (tab: Tab, command: string) => {
  switch (command) {
    case "refresh":
      emit("refresh");
      break;
    case "close":
      closeTab(tab);
      break;
    case "closeOther":
      closeOtherTab(tab);
      break;
    default:
      closeAllTab();
      break;
  }
};
</script>
<template>
  <div class="tab-wrap">
    <el-tabs
      v-model="activeTabId"
      type="card"
      class="tabs"
      @tab-click="onTabClick"
      @tab-remove="onTabRemove"
    >
      <el-tab-pane v-for="tab in tabs" :label="tab.name" :closable="tab.closed" :name="tab.path">
        <template #label>
          <tab-dropdown
            trigger="contextmenu"
            :dropdown-item-disabled="dropdownItemDisabled(tab)"
            @item-click="onTabDropdownItemClick(tab, $event)"
            style="line-height: 2"
          >
            <span :class="{ 'tab-item-active': tab.path == activeTabId }">{{ tab.name }}</span>
          </tab-dropdown>
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-icon @click="emit('refresh')"><refresh-right /></el-icon>
    <tab-dropdown
      trigger="click"
      :dropdown-item-disabled="dropdownItemDisabled(activeTab)"
      @item-click="onTabDropdownItemClick(activeTab, $event)"
    >
      <el-icon><arrow-down /></el-icon>
    </tab-dropdown>
  </div>
</template>
<style lang="less" scoped>
.tab-wrap {
  display: grid;
  grid-template-columns: auto 35px 35px;
  grid-auto-rows: 30px;
  align-items: center;
  .tabs {
    overflow: hidden;
    :deep(.el-tabs__item) {
      height: 30px;
      line-height: 2;
      // line-height: 30px;
    }
    :deep(.el-tabs__header) {
      height: 30px;
      margin: 0;
    }
    :deep(.el-tabs__nav-wrap.is-scrollable) {
      padding: 0 30px;
    }
    :deep(.el-tabs__nav-prev),
    :deep(.el-tabs__nav-next) {
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      .el-icon {
        font-size: 20px;
      }
    }
    .tab-item-active {
      color: var(--el-color-primary);
    }
  }
}
</style>
