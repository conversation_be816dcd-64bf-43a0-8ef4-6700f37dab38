<script lang="ts" setup>
import { RefreshRight, Close, SemiSelect, CloseBold } from "@element-plus/icons-vue";
import { computed, toRefs } from "vue";

const props = defineProps<{
  trigger: "click" | "contextmenu";
  dropdownItemDisabled: [boolean, boolean, boolean, boolean];
}>();

const { dropdownItemDisabled } = toRefs(props);
const emit = defineEmits<{
  (e: "item-click", command: string): void;
}>();

const dropdownItems = computed(() => {
  return [
    {
      label: "重新加载",
      icon: RefreshRight,
      disabled: dropdownItemDisabled.value[0],
      command: "refresh"
    },
    {
      label: "关闭标签页",
      icon: Close,
      disabled: dropdownItemDisabled.value[1],
      command: "close"
    },
    {
      label: "关闭其他标签页",
      icon: SemiSelect,
      disabled: dropdownItemDisabled.value[2],
      command: "closeOther"
    },
    {
      label: "关闭全部标签页",
      icon: CloseBold,
      disabled: dropdownItemDisabled.value[3],
      command: "closeAll"
    }
  ];
});

const onDropdownCommand = (command: string) => {
  emit("item-click", command);
};
</script>
<template>
  <el-dropdown :trigger="trigger" @command="onDropdownCommand">
    <slot></slot>
    <template #dropdown>
      <el-dropdown-menu style="width: 150px">
        <el-dropdown-item
          v-for="item in dropdownItems"
          :icon="item.icon"
          :disabled="item.disabled"
          :command="item.command"
          >{{ item.label }}</el-dropdown-item
        >
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
