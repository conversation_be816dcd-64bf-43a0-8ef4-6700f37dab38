<script lang="ts" setup>
import { computed } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

const navs = computed(() => {
  const data = route.matched
    .filter(item => item.name != "layout" && item.name != "home")
    .map(item => {
      const name = item.meta.title ?? "未命名";
      if (item.name == route.name) {
        return {
          name: route.query.pageName ?? name,
          path: undefined
        };
      } else {
        return {
          name,
          path: item.meta.type == 1 ? undefined : item.path
        };
      }
    });
  data.unshift({
    path: "/",
    name: "首页"
  });
  return data;
});
</script>
<template>
  <el-breadcrumb class="navs" separator="/">
    <el-breadcrumb-item v-for="nav in navs" :to="nav.path">{{ nav.name }}</el-breadcrumb-item>
  </el-breadcrumb>
</template>
