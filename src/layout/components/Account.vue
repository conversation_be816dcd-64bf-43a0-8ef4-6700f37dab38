<script lang="ts">
export interface User {
  id: number;
  userName: string;
  name: string;
  headPortrait: string;
  roleId: number;
  isAdmin: boolean;
}
</script>
<script lang="ts" setup>
import { onMounted, reactive } from "vue";
import { requestGetAccountOfDoctor } from "@/apis/account";
import token from "@/utils/token";
import { useRouter } from "vue-router";
import { ArrowDown } from "@element-plus/icons-vue";
import { useStore } from "@/store";

const router = useRouter();

const store = useStore();

const user = reactive<User>({
  id: 0,
  userName: "",
  name: "",
  headPortrait: "",
  roleId: 0,
  isAdmin: false
});

const onLogoutClick = () => {
  token.clear();
  router.push({ name: "login" });
};

onMounted(() => {
  requestGetAccountOfDoctor().then(res => {
    const { userName, name, headPortrait, roleId, isAdmin, id } = res.data;
    user.id = id;
    user.userName = userName;
    user.name = name;
    user.headPortrait = headPortrait;
    user.roleId = roleId;
    user.isAdmin = isAdmin;
    store.commit("setDoctor", user);
  });
});
</script>
<template>
  <el-dropdown>
    <span class="account">
      <span class="name">{{ user.name }}</span>
      <el-icon>
        <arrow-down />
      </el-icon>
    </span>
    <template #dropdown>
      <el-dropdown-menu style="width: 100px">
        <el-dropdown-item @click="onLogoutClick">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>
<style lang="less" scoped>
.account {
  display: flex;
  .name {
    text-align: right;
    flex: 1;
    font-size: 16px;
    font-weight: 700;
    margin-right: 10px;
  }
}
</style>
