<script lang="ts" setup>
import { Bell } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { useStore } from "@/store";
import { computed, onMounted, onUnmounted } from "vue";
import { requestGetNotifications } from "@/apis/account";
import { socket } from "@/utils/socket";
import { ElNotification } from "element-plus";
import { Plus } from "@element-plus/icons-vue";

const router = useRouter();
const store = useStore();

const notifications = computed(() => store.state.notifications.slice(0, 4));
const isNotification = computed(() => store.state.notifications.length > 0);
const notReadNotificationCount = computed(() => {
  return store.state.notifications.filter(item => !item.readed).length;
});

const goNotificationPage = () => {
  router.push("/notification");
};
const listenNotificationCallback = (data: any) => {
  store.commit("addNotification", data);
  ElNotification({
    title: data.title,
    message: data.content,
    type: "warning",
    duration: 0
  });
};

onMounted(() => {
  requestGetNotifications().then(res => {
    store.commit("setNotifications", res.data);
  });
  socket.on("notification", listenNotificationCallback);
});
onUnmounted(() => {
  socket.off("notification", listenNotificationCallback);
});
</script>
<template>
  <el-popover placement="bottom" :width="200" trigger="hover">
    <el-badge
      class="notification"
      v-for="notification in notifications"
      is-dot
      :hidden="notification.readed"
    >
      <div @click="goNotificationPage">
        <span class="notification__title">{{ notification.title }}</span>
        <span class="notification__time">{{ notification.createTime }}</span>
      </div>
    </el-badge>
    <el-badge
      class="more"
      v-if="isNotification"
      :value="notReadNotificationCount"
      :hidden="notReadNotificationCount <= 0"
    >
      <el-button class="more__btn" @click="goNotificationPage">查看更多</el-button>
    </el-badge>
    <span v-else class="not-notification-text">暂无更多消息</span>
    <template #reference>
      <el-badge is-dot :hidden="notReadNotificationCount <= 0">
        <el-icon><bell style="cursor: pointer" /></el-icon>
      </el-badge>
    </template>
  </el-popover>
</template>
<style lang="less" scoped>
.notification {
  margin: 5px 0;
  cursor: pointer;
  &:hover {
    background: var(--el-bg-color);
  }
  div:first-child {
    display: grid;
    grid-template-rows: 1fr 1fr;
    padding: 10px;
  }
  &__title {
    font-weight: 600;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  &__time {
    font-size: 12px;
  }
}
.more {
  width: 100%;
  &__btn {
    width: 100%;
  }
}
.not-notification-text {
  display: inline-block;
  width: 100%;
  text-align: center;
}
</style>
