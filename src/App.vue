<script setup lang="ts">
// import zhCn from "element-plus/lib/locale/lang/zh-cn";
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import refreshTokenService from "@/hostBackgroundService/refreshTokenService";
import token from "@/utils/token";
import { sleep } from "@/utils/tools";

// import { useWindowSize, useEventListener } from "@vueuse/core";

const loading = ref<boolean>(true);

// const { width: windowWidth } = useWindowSize();

// 由于项目已统一使用 1rem = 10px 的方案（通过 CSS 设置 html font-size: 64.5%）
// 以下代码暂时注释保留，不再使用 1rem = 100px 的动态计算方案
/*
const remSize = () => {
  const deviceWidth = Math.min(windowWidth.value, 1920)
  document.documentElement.style.fontSize = `${deviceWidth / 19.2}px`
}

// 使用watch来监听窗口宽度变化
watch(windowWidth, () => {
  remSize()
})
*/

onMounted(async () => {
  // remSize();
  refreshTokenService.start();

  const currentNowTime = new Date().valueOf();

  while (new Date().valueOf() - currentNowTime < 5000) {
    if (!token.expired) break;
    if (!token.hasRefreshToken) break;
    await sleep(1000);
  }
  loading.value = false;
});
</script>

<template>
  <el-config-provider :locale="zhCn">
    <div style="width: 100%; height: 100%" v-loading="loading" v-if="loading"></div>
    <router-view v-if="!loading"></router-view>
  </el-config-provider>
</template>

<style lang="less">
#app {
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    "Open Sans",
    "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}
ul,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}
.custom-dialog {
  height: 75%;
  display: flex;
  flex-direction: column;
}

.custom-dialog > .el-dialog__body {
  flex: 1;
  overflow: hidden;
  display: flex;
  padding-bottom: 1rem;
}

.custom-dialog > .el-dialog__body > * {
  height: 100%;
  width: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  border-radius: 10px;
  background-color: #f5f5f5;
}
//滑块
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #ccc;
  //  background-image: #1F2430;
}
//外层轨道
::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: #f5f5f5;
}
//边角
::-webkit-scrollbar-corner {
  background-color: #f5f5f5;
}
.el-scrollbar {
  .span-th-td {
    display: inline-block;
    width: 150px;
    overflow: hidden;
    .color-i {
      display: inline-block;
      width: 100px;
      height: 20px;
      vertical-align: middle;
    }
  }
}
</style>
