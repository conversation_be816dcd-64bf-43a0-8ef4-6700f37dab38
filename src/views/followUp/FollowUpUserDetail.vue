<script lang="ts">
import type { ElForm, ElTable } from "element-plus";

export interface FollowUpTaskFormItem {
  name: string;
  content: string;
  isNotifyDoctor: boolean;
  executionTime: Date;
}
</script>
<script lang="ts" setup>
import { ref, reactive, toRefs } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import useMessageTemplates from "@/composables/useMessageTemplates";
import useUserMessageContent from "@/composables/useUserMessageContent";
import useUserFollowUpTasks from "@/composables/useUserFollowUpTasks";
import useFollowUpUser from "@/composables/useFollowUpUser";
import { socket } from "@/utils/socket";
import moment from "moment";
const TASK_STATUS_OPTIONS: {
  [key: number]: {
    label: string;
    color: string;
    tagType: "warning" | "success" | "danger" | "info" | "primary";
  };
} = {
  1: {
    label: "待审核",
    color: "#E6A23C",
    tagType: "warning"
  },
  2: {
    label: "已执行",
    color: "#67C23A",
    tagType: "success"
  },
  3: {
    label: "失败",
    color: "#F56C6C",
    tagType: "danger"
  },
  // 4: {
  //   label: "停止",
  //   color: "#909399",
  //   tagType: "info"
  // },
  5: {
    label: "已完成",
    color: "#409eff",
    tagType: "primary"
  },
  6: {
    label: "已审核",
    color: "#E6A23C",
    tagType: "primary"
  }
};
const TASK_FORM_RULES = {
  name: [{ required: true, message: "请输入标题", trigger: "blur" }],
  content: [{ required: true, message: "请输入内容", trigger: "blur" }]
};

const taskFormRefs = ref<InstanceType<typeof ElForm>[]>([]);
const messageTemplateTableRef = ref<InstanceType<typeof ElTable>>();
const messageTemplateFormRef = ref<InstanceType<typeof ElForm>>();
const dialogVisible = ref(false);

const route = useRoute();
const { followUpUser } = useFollowUpUser(ref(parseInt(route.params["id"] as string)));

const followUpTaskFormModels = ref<FollowUpTaskFormItem[]>([]);
const currentTaskFormIndex = ref(0);
const { followUpTasks, nextFollowUpTasks, addUserFollowUpTasks } =
  useUserFollowUpTasks(followUpUser);

//---messageTemplate---
const messageTemplateModel = reactive({
  title: "",
  content: ""
});
const { messageTemplates } = useMessageTemplates();
const { messageParams, messageContent } = useUserMessageContent(
  followUpUser,
  toRefs(messageTemplateModel).content
);

const onTaskFormAddClick = () => {
  followUpTaskFormModels.value.push({
    name: "",
    content: "",
    isNotifyDoctor: true,
    executionTime: new Date()
  });
};

const onTaskFormRemoveClick = (index: number) => {
  followUpTaskFormModels.value.splice(index, 1);
};

const onTaskFormMessageTemplateSelectClick = (index: number) => {
  currentTaskFormIndex.value = index;
  dialogVisible.value = true;
};

const onTaskFormSendClick = () => {
  const validatePromises = taskFormRefs.value.map(item => item.validate());
  Promise.all(validatePromises).then(() =>
    addUserFollowUpTasks(followUpTaskFormModels.value).then(
      () => (followUpTaskFormModels.value = [])
    )
  );
};

const onMessageTemplateTableSelectClick = (
  messageTemplate: (typeof messageTemplates.value)[number]
) => {
  messageTemplateModel.title = messageTemplate.name;
  messageTemplateModel.content = messageTemplate.content;
};

const onDialogConfirmClick = () => {
  messageTemplateFormRef.value?.validate()?.then(() => {
    const taskFormModel = followUpTaskFormModels.value[currentTaskFormIndex.value];
    taskFormModel.content = messageContent.value;
    taskFormModel.name = messageTemplateModel.title;
    dialogVisible.value = false;
  });
};

const listenFollowUpTaskCallback = (task: { id: number; status: number }) => {
  const index = followUpTasks.value.findIndex(t => t.id == task.id);
  if (index != -1) followUpTasks.value[index].status = task.status;
};

onMounted(() => {
  socket.on("followUpTask", listenFollowUpTaskCallback);
});

onUnmounted(() => {
  socket.off("followUpTask", listenFollowUpTaskCallback);
});
</script>
<template>
  <div class="followup-user-detail-page">
    <span>任务列表</span>
    <el-scrollbar class="task-list" v-infinite-scroll="nextFollowUpTasks">
      <el-timeline>
        <el-timeline-item
          v-for="task in followUpTasks"
          :timestamp="moment(task.createTime).format('YYYY-MM-DD HH:mm:ss')"
          placement="top"
          :color="TASK_STATUS_OPTIONS[task.status].color"
        >
          <el-card shadow="hover" class="task-wrap">
            <span class="label">标题：</span>
            <span class="content">{{ task.name }}</span>
            <span class="label">内容：</span>
            <span>
              <el-popover placement="right" :width="300" trigger="click" :content="task.content">
                <template #reference>
                  <el-button type="text">点击查看</el-button>
                </template>
              </el-popover>
            </span>
            <span class="label" v-if="task.summary">总结：</span>
            <span v-if="task.summary">
              <el-popover placement="right" :width="300" trigger="click" :content="task.summary">
                <template #reference>
                  <el-button type="text">点击查看</el-button>
                </template>
              </el-popover>
            </span>
            <span class="label">状态：</span>
            <span>
              <el-tag :type="TASK_STATUS_OPTIONS[task.status].tagType">
                {{ TASK_STATUS_OPTIONS[task.status].label }}
              </el-tag>
            </span>
            <span class="label">是否通知医生：</span>
            <span>{{ task.isNotifyDoctor ? "是" : "否" }}</span>
            <span class="label">执行时间：</span>
            <span>{{ task.executionTime }}</span>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-scrollbar>
    <el-divider direction="vertical" class="divider" />
    <span>添加任务</span>
    <el-scrollbar class="form-wrap">
      <el-card class="form-card" shadow="hover" v-for="(task, index) in followUpTaskFormModels">
        <template #header>
          <div class="card-header">
            <span>随访任务{{ index + 1 }}</span>
            <span>
              <el-button size="small" @click="onTaskFormMessageTemplateSelectClick(index)">
                选择模板
              </el-button>
              <el-button type="danger" size="small" @click="onTaskFormRemoveClick(index)">
                删除
              </el-button>
            </span>
          </div>
        </template>
        <el-form
          class="task-form"
          :model="task"
          label-width="100px"
          :rules="TASK_FORM_RULES"
          :ref="
            (el: any) => {
              if (el) taskFormRefs[index] = el;
            }
          "
        >
          <el-form-item label="标题" prop="name">
            <el-input v-model="task.name" />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input
              v-model="task.content"
              :autosize="{ minRows: 3, maxRows: 5 }"
              type="textarea"
              resize="none"
              maxlength="300"
            />
          </el-form-item>
          <el-form-item label="是否通知医生" prop="isNotifyDoctor">
            <el-switch
              v-model="task.isNotifyDoctor"
              inline-prompt
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
          <el-form-item label="时间" prop="executionTime">
            <el-date-picker v-model="task.executionTime" type="datetime" :clearable="false" />
          </el-form-item>
        </el-form>
      </el-card>
      <el-button class="add-task-btn" :icon="Plus" @click="onTaskFormAddClick"></el-button>
      <el-button
        class="send-task-btn"
        type="primary"
        :disabled="followUpTaskFormModels.length <= 0"
        @click="onTaskFormSendClick"
      >
        发送
      </el-button>
    </el-scrollbar>
    <el-dialog v-model="dialogVisible" title="选择模板" width="70%">
      <div class="select-meesage-tempalte-wrap">
        <el-table ref="messageTemplateTableRef" :data="messageTemplates" height="100%">
          <el-table-column type="expand">
            <template #default="props">
              <p>{{ props.row.content }}</p>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="标题" />
          <el-table-column label="操作" width="80px">
            <template #default="scope">
              <el-button type="text" @click="onMessageTemplateTableSelectClick(scope.row)">
                选择
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="meesage-tempalte-form-wrap">
          <span class="title">
            当前模板：
            <b>{{ messageTemplateModel.title }}</b>
          </span>
          <el-input
            class="content"
            type="textarea"
            :rows="5"
            resize="none"
            v-model="messageContent"
          />
          <el-scrollbar>
            <el-form
              class="form"
              :model="{ params: messageParams }"
              ref="messageTemplateFormRef"
              label-width="150px"
            >
              <el-form-item
                v-for="(param, index) in messageParams"
                :prop="`params.${index}.value`"
                :label="`${param.label}：`"
                :rules="[
                  {
                    required: true,
                    message: '请输入数据',
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input v-model="param.value" />
              </el-form-item>
            </el-form>
          </el-scrollbar>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="onDialogConfirmClick"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.followup-user-detail-page {
  .model-page;
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: grid;
  grid-template-columns: 50% 1px auto;
  grid-template-rows: 20px auto;
  grid-gap: 20px;

  .divider {
    grid-row: 1 / 3;
    height: 100%;
    margin: 0;
  }
  .task-list {
    padding-right: 10px;
    grid-column: 1/ 2;
    grid-row: 2/ 3;
    .task-wrap {
      :deep(.el-card__body) {
        display: grid;
        grid-template-columns: 100px auto;
        align-items: center;
        grid-gap: 10px;
        .label {
          font-weight: 600;
          color: var(--el-text-color-secondary);
        }
      }
    }
  }
  .form-wrap {
    grid-column: 3 ~"/" 4;
    padding-right: 20px;
    .form-card {
      margin-bottom: 20px;
      .card-header {
        display: flex;
        justify-content: space-between;
      }
      :deep(.el-card__header) {
        padding: 10px;
      }
    }
    .add-task-btn {
      width: 100%;
      height: 100px;
      border-style: dashed;
      margin-bottom: 50px;
    }
    .send-task-btn {
      width: 100%;
      margin: 0;
      position: absolute;
      bottom: 0rem;
      left: 0;
    }
  }
  .select-meesage-tempalte-wrap {
    height: 380px;
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;
    .meesage-tempalte-form-wrap {
      display: grid;
      grid-template-rows: 20px 120px auto;
      grid-gap: 20px;
    }
  }
  .model-page {
    grid-row: 1/5;
  }
}
</style>
