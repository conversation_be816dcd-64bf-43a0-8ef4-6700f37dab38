<script lang="ts">
import type { MessageTemplateItem } from "@/composables/useMessageTemplates";
enum Operation {
  Create,
  Update
}
</script>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import { ElMessage, ElForm, ElMessageBox } from "element-plus";

import useMessageTemplates from "@/composables/useMessageTemplates";

const MESSAGE_TEMPLATE_FORM_RULES = {
  name: [
    {
      required: true,
      message: "请输入模板名称",
      trigger: "blur"
    }
  ],
  content: [
    {
      required: true,
      message: "请输入模板内容",
      trigger: "blur"
    }
  ]
};

const messageTemplateFormModel = reactive({
  id: 0,
  name: "",
  content: ""
});
const operation = ref(Operation.Create);
const dialogVisible = ref(false);
const {
  messageTemplates,
  pagination,
  total,
  nameSearchQuery,
  addMessageTemplate,
  editMessageTemplate,
  removeMessageTemplate
} = useMessageTemplates();
const formRef = ref<InstanceType<typeof ElForm>>();

const onResetClick = () => {
  nameSearchQuery.value = "";
};

const onAddTemplateClick = () => {
  messageTemplateFormModel.id = 0;
  messageTemplateFormModel.name = "";
  messageTemplateFormModel.content = "";
  operation.value = Operation.Create;
  dialogVisible.value = true;
};

const onEditTemplateClick = (template: MessageTemplateItem) => {
  operation.value = Operation.Update;
  messageTemplateFormModel.id = template.id;
  messageTemplateFormModel.name = template.name;
  messageTemplateFormModel.content = template.content;
  dialogVisible.value = true;
};

const onDeleteClick = (id: number) => {
  ElMessageBox.confirm("该操作将永久删除该条数据，是否继续？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    if (!id) {
      return;
    }

    removeMessageTemplate(id)
      .then(() => {
        ElMessage.success("已删除");
      })
      .catch(err => {
        console.error(err);
        ElMessage.error("删除失败");
      });
  });
};

const onMessageTemplateSubmit = () => {
  const promise =
    operation.value == Operation.Create
      ? addMessageTemplate(messageTemplateFormModel)
      : editMessageTemplate(messageTemplateFormModel);

  promise
    .then(() => {
      ElMessage.success("保存成功");
      dialogVisible.value = false;
    })
    .catch(err => {
      console.error(err);
      ElMessage.error("保存失败");
    });
};
</script>
<template>
  <div class="default-page message-template-page">
    <div class="header">
      <span class="right-10">模板名称</span>
      <el-input
        class="input right-10"
        v-model="nameSearchQuery"
        placeholder="请输入模板名称"
        clearable
      ></el-input>
      <el-button @click="onResetClick">重置</el-button>
    </div>
    <div class="operation">
      <el-button type="primary" @click="onAddTemplateClick">添加模板</el-button>
    </div>
    <el-table :data="messageTemplates" width="100%" height="100%">
      <el-table-column prop="id" label="Id" />
      <el-table-column prop="name" label="模板名称" />
      <el-table-column prop="content" label="模板内容">
        <template #default="scope">
          <div class="messageContent">
            {{ scope.row.content }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button type="primary" @click="onEditTemplateClick(scope.row)"> 编辑 </el-button>
          <el-button type="danger" @click="onDeleteClick(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      layout="prev, pager, next"
      :total="total"
      v-model:page-size="pagination.rows"
      v-model:current-page="pagination.page"
      @current-change=""
    ></el-pagination>
    <el-dialog
      v-model="dialogVisible"
      width="30%"
      :title="operation == Operation.Create ? '添加模板' : '编辑模板'"
    >
      <el-form
        ref="formRef"
        :model="messageTemplateFormModel"
        :rules="MESSAGE_TEMPLATE_FORM_RULES"
        label-width="80px"
      >
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="messageTemplateFormModel.name" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="模板内容" prop="content">
          <el-input
            v-model="messageTemplateFormModel.content"
            :autosize="{ minRows: 4, maxRows: 6 }"
            type="textarea"
            placeholder="请输入内容"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onMessageTemplateSubmit">{{
            operation == Operation.Create ? "添加模板" : "保存修改"
          }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.message-template-page {
  .header {
    .right-10 {
      margin-right: 10px;
    }
    .input {
      width: 200px;
    }
  }
  .messageContent {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
