<script lang="ts">
import type { FollowUpUserItem } from "@/composables/useFollowUpUsers";
enum Operation {
  create,
  update
}
</script>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import Page from "@/components/Page.vue";

import { useRouter } from "vue-router";
import { Search, RefreshRight } from "@element-plus/icons-vue";
import { ElMessage, ElTable, FormInstance } from "element-plus";
import useFollowUpUsers from "@/composables/useFollowUpUsers";
import { Doctor, useDoctors } from "@/composables/useDoctor";
import { requestGetTransferPatients, requestReturnVisitTime } from "@/apis/patients";

interface FollowupUser extends FollowUpUserItem {}

const tableRef = ref<InstanceType<typeof ElTable>>();
const fromRef = ref<FormInstance>();

const transferDialogVisible = ref(false);

const isTransferAll = ref(false);

const fromModel = ref({
  turnOutDoctor: { userName: "", name: "" },
  targetDoctor: { userName: "", name: "" }
});

const selectedTableRow = ref<FollowupUser[]>([]);

const followUpUserFilters = reactive({
  name: "",
  idCard: ""
});
const router = useRouter();
const { getFollowUpUsers, followUpUsers, pagination, filters, total } = useFollowUpUsers();
const { doctorData, getDoctors } = useDoctors();
const dialogVisible = ref(false);

const printTableHeader = [
  {
    field: "patientId",
    title: "病人号"
  },
  {
    field: "name",
    title: "姓名"
  },
  {
    field: "idCard",
    title: "身份证号"
  },
  {
    field: "sex",
    title: "性别"
  },
  {
    field: "age",
    title: "年龄"
  },
  {
    field: "phone",
    title: "手机号"
  },
  {
    field: "doctorName",
    title: "所属医生"
  },
  {
    field: "lastFollowUpTime",
    title: "最近任务"
  },
  {
    field: "nextFollowUpTime",
    title: "下次任务"
  },
  {
    field: "returnVisitTime",
    title: "复诊时间"
  }
];
const transferDoctorOptions = computed(() => {
  return doctorData.value.filter(
    doctor =>
      ![fromModel.value.targetDoctor.userName, fromModel.value.turnOutDoctor.userName].includes(
        doctor.userName
      )
  );
});
const resetFromModel = () => {
  fromModel.value.targetDoctor = { userName: "", name: "" };
  fromModel.value.turnOutDoctor = { userName: "", name: "" };
};

const onFollowUpUserTableTaskClick = (followUpUser: FollowUpUserItem) => {
  router.push({
    name: "followUpUserDetail",
    params: {
      id: followUpUser.id
    },
    query: {
      pageName: `${followUpUser.name}随访用户详情`
    }
  });
};

const onSearchClick = () => {
  filters.name = followUpUserFilters.name;
  filters.idCard = followUpUserFilters.idCard;
};

const onResetFiltersClick = () => {
  followUpUserFilters.name = "";
  followUpUserFilters.idCard = "";
  onSearchClick();
};

//点病人转移按钮
const onTransferPatientClick = () => {
  if (selectedTableRow.value.length == 0) {
    ElMessage.warning("请选择要转移的病人");
    return;
  }
  isTransferAll.value = false;

  transferDialogVisible.value = true;
};
const onTransferPatientAccordingToClick = () => {
  isTransferAll.value = true;
  transferDialogVisible.value = true;
};

//转移弹窗-确认转移
const onTransferDialogConfirm = () => {
  fromRef.value?.validate().then(() => {
    const req = {
      isTransferAll: isTransferAll.value,
      targetDoctorNo: fromModel.value.targetDoctor.userName,
      fromDoctorNo: "",
      patientIds: [] as number[]
    };
    if (isTransferAll.value) {
      //全转移
      req.fromDoctorNo = fromModel.value.turnOutDoctor.userName;
    } else {
      //选中转移
      req.patientIds = selectedTableRow.value.map(p => p.id);
    }

    requestGetTransferPatients(req).then(() => {
      ElMessage.success("病人转移已完成");
      getFollowUpUsers().then(() => {
        tableRef.value?.clearSelection();
        transferDialogVisible.value = false;
      });
    });
  });
};

const onPrintClick = () => {
  dialogVisible.value = true;
};
//表格勾选或取消勾选
const onTableSelectionChange = (v: FollowupUser[]) => {
  selectedTableRow.value = v;
};
const onTransferDialogClose = () => {
  resetFromModel();
};
const onTurnOutDoctorClick = (val: any) => {
  fromModel.value.turnOutDoctor = JSON.parse(JSON.stringify(val));
};
const onTargetDoctorClick = (val: any) => {
  fromModel.value.targetDoctor = JSON.parse(JSON.stringify(val));
};
const onTurnOutDoctorClear = () => {
  fromModel.value.turnOutDoctor = { userName: "", name: "" };
};
const onTargetDoctorClear = () => {
  fromModel.value.targetDoctor = { userName: "", name: "" };
};

const defaultTime = new Date();
const onSelectTimeClick = (task: any) => {
  return requestReturnVisitTime(task.id, task.returnVisitTime).then(res => {
    ElMessage.success("下次复诊时间已更新");
  });
};

onMounted(() => {
  getDoctors();
});
</script>

<template>
  <page class="follow-up-user" child-route-name="followUpUserDetail">
    <div class="search-wrapper">
      <div class="search-item">
        <div class="content">
          <el-input
            v-model="followUpUserFilters.name"
            placeholder="可用: 姓名、身份证、手机号、医生、医生号"
          />
        </div>
      </div>
      <div class="btn-group">
        <el-button @click="onSearchClick" type="primary" :icon="Search">搜索</el-button>
        <el-button @click="onResetFiltersClick" :icon="RefreshRight">重置</el-button>
      </div>
    </div>
    <div class="tools-wrapper">
      <!-- <template v-if="doctor">

      </template> -->
      <el-button
        type="primary"
        @click="onTransferPatientClick"
        :disabled="selectedTableRow.length == 0"
      >
        选中转移
      </el-button>
      <el-button type="primary" @click="onTransferPatientAccordingToClick"> 全部转移 </el-button>
      <el-button @click="onPrintClick" :disabled="selectedTableRow.length == 0">打印预览</el-button>
    </div>
    <el-table
      :data="followUpUsers"
      @selection-change="onTableSelectionChange"
      ref="tableRef"
      stripe
      style="width: 100%"
      height="100%"
      row-key="id"
    >
      <el-table-column type="selection" width="55" :reserve-selection="true" />
      <!-- <el-table-column prop="patientId" label="病人号" /> -->
      <el-table-column prop="patientIdToStr" label="病人号" width="100" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="name" label="姓名" />
      <!-- <el-table-column prop="idCard" label="身份证号" /> -->
      <el-table-column prop="sex" label="性别" width="100" />
      <el-table-column prop="age" label="年龄" width="100" />
      <el-table-column prop="phone" label="手机号" />
      <el-table-column prop="doctorName" label="所属医生" />
      <el-table-column prop="lastFollowUpTime" label="最近任务" width="160" />

      <el-table-column prop="nextFollowUpTime" label="下次任务" width="160" />
      <el-table-column prop="returnVisitTime" label="复诊时间" width="240">
        <template #default="scope">
          <div class="task-content">
            <el-date-picker
              v-model="scope.row.returnVisitTime"
              type="datetime"
              placeholder="复诊时间"
              :default-time="defaultTime"
              @change="onSelectTimeClick(scope.row)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100px">
        <template #default="append">
          <el-button type="primary" plain @click="onFollowUpUserTableTaskClick(append.row)">
            任务
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next,sizes"
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.rows"
      :total="total"
      class="pagination"
    >
    </el-pagination>
    <el-dialog
      v-model="transferDialogVisible"
      :title="isTransferAll ? '转移某医生全部病人' : '转移选中的病人'"
      @close="onTransferDialogClose"
      :destroy-on-close="true"
    >
      <el-form ref="fromRef" :model="fromModel">
        <template v-if="isTransferAll">
          <el-form-item
            label="转出病人的医生"
            prop="turnOutDoctor.userName"
            :rules="{
              required: true,
              trigger: 'change',
              message: '请选择转出医生'
            }"
            :validate-on-rule-change="false"
          >
            <el-select
              v-model="fromModel.turnOutDoctor.name"
              clearable
              placeholder="请选择"
              value-key="userName"
              @clear="onTurnOutDoctorClear"
            >
              <el-option
                v-for="item in transferDoctorOptions"
                :key="item.userName"
                :label="item.name"
                :value="item"
                @click="onTurnOutDoctorClick(item)"
              />
            </el-select>
          </el-form-item>
        </template>
        <el-form-item
          label="接收病人的医生"
          prop="targetDoctor.userName"
          :rules="{
            required: true,
            trigger: 'change',
            message: '请选择接受医生'
          }"
          :validate-on-rule-change="false"
        >
          <el-select
            v-model="fromModel.targetDoctor.name"
            clearable
            placeholder="请选择"
            value-key="userName"
            @clear="onTargetDoctorClear"
          >
            <el-option
              v-for="item in transferDoctorOptions"
              :key="item.userName"
              :label="item.name"
              :value="item"
              @click="onTargetDoctorClick(item)"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="transferDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onTransferDialogConfirm"> 转移 </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible"
      title="随访用户打印预览"
      width="60%"
      custom-class="custom-dialog"
    >
      <print-table :tableHeader="printTableHeader" :data="selectedTableRow"> </print-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </page>
</template>

<style lang="less" scoped>
.follow-up-user {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  height: 100%;
  .search-wrapper {
    height: 50px;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 4px;
    .search-item {
      width: 300px;
    }
    .btn-group {
      padding: 0 6px;
    }
  }
  .tools-wrapper {
    height: 50px;
    width: 100%;
    display: flex;
    align-items: center;
    background: hsl(0, 0%, 98%);
    // margin: 0 10px;
    gap: 10px;
    padding: 0 20px;
  }
  .pagination {
    height: 50px;
    margin: 8px auto 8px;
  }
}
</style>
