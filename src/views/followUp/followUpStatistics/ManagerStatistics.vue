<!-- 管理员随访统计 -->
<script lang="ts" setup>
import useFollowUpStatistics from "@/composables/useFollowUpStatistics";
import type { VxeTableInstance, VxeToolbarInstance, VxeTablePropTypes } from "vxe-table";

import moment from "moment";

const router = useRouter();

//获取当前时间而且往前推一年
const getDefualtSearchDate = (): [Date, Date] => {
  const today = new Date();
  return [moment(today).add(-1, "y").toDate(), today];
};
const date = ref<[Date, Date]>(getDefualtSearchDate());
const { getTaskStatistics, taskStatistics, getTaskStatisticsTopFive } = useFollowUpStatistics();
// 进度条颜色
const customColors = [
  { color: "#f89898", percentage: 24 },
  { color: "#eebe77", percentage: 49 },
  { color: "#79bbff", percentage: 74 },
  { color: "#95d475", percentage: 100 }
];

const tableExportConfig = {
  type: "xlsx",
  types: ["xlsx", "csv", "html", "xml", "txt"]
} as VxeTablePropTypes.ExportConfig;
const xTable = ref<VxeTableInstance>();

const xToolbar = ref<VxeToolbarInstance>();

const onDateChange = (v: any) => {
  console.log(v);
  if (v) {
    getTaskStatistics(v);
  }
};
const onThisYearClick = () => {
  date.value = getDefualtSearchDate();
  getTaskStatistics(date.value);
};
const onPrintClick = () => {
  xTable.value?.print({
    sheetName: "随访统计",
    columns: [
      { type: "seq" },
      { field: "doctorName" },
      { field: "taskQuantity" },
      { field: "completedQuantity" },
      { field: "completedProgress" }
    ]
  });
};
const onExportClick = () => {
  const $table = xTable.value;

  $table!
    .exportData({
      sheetName: "随访统计",
      filename: `随访统计导出${moment().format("YYYY-MM-DD HH:mm:ss")}`
    })
    .then(r => {
      // console.log("成功", r);
    })
    .catch(e => {
      console.log("失败", e);
    });
};
const onDoctorNameShowDetail = (task: any) => {
  console.log(task);
  const {
    row
  }: {
    row: {
      completedProgress: number;
      completedQuantity: number;
      doctorName: string;
      doctorNo: string;
      taskQuantity: number;
      _X_ROW_KEY: string;
    };
  } = task;

  router.push({
    name: "doctorStatistics",
    query: {
      doctorNo: row.doctorNo,
      doctorName: row.doctorName
    }
  });
};

onMounted(() => {
  getTaskStatistics(date.value);
});
</script>
<template>
  <div class="follow-up-statistics-manager">
    <div class="header">
      <div class="search-criteria">
        <el-date-picker
          v-model="date"
          @change="onDateChange"
          type="daterange"
          range-separator="到"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :clearable="false"
        />

        <el-button type="primary" @click="onThisYearClick" plain>本月</el-button>
      </div>
      <div class="button-group">
        <vxe-toolbar ref="xToolbar" class="vxe-toolbar">
          <template #buttons>
            <vxe-button content="打印表格" @click="onPrintClick"></vxe-button>
            <vxe-button content="导出" @click="onExportClick"></vxe-button>
          </template>
        </vxe-toolbar>
      </div>
    </div>
    <div class="workload-ranking">
      <div class="title">工作量排行(前五)</div>
      <div class="workload-ranking__content">
        <div class="workload-ranking__content__item">
          <template v-for="task in getTaskStatisticsTopFive()">
            <div class="label">{{ task.doctorName }}</div>
            <div>{{ `${task.completedQuantity}/${task.taskQuantity}` }}</div>

            <el-progress
              :text-inside="true"
              :percentage="Number(task.completedProgress)"
              :stroke-width="16"
              :color="customColors"
            />
          </template>
        </div>
      </div>
    </div>

    <vxe-table
      :data="taskStatistics.doctorTaskStatisticals"
      :export-config="tableExportConfig"
      :print-config="{ sheetName: '随访统计' }"
      @cell-click="onDoctorNameShowDetail"
      ref="xTable"
      class="table"
      id="taskTable"
    >
      <vxe-column field="doctorName" title="医生姓名"> </vxe-column>
      <vxe-column field="taskQuantity" title="任务数量" align="right" />
      <vxe-column field="completedQuantity" title="已完成" align="right" />
      <vxe-column field="completedProgress" title="完成进度" align="right">
        <template #default="scope"> {{ scope.row.completedProgress }}% </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>
<style lang="less" scoped>
.follow-up-statistics-manager {
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 300px minmax(500px, 2fr);
  grid-template-rows: 32px 220px 5fr;
  grid-template-areas:
    "header header"
    "workload_ranking workload_ranking"
    "table table";
  gap: 10px;
  .header {
    // display: grid;
    // grid-template-columns: minmax(230px, 320px) auto;
    // grid-template-rows: 1fr;
    display: flex;
    justify-content: space-between;
    gap: 10px;
    grid-area: header;
    .search-criteria {
      display: flex;
      justify-content: space-between;
      gap: 10px;
    }
    :deep(.el-date-editor) {
      width: auto;
    }
    .button-group {
      display: flex;
      align-items: center;
      .vxe-toolbar {
        height: 30px;

        margin-left: 10px;
        button {
          height: 32px;
        }
      }
    }
  }
  .done-percentage {
    grid-area: done_percentage;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    gap: 10px;
    :deep(.el-progress-circle) {
      height: 200px;
    }

    .done-percentage__content__progress {
      display: flex;
      justify-content: center;

      .percentage-value {
        display: block;
        margin-bottom: 10px;
        font-size: 30px;
      }
    }
  }
  .workload-ranking {
    display: grid;
    grid-template-rows: 21px auto;
    grid-area: workload_ranking;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 10px;
    .title {
      color: #6e6e6e;
    }

    .workload-ranking__content {
      display: grid;
      grid-template-rows: repeat(22px, 5);
      gap: 10px;
      padding: 10px 0;

      .workload-ranking__content__item {
        height: 21px;
        display: grid;
        grid-template-columns: 80px 28px auto;
        align-items: center;
        gap: 4px;
      }
    }
  }
  .table {
    grid-area: table;
    height: 100%;
    .table__doctor-name {
      &:hover {
        cursor: pointer;
      }
    }
  }
}
</style>
