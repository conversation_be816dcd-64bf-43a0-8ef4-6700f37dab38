<!-- 医生看自己患者随访统计 -->
<script lang="ts" setup>
import { useStore } from "@/store";
// import { VxeTableInstance, VxeToolbarInstance, VxeTablePropTypes } from "vxe-table";
import type { VxeTableInstance, VxeToolbarInstance, VxeTablePropTypes } from "vxe-table";
import moment from "moment";
import { requestGetDoctorTasksStatisical } from "@/apis/followUpTasks";

const route = useRoute();
const store = useStore();

// 进度条颜色
const customColors = [
  { color: "#f89898", percentage: 24 },
  { color: "#eebe77", percentage: 49 },
  { color: "#79bbff", percentage: 74 },
  { color: "#95d475", percentage: 100 }
];

//获取当前时间而且往前推一年
const getDefaultSearchDate = (): [Date, Date] => {
  const today = new Date();
  return [moment(today).add(-1, "y").toDate(), today];
};
//计算年龄
const getAge = (birthday: Date): number => {
  return moment(moment()).diff(birthday, "years");
};

const searchCriteria = ref({
  startDate: getDefaultSearchDate()[0],
  endDate: getDefaultSearchDate()[1],
  name: ""
});
const searchFullDate = computed<[Date, Date]>({
  get() {
    return [searchCriteria.value.startDate, searchCriteria.value.endDate];
  },
  set(v) {
    searchCriteria.value.startDate = v[0];
    searchCriteria.value.endDate = v[1];
  }
});

/**
 * 显示百分比圆形进度条
 */
const dataToDonePercentage = ref({
  totalCount: 0,
  completedTasksCount: 0,
  completedPercentage: 0,
  noCompletedTasksCount: 0
});

const doctorInfo = computed(() => {
  const { doctorNo, doctorName } = route.query;
  if (doctorNo && doctorName) {
    return {
      id: doctorNo,
      name: doctorName
    };
  } else {
    const { userName, name } = store.state.doctor;

    return {
      id: userName,
      name
    };
  }
});

const tableData = ref([]);

const tableExportConfig = {
  type: "xlsx",
  types: ["xlsx", "csv", "html", "xml", "txt"]
} as VxeTablePropTypes.ExportConfig;
const xTable = ref<VxeTableInstance>();

const xToolbar = ref<VxeToolbarInstance>();

const onSearchCriteriaDateChange = (v: any) => {
  searchAllData();
};
const onSearchClick = () => {
  searchAllData();
};
const onResetCriteriaClick = () => {
  searchCriteria.value.name = "";
  searchFullDate.value = getDefaultSearchDate();
  return searchAllData();
};
const onPrintClick = () => {
  const $table = xTable.value;
  $table!.print({
    sheetName: "随访统计",
    columns: [
      { type: "seq" },
      { field: "patientId" },
      { field: "age" },
      { field: "executionTime" },
      { field: "lastTaskTime" },
      { field: "nextTaskTime" }
    ]
  });
};
const onExportClick = () => {
  const $table = xTable.value;

  $table!
    .exportData({
      sheetName: "随访统计",
      filename: `随访统计导出${moment().format("YYYY-MM-DD HH:mm:ss")}`
    })
    .then(r => {})
    .catch(e => {
      console.log("失败", e);
    });
};
const searchAllData = async () => {
  const getDoctorTasksResponse = await requestGetDoctorTasksStatisical({
    doctorNo: doctorInfo.value.id.toString(),
    startDate: searchFullDate.value[0],
    endDate: searchFullDate.value[1],
    patientName: searchCriteria.value.name
  });
  const { data: taskData } = getDoctorTasksResponse;
  const { docPatients, docTaskCompletion, statisticsClientTask } = taskData;
  dataToDonePercentage.value = {
    totalCount: docTaskCompletion.taskCount,
    completedTasksCount: docTaskCompletion.completedCount,
    completedPercentage: docTaskCompletion.completionRate,
    noCompletedTasksCount: docTaskCompletion.noCompletedCount
  };

  tableData.value = docPatients.map((m: any) => ({
    ...m,
    patientIdToStr: JSON.parse(m.patientIds).join("、"),
    age: m.birthdate == "0001-01-01 00:00:00" ? 0 : getAge(new Date(m.birthdate))
  }));
};

// onMounted(async () => {
//   searchAllData();
// });
onActivated(async () => {
  searchAllData();
});
</script>
<template>
  <div class="follow-up-doctor-statistics">
    <div class="header-tools">
      <div class="search-criteria">
        <div class="search-criteria__item">
          <el-date-picker
            v-model="searchFullDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            unlink-panels
            :clearable="false"
            :editable="false"
            @change="onSearchCriteriaDateChange"
          />
        </div>
        <div class="search-criteria__item">
          <el-input v-model="searchCriteria.name" placeholder="客户名字搜索" />
        </div>

        <div class="button-group">
          <el-button type="primary" @click="onSearchClick">搜索</el-button>
          <el-button type="primary" plain @click="onResetCriteriaClick">重置</el-button>
        </div>
      </div>
      <vxe-toolbar ref="xToolbar" class="vxe-toolbar">
        <template #buttons>
          <vxe-button content="打印表格" @click="onPrintClick"></vxe-button>
          <vxe-button content="导出" @click="onExportClick"></vxe-button>
        </template>
      </vxe-toolbar>
    </div>

    <div class="userinfo">
      <div class="title">当前医生</div>
      <div class="info-item">
        <div class="info-item__label-name">姓名：</div>

        <h2 class="info-item__value-name">{{ doctorInfo.name }}</h2>
      </div>
      <div class="info-item">
        <div class="info-item__label">工号：</div>

        <h2 class="info-item__value">{{ doctorInfo.id }}</h2>
      </div>
    </div>
    <div class="percentage-progress">
      <div class="percentage-progress__info">
        <div class="item">
          <div class="item-label">任务总数</div>
          <div class="item-value">{{ dataToDonePercentage.totalCount }}个</div>
        </div>

        <div class="item">
          <div class="item-label">已完成数</div>
          <div class="item-value">{{ dataToDonePercentage.completedTasksCount }}个</div>
        </div>
        <div class="item">
          <div class="item-label">未完成数</div>
          <div class="item-value">{{ dataToDonePercentage.noCompletedTasksCount }}个</div>
        </div>
        <div class="item">
          <div class="item-label">总人数</div>
          <div class="item-value">{{ tableData.length }}人</div>
        </div>
      </div>
      <el-progress
        type="circle"
        :percentage="dataToDonePercentage.completedPercentage"
        :color="customColors"
        :stroke-width="16"
        class="done-percentage__content__progress"
      >
        <template #default="{ percentage }">
          <span class="percentage-value">{{ Math.ceil(percentage) }}%</span>
          <span class="percentage-label">完成度</span>
        </template>
      </el-progress>
    </div>
    <div class="table-wrap">
      <vxe-table :data="tableData" :export-config="tableExportConfig" ref="xTable" height="auto">
        <!-- <vxe-column field="patientId" title="病人号"> </vxe-column> -->
        <vxe-column field="patientIdToStr" title="病人号" show-overflow> </vxe-column>

        <vxe-column field="name" title="姓名"> </vxe-column>
        <vxe-column field="phone" title="手机号码"> </vxe-column>
        <vxe-column field="age" title="年龄">
          <template #default="scope">
            {{ scope.row.age == 0 ? "-" : scope.row.age }}
          </template>
        </vxe-column>
        <vxe-column field="executionTime" title="首次任务"> </vxe-column>
        <vxe-column field="lastTaskTime" title="上次任务"> </vxe-column>
        <vxe-column field="nextTaskTime" title="下次任务">
          <template #default="scope">
            {{ scope.row.nextTaskTime == null ? "-" : scope.row.nextTaskTime }}
          </template></vxe-column
        >
      </vxe-table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.follow-up-doctor-statistics {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 3fr;
  grid-template-rows: 34px 200px 1fr;
  grid-template-areas:
    "tools tools"
    "userinfo progress"
    "table table";
  gap: 10px;
  box-sizing: border-box;
  // overflow: hidden;

  .header-tools {
    grid-area: tools;
    display: flex;
    justify-content: space-between;
    .search-criteria {
      display: flex;
      justify-content: space-between;
      gap: 20px;
    }
  }

  .percentage-progress {
    grid-area: progress;
    border: 1px solid #e8eaec;
    border-radius: 6px;
    padding: 10px;
    display: grid;
    grid-template-columns: 200px 1fr;
    grid-template-rows: 36px 1fr;
    grid-template-areas:
      "info progress"
      "info progress";
    gap: 10px;

    .percentage-progress__info {
      grid-area: info;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-around;
      gap: 20px;
      padding-left: 20px;
      border-right: 1px solid #e8eaec;
      padding: 20px;
      .item {
        width: 100%;
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .item-label {
          display: flex;
          align-items: center;
          color: var(--el-text-color-secondary);
        }
        .item-value {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }
      }
    }

    .done-percentage__content__progress {
      grid-area: progress;
      width: calc(100% - 20px);
      display: flex;
      justify-content: center;
      margin: 0 auto;

      .percentage-value {
        display: block;
        margin-bottom: 10px;
        font-size: 30px;
      }
    }
  }

  .userinfo {
    grid-area: userinfo;
    border: 1px solid #e8eaec;
    border-radius: 6px;
    padding: 10px;
    .title {
      border-bottom: 1px solid #e8eaec;
      padding-bottom: 8px;
    }
    .info-item {
      display: flex;
      align-items: center;

      .info-item__label {
        color: var(--el-text-color-secondary);
      }
      .info-item__value-name {
        font-size: 40px;
        margin: 10px 0;
        color: #333;
      }
      .info-item__value {
        color: var(--el-text-color-primary);
      }
    }
  }
  .table-wrap {
    grid-area: table;
    box-sizing: border-box;
  }
  @media screen and (max-width: 1366px) {
    grid-template-rows: 34px 180px 1fr;
    .info-item__value-name {
      font-size: 20px;
    }
    .percentage-progress {
      grid-template-columns: 120px 1fr;
      .percentage-progress__info {
        padding: 8px;
        .item {
          width: 100%;
        }
      }
      .done-percentage__content__progress {
        width: 180;
        margin: 0 auto;
      }
    }
  }
}
</style>
