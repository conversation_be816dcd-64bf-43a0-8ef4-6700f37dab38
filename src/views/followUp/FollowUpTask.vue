<script lang="ts">
import type { Task } from "@/composables/useFollowUpTasks";
import type { MessageTemplateItem } from "@/composables/useMessageTemplates";
import { ElMessage, ElTable } from "element-plus";
import { socket } from "@/utils/socket";
import { requestAuditTask } from "@/apis/followUpTasks";

const multipleTableRef = ref<InstanceType<typeof ElTable>>();

type TagType = "primary" | "warning" | "success" | "danger" | "info";

enum Operation {
  Create,
  Update
}
</script>
<script lang="ts" setup>
import useFollowUpTasks from "@/composables/useFollowUpTasks";
import useMessageTemplates from "@/composables/useMessageTemplates";
import { store } from "@/store";

const {
  tasks,
  pagation,
  tasksFilter,
  tasksTotal,
  getTasks,
  editTask,
  deleteTask,
  submitTask,
  sendTask,
  stopTasks,
  tryStartTasks
} = useFollowUpTasks();
const { messageTemplates } = useMessageTemplates();
const DEFAULT_TASK = {
  id: 0,
  name: "",
  doctorNo: "",
  userId: 0,
  status: 0,
  content: "",
  summary: null,
  isAudit: false,
  isNotifyDoctor: false,
  createTime: new Date(),
  executionTime: new Date(),
  statusTxt: ""
};

const TASK_FORM_RULES = {
  name: [{ required: true, message: "请输入标题", trigger: "blur" }],
  content: [{ required: true, message: "请输入内容", trigger: "blur" }]
};

const dialogVisible = ref(false);

const taskModel = reactive<Task>(
  JSON.parse(
    JSON.stringify({
      DEFAULT_TASK
    })
  )
);

const currentQuery = reactive({
  taskName: "",
  doctorNo: "",
  userId: "",
  status: "",
  doctorName: "",
  userName: ""
});

const tasksStatus = [
  { id: 1, name: "待审核", type: "warning" },
  { id: 6, name: "已审核", type: "primary" },
  { id: 2, name: "已执行", type: "success" },
  { id: 5, name: "已完成", type: "primary" },
  { id: 3, name: "失败", type: "danger" }
];

const tasksSelection = ref<Task[]>([]);

const operation = ref(Operation.Create);

const dialog = reactive({
  visible: false
});

const taskSummaryDialogVisible = ref(false);

const printTableHeader = [
  {
    field: "name",
    title: "任务名"
  },
  {
    field: "content",
    title: "内容"
  },
  {
    field: "doctorName",
    title: "所属医生"
  },
  {
    field: "statusTxt",
    title: "状态"
  },
  {
    field: "createTime",
    title: "创建时间"
  },
  {
    field: "executionTime",
    title: "结束时间"
  }
];
const query = () => {
  console.log(currentQuery);
  tasksFilter.name = currentQuery.taskName;
  tasksFilter.doctorNo = currentQuery.doctorNo;
  tasksFilter.doctorName = currentQuery.doctorName;
  tasksFilter.userName = currentQuery.userName;
  const userId = parseInt(currentQuery.userId);
  const status = parseInt(currentQuery.status);
  if (!isNaN(userId)) {
    tasksFilter.userId = userId;
  } else {
    tasksFilter.userId = 0;
    currentQuery.userId = "";
  }
  if (!isNaN(status)) tasksFilter.status = status;
  else {
    tasksFilter.status = 0;
  }
  getTasks();
};

const resetQuery = () => {
  currentQuery.taskName = "";
  currentQuery.doctorNo = "";
  currentQuery.userId = "";
  currentQuery.status = "";
  currentQuery.doctorName = "";
  currentQuery.userName = "";
  query();
};

const onTableSelectionChange = (val: Task[]) => {
  tasksSelection.value = val;
  // }
};

const onEditTaskClick = (task: Task) => {
  taskModel.id = task.id;
  taskModel.name = task.name;
  taskModel.doctorNo = task.doctorNo;
  taskModel.userId = task.userId;
  taskModel.content = task.content;
  taskModel.executionTime = task.executionTime;
  operation.value = Operation.Update;
  dialog.visible = true;
};

const onSubmitSummaryClick = (task: Task) => {
  taskModel.id = task.id;
  taskModel.summary = "";
  taskSummaryDialogVisible.value = true;
};

const onDeleteClick = (task: Task) => {
  ElMessageBox.confirm("该操作将永久删除该条数据，是否继续？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      deleteTask(task.id).then(() => {
        ElMessage.success("删除成功");
      });
    })
    .catch(() => {});
};

const onSendOutClick = (task: Task) => {
  ElMessageBox.confirm("该操作将发送任务，是否继续？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      sendTask(task.id).then(() => {
        ElMessage.success("发送成功");
      });
    })
    .catch(() => {});
};

const onSubmitConfirm = () => {
  if (taskModel.summary) {
    submitTask(taskModel.id, taskModel.summary).then(() => {
      ElMessage.success("提交成功");
      taskSummaryDialogVisible.value = false;
    });
  } else {
    ElMessage.warning("请输入任务总结");
  }
};

const onChooseTemplateClick = (template: MessageTemplateItem) => {
  taskModel.name = template.name;
  taskModel.content = template.content;
};

const onDialogConfirmClick = () => {
  const promise =
    operation.value == Operation.Create
      ? new Promise<void>((resolve, reject) => {
          null;
        })
      : editTask(taskModel);
  promise.then(() => {
    ElMessage.success("保存成功");
    dialog.visible = false;
  });
};

const onTryStartTaskClick = () => {
  if (tasksSelection.value.length > 0) {
    const isValid = !tasksSelection.value.find(t => ![3, 4].includes(t.status));
    if (isValid)
      tryStartTasks(tasksSelection.value.map(t => t.id)).then(() => {
        ElMessage.success("重试执行");
        multipleTableRef.value?.clearSelection();
      });
    else ElMessage.warning("选中的任务必须是停止或失败状态");
  }
};

const onStopTaskClick = () => {
  if (tasksSelection.value.length > 0) {
    const isValid = !tasksSelection.value.find(t => t.status != 1);
    if (isValid)
      stopTasks(tasksSelection.value.map(t => t.id)).then(() => {
        ElMessage.success("停止任务");
        multipleTableRef.value?.clearSelection();
      });
    else ElMessage.warning("选中的任务必须是待执行状态");
  }
};

const listenFollowUpTaskCallback = (data: { id: number; status: number }) => {
  const { id, status } = data;
  const index = tasks.value.findIndex(t => t.id == id);
  tasks.value[index].status = status;
};
const onPrintClick = () => {
  dialogVisible.value = true;
};

const onAuditTaskClick = (task: Task) => {
  requestAuditTask(task.id).then(r => {
    task.status = 6;
    ElMessage.success("审核完成");
  });
};

onMounted(() => {
  getTasks();
  socket.on("followUpTask", listenFollowUpTaskCallback);
});

onUnmounted(() => {
  socket.off("followUpTask", listenFollowUpTaskCallback);
});
</script>
<template>
  <div class="default-page task-page">
    <div class="header">
      <div class="header-item">
        <div class="header-item__label">任务名称</div>
        <el-input v-model="currentQuery.taskName" placeholder="请输入任务名称" clearable></el-input>
      </div>
      <div class="header-item">
        <div class="header-item__label">医生姓名</div>
        <el-input
          v-model="currentQuery.doctorName"
          placeholder="请输入医生姓名"
          clearable
        ></el-input>
      </div>
      <div class="header-item">
        <div class="header-item__label">病人姓名</div>
        <el-input v-model="currentQuery.userName" placeholder="病人姓名" clearable></el-input>
      </div>
      <div class="header-item">
        <div class="header-item__label">任务状态</div>
        <el-select v-model="currentQuery.status" class="spacing" placeholder="请选择状态">
          <el-option
            v-for="item in tasksStatus"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <el-button type="primary" @click="query">查询</el-button>
      </div>
      <div class="header-item">
        <el-button @click="resetQuery">重置</el-button>
      </div>
    </div>
    <div>
      <el-button type="warning" @click="onTryStartTaskClick"> 重试任务 </el-button>
      <!-- <el-button type="info" @click="onStopTaskClick">停止任务</el-button> -->
      <el-button @click="onPrintClick" :disabled="tasksSelection.length == 0"> 打印预览 </el-button>
    </div>

    <el-table
      :data="tasks"
      @selection-change="onTableSelectionChange"
      ref="multipleTableRef"
      row-key="id"
      width="100%"
      height="100%"
    >
      <el-table-column type="selection" width="55" :reserve-selection="true" />
      <!-- <el-table-column prop="id" label="任务Id" width="100px" /> -->
      <el-table-column prop="name" label="任务名称" />
      <el-table-column prop="content" label="内容">
        <template #default="scope">
          <div class="task-content">
            {{ scope.row.content }}
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="isAudit" label="审核状态" width="120px">
        <template #default="scope">
          <el-tag :type="scope.row.isAudit ? 'success' : 'warning'">
            {{ scope.row.isAudit ? "已审核" : "未审核" }}
          </el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="userName" label="姓名" width="160px" />
      <el-table-column prop="patientId" label="病人号" width="160px" />
      <el-table-column prop="doctorName" label="医生姓名" width="120px" />
      <el-table-column prop="status" label="执行状态" width="100px">
        <template #default="scope">
          <el-tag
            v-if="tasksStatus.find(s => s.id == scope.row.status)"
            :type="tasksStatus.find(s => s.id == scope.row.status)?.type as TagType"
          >
            {{ tasksStatus.find(s => s.id == scope.row.status)?.name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" />
      <el-table-column prop="executionTime" label="执行时间" />
      <el-table-column label="操作" width="240px">
        <template #default="scope">
          <!-- <el-button
            :disabled="store.state.doctor.userName != scope.row.doctorNo"
            v-if="scope.row.status == 6 && scope.row.isAudit"
            type="success"
            @click="onSendOutClick(scope.row)">
            发送
          </el-button> -->
          <el-button
            type="primary"
            :disabled="store.state.doctor.userName != scope.row.doctorNo"
            v-if="scope.row.status == 1"
            @click="onAuditTaskClick(scope.row)"
          >
            审核
          </el-button>
          <el-button
            type="primary"
            plain
            @click="onEditTaskClick(scope.row)"
            :disabled="store.state.doctor.userName != scope.row.doctorNo"
            v-if="scope.row.status == 1"
          >
            编辑
          </el-button>
          <el-button
            :disabled="store.state.doctor.userName != scope.row.doctorNo"
            v-if="scope.row.status == 2"
            type="success"
            @click="onSubmitSummaryClick(scope.row)"
          >
            提交
          </el-button>
          <el-button
            type="danger"
            plain
            @click="onDeleteClick(scope.row)"
            :disabled="store.state.doctor.userName != scope.row.doctorNo"
            v-if="scope.row.status == 1 || scope.row.status == 6"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      layout="prev, pager, next, sizes"
      v-model:page-size="pagation.row"
      v-model:current-page="pagation.page"
      :total="tasksTotal"
    ></el-pagination>
    <el-dialog
      v-model="dialog.visible"
      :title="operation == Operation.Create ? '创建任务' : '编辑任务'"
      custom-class="follow-up-task-dialog"
    >
      <el-scrollbar class="short-message-templates">
        <el-card class="box-card" v-for="item in messageTemplates">
          <template #header>
            <div class="card-header">
              <div>{{ item.name }}</div>
              <el-button class="button" type="text" @click="onChooseTemplateClick(item)">
                选择模板
              </el-button>
            </div>
          </template>
          {{ item.content }}
        </el-card>
      </el-scrollbar>
      <div class="task-form">
        <div class="doctor-and-patient-info">
          <span v-if="operation == Operation.Update">
            &emsp; 医生编号：
            <label class="light-height">
              {{ taskModel.doctorNo }}
            </label>
          </span>
          <span v-if="operation == Operation.Update">
            &emsp;用户Id：
            <label class="light-height">
              {{ taskModel.userId }}
            </label>
          </span>
        </div>
        <el-form :model="taskModel" label-width="80px" :rules="TASK_FORM_RULES">
          <el-form-item label="标题" prop="name">
            <el-input v-model="taskModel.name" />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input
              v-model="taskModel.content"
              :autosize="{ minRows: 3, maxRows: 5 }"
              type="textarea"
              resize="none"
              maxlength="300"
            />
          </el-form-item>
          <el-form-item label="时间" prop="executionTime">
            <el-date-picker
              v-model="taskModel.executionTime"
              type="datetime"
              :clearable="false"
              :teleported="false"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="onDialogConfirmClick">
            {{ operation == Operation.Create ? "创建任务" : "保存修改" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="taskSummaryDialogVisible" title="提交任务">
      <el-input
        v-model="taskModel.summary"
        :autosize="{ minRows: 4, maxRows: 6 }"
        type="textarea"
        placeholder="请输入任务总结"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskSummaryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onSubmitConfirm">提交</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible"
      title="随访用户打印预览"
      width="60%"
      custom-class="custom-dialog"
    >
      <!-- <div> -->
      <print-table :tableHeader="printTableHeader" :data="tasksSelection"></print-table>
      <!-- </div> -->
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.task-page {
  .header {
    display: flex;
    .header-item {
      display: flex;
      margin: 0 10px 0 0;
      align-items: center;
      gap: 10px;
      .el-input {
        width: 200px;
      }
      .header-item__label {
        flex-shrink: 0;
      }
      .el-select {
        min-width: 120px;
      }
    }
    @media screen and (max-width: 1580px) {
      .header-item {
        margin: 0 10px 10px 0;
      }
    }
  }
  .task-content {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .short-message-templates {
    width: 45%;
    padding: 0 15px;
    .el-card {
      margin-bottom: 10px;
    }
  }
  .task-form {
    flex: 1;
    .doctor-and-patient-info {
      padding: 10px;
      .light-height {
        color: var(--el-color-warning);
      }
    }
  }
}

@media screen and (max-width: 1580px) {
  .task-page {
    grid-template-rows: 100px 50px auto 50px !important;
  }
}
</style>
