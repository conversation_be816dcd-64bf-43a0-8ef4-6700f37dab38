<script lang="ts">
enum OperationType {
  添加,
  修改
}
interface Form {
  id?: number;
  title: string;
  classId?: number;
  description: string;
  authorId: number;
  content: string;
  coverThePath: string;
  pdfUrl: string;
}
</script>

<script lang="ts" setup>
import {
  requestGetArticles,
  Article,
  requestChangeArticleState,
  requestCreateArticle,
  requestUplaodImage,
  requestDeleteArticle,
  requestUpdateArticle,
  RequestGetArticles,
  requestUploadPDF
} from "@/apis/article";
import { requestGetArticleClasses, ResponseGetArticleClass } from "@/apis/articleClasses";
import WangEdit from "@/components/WangEdit/Index.vue";
import { useStore } from "@/store";
import {
  ElMessage,
  ElMessageBox,
  FormInstance,
  FormRules,
  genFileId,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadRequestOptions
} from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { useFileDialog } from "@vueuse/core";

const apiUrl = import.meta.env.VITE_APP_API_URL;
const WangEditApiUrl = window.config.WangEditApiUrl;
const store = useStore();
const authorInfo = computed(() => {
  return {
    authorId: store.state.doctor.id,
    authorName: store.state.doctor.name
  };
});
const search = reactive({
  title: "",
  author: "",
  classId: 0,
  state: -1
});

const paginationInfo = reactive({
  page: 1,
  size: 10,
  total: 0
});

const currentPage = ref(1);
const isShowPhonePreview = ref(false);

const articleClassifies = ref<ResponseGetArticleClass[]>([]);
const isShowArticleDialog = ref(false);
const articleList = ref<Article[]>([]);
const operation = ref(OperationType.添加);

const resetEditForm = () => {
  return {
    id: undefined,
    title: "",
    classId: undefined,
    description: "",
    authorId: authorInfo.value.authorId,
    content: "",
    coverThePath: "",
    pdfUrl: ""
  };
};

let editForm = reactive<Form>(resetEditForm());

const RULES: FormRules = {
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  classId: [{ required: true, message: "请选择分类", trigger: "blur" }]
};
const formRef = ref<FormInstance>();

const onResetSearchBtnClick = () => {
  search.author = "";
  search.classId = 0;
  search.state = -1;
  search.title = "";
  onArticleSearchBtnClick();
  // console.log(articleList.value);
};
const onSwitchChange = async (val: string | number | boolean, article: Article) => {
  article.state = val ? 1 : 0;
  try {
    const { data } = await requestChangeArticleState({
      id: article.id,
      publish: val ? true : false
    });
    article.state = val ? 1 : 0;
    ElMessage({
      message: "修改成功！",
      type: "success"
    });
  } catch (error) {
    ElMessage.error("修改失败!");
  }
};
const onCreateArticleBtnClick = () => {
  editForm.classId = undefined;
  editForm.id = undefined;
  editForm.content = "";
  editForm.coverThePath = "";
  editForm.description = "";
  editForm.title = "";
  operation.value = OperationType.添加;

  isShowArticleDialog.value = true;
};
const onEditArticleBtnClick = (row: Article) => {
  editForm.id = row.id;
  editForm.classId = row.classId;
  editForm.description = row.description;
  editForm.coverThePath = row.coverThePath;
  editForm.content = row.content;
  editForm.title = row.title;
  operation.value = OperationType.修改;
  isShowArticleDialog.value = true;
};
const onConfirmEditFormBtnClick = async () => {
  const res = await formRef.value?.validate();
  if (res) {
    try {
      switch (operation.value) {
        case OperationType.修改:
          const { data: editData } = await requestUpdateArticle({
            id: editForm.id!,
            title: editForm.title,
            authorId: editForm.authorId,
            description: editForm.description,
            classId: editForm.classId!,
            content: editForm.content.replaceAll(apiUrl, WangEditApiUrl),
            coverThePath: editForm.coverThePath,
            pdfUrl: editForm.pdfUrl
          });
          const index = articleList.value.findIndex(ele => ele.id == editForm.id!);
          if (index != -1) {
            articleList.value[index].coverThePath = editData.coverThePath;
            articleList.value[index].title = editData.title;
            articleList.value[index].description = editData.description;
            articleList.value[index].classId = editData.classId;
            articleList.value[index].content = editData.content;
          }
          ElMessage({
            message: "保存成功！",
            type: "success"
          });
          break;

        default:
          const { data: createData } = await requestCreateArticle({
            authorId: editForm.authorId,
            title: editForm.title,
            description: editForm.description,
            classId: editForm.classId!,
            content: editForm.content.replaceAll(apiUrl, WangEditApiUrl),
            coverThePath: editForm.coverThePath,
            pdfUrl: editForm.pdfUrl
          });
          articleList.value.push({
            id: createData.id,
            title: createData.title,
            classId: createData.classId,
            description: createData.description,
            authorName: createData.authorName,
            content: createData.content,
            coverThePath: createData.coverThePath,
            state: createData.state,
            createTime: createData.createTime,
            className: createData.className,
            pdfUrl: createData.pdfUrl
          });
          ElMessage({
            message: "添加成功！",
            type: "success"
          });
          break;
      }
      isShowArticleDialog.value = false;
    } catch (error) {
      ElMessage.error("保存失败!");
    }
  }
};

const httpRequest = async (options: UploadRequestOptions) => {
  // console.log(options);
  try {
    const { data } = await requestUplaodImage(options.file);
    editForm.coverThePath = data.url;
  } catch (err) {
    console.log(err);
  }
};

const uploadPdf = async (file: File) => {
  // console.log(options);
  try {
    const { data } = await requestUploadPDF(file);
    editForm.pdfUrl = data.url;
    ElMessage({
      message: "上传成功！",
      type: "success"
    });
  } catch (err) {
    console.log(err);
  }
};

const onPreviewBtnClick = (row: Article) => {
  editForm.content = row.content;
  isShowPhonePreview.value = true;
};
const onDeleteArticleBtnClick = async (row: Article) => {
  await ElMessageBox.confirm("确定删除此文章吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  try {
    requestDeleteArticle(row.id);
    ElMessage({
      message: "删除成功！",
      type: "success"
    });
    const index = articleList.value.findIndex(ele => ele.id == row.id);
    if (index != -1) {
      articleList.value.splice(index, 1);
    }
  } catch (error) {
    ElMessage.error("删除失败!");
  }
};

const onCurrentPageChangeClick = async (page: number) => {
  const obj = {
    page: page,
    rows: paginationInfo.size
  } as RequestGetArticles;
  if (search.title) {
    obj["title"] = search.title;
  }
  if (search.classId != 0) {
    obj["classId"] = search.classId;
  }
  if (search.state != -1) {
    obj["state"] = search.state;
  }
  const { data: articlesData } = await requestGetArticles(obj);
  paginationInfo.page = page;
  paginationInfo.total = articlesData.count;
  articleList.value = articlesData.articles.map(ele => {
    return {
      ...ele,
      content: ele.content.replaceAll(WangEditApiUrl, apiUrl)
    };
  });
};
const onArticleSearchBtnClick = async () => {
  const obj = {
    page: paginationInfo.page,
    rows: paginationInfo.size
  } as RequestGetArticles;
  if (search.title != "") {
    obj["title"] = search.title;
  }
  if (search.classId != 0) {
    obj["classId"] = search.classId;
  }
  if (search.state != -1) {
    obj["state"] = search.state;
  }
  const { data: articlesData } = await requestGetArticles(obj);
  paginationInfo.total = articlesData.count;
  articleList.value = articlesData.articles.map(ele => {
    return {
      ...ele,
      content: ele.content.replaceAll(WangEditApiUrl, apiUrl)
    };
  });
};
const { onChange, open, reset, files } = useFileDialog({
  multiple: false,
  accept: ".pdf"
});

function openPdf() {
  if (editForm.pdfUrl) {
    window.open(apiUrl + editForm.pdfUrl, "_blank");
  }
}

function onSelectPdfFile() {
  reset();
  onChange(async fs => {
    if (fs != null && fs.item(0) != null) {
      const file = fs.item(0);
      if (file) {
        await uploadPdf(file);
      }
    }
  });
  open();
}

onMounted(async () => {
  const { data: articleClasses } = await requestGetArticleClasses();
  articleClassifies.value = articleClasses;

  const { data: articlesData } = await requestGetArticles({
    page: currentPage.value,
    rows: paginationInfo.size
  });
  paginationInfo.total = articlesData.count;

  articleList.value = articlesData.articles.map(ele => {
    return {
      ...ele,
      content: ele.content.replaceAll(WangEditApiUrl, apiUrl)
    };
  });
});
</script>

<template>
  <div class="article-wrap">
    <div class="article-wrap__header">
      <div class="search">
        <div class="search-item">
          <div class="search__label">文章标题</div>
          <el-input
            class="search__content"
            v-model="search.title"
            placeholder="请输文章标题"
            clearable
          />
        </div>
        <div class="search-item">
          <div class="search__label">文章分类</div>
          <el-select
            class="search__content"
            v-model="search.classId"
            placeholder="请选择文章分类"
            style="width: 10rem"
          >
            <el-option :key="0" label="全部" :value="0" />
            <el-option
              v-for="item in articleClassifies"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
        <div class="search-item">
          <div class="search__label">文章状态</div>
          <el-select
            class="search__content"
            v-model="search.state"
            placeholder="请选择文章状态"
            style="width: 10rem"
          >
            <el-option :key="0" label="全部" :value="-1" />
            <el-option :key="1" label="未发布" :value="0" />
            <el-option :key="2" label="已发布" :value="1" />
          </el-select>
        </div>
      </div>
      <div class="button-group">
        <el-button type="primary" plain @click="onArticleSearchBtnClick">搜索</el-button>
        <el-button @click="onResetSearchBtnClick">重置</el-button>
        <el-button type="primary" @click="onCreateArticleBtnClick">添加文章</el-button>
      </div>
    </div>

    <el-table style="flex: 1" border :data="articleList">
      <el-table-column prop="title" label="文章标题" align="center" />
      <el-table-column prop="className" label="文章分类" align="center" width="120" />
      <el-table-column prop="description" label="摘抄自" align="center" show-overflow-tooltip />
      <el-table-column prop="authorName" label="作者" align="center" width="120" />
      <el-table-column prop="createTime" label="创建时间" width="120" />
      <el-table-column prop="coverThePath" label="封面" align="center" width="170">
        <template #default="{ row }">
          <el-image
            v-if="row.coverThePath"
            preview-teleported
            :preview-src-list="[apiUrl + row.coverThePath]"
            style="width: 10rem; height: 10rem"
            :src="apiUrl + row.coverThePath"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80">
        <template #default="{ row }">
          <el-switch
            :model-value="row.state == 1 ? true : false"
            @change="onSwitchChange($event, row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="240">
        <template #default="{ row }">
          <el-button plain @click="onPreviewBtnClick(row)" text bg>预览</el-button>

          <el-button type="primary" plain @click="onEditArticleBtnClick(row)" text bg
            >编辑</el-button
          >
          <el-button type="danger" plain @click="onDeleteArticleBtnClick(row)" text bg
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      class="pagination"
      background
      layout="prev, pager, next"
      :total="paginationInfo.total"
      :page-size="paginationInfo.size"
      v-model:current-page="currentPage"
      @current-change="onCurrentPageChangeClick"
    />
    <el-dialog
      v-model="isShowArticleDialog"
      width="70%"
      custom-class="custom-dialog1"
      :title="operation == OperationType.添加 ? '创建文章' : '编辑文章'"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="edit-form-wrap">
        <el-form class="form" :model="editForm" ref="formRef" label-position="top" :rules="RULES">
          <el-form-item label="文章标题" prop="title">
            <el-input v-model="editForm.title" />
          </el-form-item>
          <el-form-item label="文章分类" prop="classId">
            <el-select v-model="editForm.classId" placeholder="请选择文章分类" clearable filterable>
              <el-option v-for="item in articleClassifies" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="description" label="摘抄自">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 4 }"
              placeholder="请输入文章描述"
              v-model="editForm.description"
            />
          </el-form-item>
          <el-form-item prop="coverThePath" label="封面">
            <el-upload
              :limit="1"
              accept=".jpg,.png"
              :http-request="httpRequest"
              :show-file-list="false"
            >
              <el-image
                v-if="editForm.coverThePath"
                class="iamge"
                :src="apiUrl + editForm.coverThePath"
                fit="cover"
              />
              <el-icon v-else class="avatar-uploader-icon"> <Plus /></el-icon>
            </el-upload>
          </el-form-item>
          <el-form-item prop="pdfUrl" label="附件">
            <div style="display: flex; align-items: center; gap: 10px">
              <span
                v-if="files?.[0]"
                style="cursor: pointer; color: var(--el-color-primary)"
                @click="openPdf"
              >
                {{ files[0].name }}
              </span>
              <el-button type="primary" @click="onSelectPdfFile">上传文件</el-button>
            </div>
          </el-form-item>
        </el-form>
        <div class="article-rich-text">
          <WangEdit v-model="editForm.content" />
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isShowArticleDialog = false">取消</el-button>
          <el-button type="primary" @click="onConfirmEditFormBtnClick">
            {{ operation == OperationType.添加 ? "创建" : "保存" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
    <PhonePreview
      v-model="isShowPhonePreview"
      :html="editForm.content"
      height="80%"
      width="360px"
    />
  </div>
</template>

<style lang="less" scoped>
.article-wrap {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .article-wrap__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    overflow: auto;
    .search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;

      .search-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .search__label {
          flex-shrink: 0;
          width: 80px;
        }
        .search__content {
          min-width: 100px;
          width: 22rem;
        }
      }
    }
    .button-group {
      display: flex;
    }
  }

  .pagination {
    align-self: center;
  }
  .edit-form-wrap {
    display: flex;
    gap: 1rem;

    .form {
      // flex: 2;
      // flex: 0;
      flex: 0.4;
      // flex-basis: 30%;
    }
    .article-rich-text {
      // flex: 3;
      // flex-shrink: 0;
      flex: 0.6;
      width: 20rem;
      > video {
        width: 60rem;
      }
    }
  }

  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }
  .avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .iamge {
    width: 178px;
    height: 178px;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #ebeef5;
  }

  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }

  :deep(.custom-dialog1) {
    height: 75%;
    display: flex;
    flex-direction: column;
  }
  :deep(.custom-dialog1 > .el-dialog__body) {
    flex: 1;
    overflow: hidden;
    display: flex;
    padding-bottom: 10px;
  }
  :deep(.custom-dialog1 > .el-dialog__body > *) {
    height: 100%;
    width: 100%;
  }
}
</style>
