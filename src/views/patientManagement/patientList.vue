<script lang="ts" setup>
import Page from "@/components/Page.vue";
import { birthdayConversionAge } from "@/utils/validationRules";
import {
  getPatients,
  getPreview,
  postPatients,
  putPatients,
  requestDeletePatient
} from "@/apis/patients";
import moment from "moment";
import { useStorePatient } from "@/store/patient";

interface ViewPatient {
  address: string | null;
  birthday: string;
  doctorName: string;
  id: number;
  idCard: string;
  lastFollowUpTime: string | null;
  name: string;
  nextFollowUpTime: string | null;
  patientIdToStr: string;
  phone: string;
  patientIds: string[];
  returnVisitTime: string | null;
  sex: string;
}
const mainlandReg =
  /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/; // 大陆身份证
const HKReg = /^[A-Z]{1,2}[0-9]{6,10}[0-9A-Z]$/; //香港身份证
const MacaoReg = /^[1|5|7][0-9]{6}[0-9A-Z]$/; //澳门身份证
const TaiWangReg = /^[a-zA-Z][0-9]{9}$/; //台湾身份证

const HKandMacaoPassReg = /^[HMhm]{1}([0-9]{10}|[0-9]{8})$/; //港澳通行证

const HKLiveReg = /(^810)([A-Za-z0-9]{15}$)/; //香港居住证
const MacaoLiveReg = /(^820)([A-Za-z0-9]{15}$)/; //香港居住证
const TaiWangLiveReg = /(^820)([A-Za-z0-9]{15}$)/; //香港居住证

const PassPortReg = /^([a-zA-z]|[0-9]){5,17}$/; //护照

const router = useRouter();
const singleTableRef = ref();

const dataObj = reactive({
  searchVal: "",
  total: 0,
  currentPage: 1,
  pageSize: 20,
  patientList: [] as ViewPatient[],
  dialogTableVisible: false,
  dialogSearchVal: "",
  searchInfo: {} as any,
  editDialogShow: false,
  editInfo: {} as any,
  editIndex: 0,
  importSearchType: "idCard",
  importSearchTypeList: [
    {
      label: "身份证",
      value: "idCard"
    },
    {
      label: "姓名",
      value: "personName"
    },
    {
      label: "手机号",
      value: "telePhone"
    }
  ] as any[],
  tableData: [] as any[],
  checkRow: {} as any
});

const { loadStorePatient } = useStorePatient();
// 获取病人列表
const getPatientList = async () => {
  const { data, total } = await getPatients({
    page: dataObj.currentPage,
    rows: dataObj.pageSize,
    filter: dataObj.searchVal
  });
  dataObj.patientList = data.map((d: any) => ({
    ...d,
    patientIdToStr: d.patientIds.join("、"),
    age: birthdayConversionAge(d.birthday)
  }));
  dataObj.total = total;
};

// 搜索病人
const searchPatient = () => {
  dataObj.currentPage = 1;
  getPatientList();
};

// 页码改变的回调函数
const handleCurrentChange = () => {
  getPatientList();
};

// 导入按钮
const importBtnClick = () => {
  dataObj.searchInfo = {};
  dataObj.dialogTableVisible = true;
};

// 导入前的预览信息
const previewBtn = () => {
  if (dataObj.dialogSearchVal == "") {
    ElMessage.warning("请输入搜索内容");
    return;
  }
  //if (
  //dataObj.dialogSearchVal.length == 15 ||
  //dataObj.dialogSearchVal.length == 18
  //) {
  getPreview({
    [dataObj.importSearchType]: dataObj.dialogSearchVal
  }).then(r => {
    const { data } = r;
    dataObj.tableData = data
      .map((ele: any) => {
        return {
          ...ele,
          patientIdToStr: ele.patientIds.join("、")
        };
      })
      .filter((ele: any) => {
        const regResArr = [
          mainlandReg.test(ele.idCard),

          HKReg.test(ele.idCard),

          MacaoReg.test(ele.idCard),

          TaiWangReg.test(ele.idCard),

          HKandMacaoPassReg.test(ele.idCard),

          HKLiveReg.test(ele.idCard),

          MacaoLiveReg.test(ele.idCard),

          TaiWangLiveReg.test(ele.idCard),
          PassPortReg.test(ele.idCard)
        ];
        return regResArr.some(e => e == true);
      });
  });
  //return;
  //}
  //ElMessage.warning("请输入正确的身份证");
};

const currentChange = (currentRow: any) => {
  const index = tableData.value.findIndex(ele => ele.idCard == currentRow.idCard);
  importSelectIndex.value = index + 1;
  dataObj.checkRow = currentRow;
};
const importDialogClose = () => {
  dataObj.checkRow = {};
  dataObj.tableData = [];
  dataObj.dialogSearchVal = "";
  dataObj.importSearchType = "idCard";
};
// 确定导入
const confirmImport = () => {
  if (JSON.stringify(dataObj.checkRow) === "{}") {
    ElMessage.warning("请选择病人导入");
    return;
  }

  postPatients({
    IdCard: dataObj.checkRow.idCard
  }).then(r => {
    const { data } = r;
    let editIndex: any = 0;
    let patientList = JSON.parse(JSON.stringify(dataObj.patientList));

    dataObj.patientList.some((item, index) => {
      if (item.idCard == data.idCard) {
        editIndex = index;
        return true;
      } else {
        editIndex = null;
      }
    });
    if (editIndex == null && patientList.length >= dataObj.pageSize) {
      patientList.splice(patientList.length - 1, 1);
    }
    if (editIndex !== null) {
      patientList.splice(editIndex, 1);
    }

    patientList.unshift(data);

    dataObj.patientList = patientList;
    dataObj.dialogTableVisible = false;
    ElMessage.success("导入成功");
    getPatientList();
  });
};

// 点击病人跳转体检信息页面
const rowClick = async (row: ViewPatient) => {
  await loadStorePatient(row.id);
  console.log(row)
  console.log(router.currentRoute.value)
  router.push({
    name: "healthExaminationInfo",
    query: {
      patientId: row.id,
      sex: row.sex
    }
  });
};

// 编辑按钮
const editBtnClick = (row: object, index: number) => {
  dataObj.editDialogShow = true;
  dataObj.editInfo = {
    ...row
  };
  dataObj.editIndex = index;
};

// 确定修改信息
const modifyInfo = () => {
  putPatients({
    Id: dataObj.editInfo.id,
    Phone: dataObj.editInfo.phone
  }).then(r => {
    dataObj.editDialogShow = false;
    ElMessage.success("修改成功");
    dataObj.patientList[dataObj.editIndex].phone = dataObj.editInfo.phone;
  });
};

const onRemovePatientClick = (id: number, index: number) => {
  if (!id && id != 0) {
    return;
  }

  ElMessageBox.confirm("该操作将永久删除该条数据，是否继续？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      requestDeletePatient(id)
        .then(r => {
          getPatientList();
          ElMessage.success("已删除");
        })
        .catch(err => {
          console.error(err);
          ElMessage.error("删除失败");
        });
    })
    .catch(e => {});
};

let {
  searchVal,
  patientList,
  dialogTableVisible,
  dialogSearchVal,
  total,
  currentPage,
  pageSize,
  editDialogShow,
  editInfo,
  importSearchType,
  importSearchTypeList,
  tableData
} = toRefs(dataObj);

onMounted(async () => {
  getPatientList();
});
const importSelectIndex = ref();
</script>
<template>
  <Page class="patientList_page" child-route-name="healthExaminationInfo">
    <header>
      <!-- 搜索输入框 -->
      <div class="search-section">
        <el-input
          @clear="searchPatient"
          v-model="searchVal"
          clearable
          @keyup.enter.native="searchPatient"
          placeholder="请输入身份证、手机号搜索"
        />
      </div>

      <!-- 搜索操作按钮 -->
      <div class="search-actions">
        <el-button type="primary" @click="searchPatient">搜索</el-button>
      </div>

      <!-- 头部其他操作按钮 -->
      <div class="header-actions">
        <el-button type="primary" plain @click="importBtnClick">导入</el-button>
      </div>
    </header>
    <!-- 病人列表 -->
    <div class="patientList_wrap">
      <el-table :data="patientList" height="100%" @row-click="rowClick">
        <el-table-column width="70" label="序号">
          <template #default="scope">
            <div>
              {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="patientId" label="病人号" width="100" /> -->
        <el-table-column prop="patientIdToStr" label="病人号" width="100" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="sex" label="性别" width="60" />
        <el-table-column prop="age" label="年龄" width="60" />
        <el-table-column label="出生日期" width="160">
          <template #default="scope">
            <div>
              <!-- {{ dateSubT(scope.row.birthday) }} -->
              {{ moment(scope.row.birthday).format("YYYY-MM-DD") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" width="115" />
        <el-table-column prop="address" label="地址" />
        <el-table-column label="操作" width="240">
          <template #default="scope">
            <div>
              <el-button
                type="primary"
                plain
                text
                bg
                @click.stop="editBtnClick(scope.row, scope.$index)"
                >编辑</el-button
              >
              <el-button
                type="danger"
                plain
                text
                bg
                @click.stop="onRemovePatientClick(scope.row.id, scope.$index)"
                >删除</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页组件 -->
    <!-- <div class="page_group"> -->
    <el-pagination
      background
      layout="prev, pager, next"
      :total="total"
      v-model:currentPage="currentPage"
      v-model:page-size="pageSize"
      @current-change="handleCurrentChange"
      class="pagination"
    />
    <!-- </div> -->

    <!-- 编辑弹窗 -->
    <el-dialog v-model="editDialogShow" title="编辑">
      <el-form :model="editInfo">
        <el-form-item label="病人号" :label-width="80">
          <el-input v-model="editInfo.patientId" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="姓名" :label-width="80">
          <el-input v-model="editInfo.name" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="性别" :label-width="80">
          <el-input v-model="editInfo.sex" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="年龄" :label-width="80">
          <el-input v-model="editInfo.age" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="出生日期" :label-width="80">
          <el-input v-model="editInfo.birthday" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="身份证" :label-width="80">
          <el-input v-model="editInfo.idCard" disabled autocomplete="off" />
        </el-form-item>
        <el-form-item label="手机号" :label-width="80">
          <el-input v-model="editInfo.phone" autocomplete="off" />
        </el-form-item>
        <el-form-item label="地址" :label-width="80">
          <el-input v-model="editInfo.address" disabled autocomplete="off" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogShow = false">取消</el-button>
          <el-button type="primary" @click="modifyInfo">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入弹窗 -->
    <el-dialog v-model="dialogTableVisible" @close="importDialogClose" title="病人信息" width="80%">
      <div class="dialog_tbody">
        <div class="dialog_head">
          <el-select
            style="margin-right: 10px; width: 150px"
            v-model="importSearchType"
            size="large"
          >
            <el-option
              v-for="item in importSearchTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-input
            clearable
            v-model.trim="dialogSearchVal"
            placeholder="请输入身份证查询"
            @keyup.enter.prevent="previewBtn"
            style="margin-right: 10px"
          />
          <el-button type="primary" @click="previewBtn">查询</el-button>
        </div>
        <el-table
          ref="singleTableRef"
          @current-change="currentChange"
          :data="tableData"
          style="width: 100%"
          height="400"
          highlight-current-row
        >
          <el-table-column prop="patientName" label="序号" width="60" align="center">
            <template #default="{ row, column, $index }">
              <el-radio-group v-model="importSelectIndex" class="ml-4">
                <el-radio :label="$index + 1"> </el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column prop="patientIdToStr" label="病人号" width="100" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="sex" label="性别" width="60" />
          <el-table-column prop="age" label="年龄" width="60">
            <template #default="scope">
              {{ birthdayConversionAge(scope.row.birthday) }}
            </template>
          </el-table-column>
          <el-table-column prop="birthday" label="出生日期" width="100">
            <template #default="scope">
              {{ moment(scope.row.birthday).format("YYYY-MM-DD") }}
            </template>
          </el-table-column>

          <el-table-column prop="phone" label="手机号" width="110" />
          <el-table-column prop="address" label="地址" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogTableVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmImport">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </Page>
</template>

<style lang="less" scoped>
.patientList_page {
  display: flex;
  flex-direction: column;

  header {
    display: flex;
    justify-content: flex-start;
    gap: 10px;

    .search-section {
      width: 350px;
    }

    .search-actions,
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .patientList_wrap {
    flex: 1;
    flex-shrink: 0;
    overflow: auto;
  }

  // .page_group {
  //   text-align: center;
  //   margin-top: 5px;
  // }
  .pagination {
    margin: 8px auto 8px;
  }

  .dialog_tbody {
    // height: 300px;

    .dialog_head {
      display: flex;
    }

    li {
      font-size: 16px;
      line-height: 30px;
      display: flex;
      color: #e6a23c;

      label {
        width: 80px;
        color: #909399;
      }
    }
  }
}
</style>
