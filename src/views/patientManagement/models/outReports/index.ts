//院外报告

import {
  requestAddOutReport,
  requestGetOutReportsByPatient,
  requestUploadFile,
  requestDeleteOutReport
} from "@/apis/outReports";
import {
  OutReport,
  OutReportFile,
  OutReportLink,
  UserOutReport,
  UserOutReports
} from "@/types/outReports";

export const useOutReports = (patientId: number) => {
  const userOutReports = ref<UserOutReports>({
    patientId,
    outReports: []
  });

  async function saveOutReport(_outReportFile: OutReportFile) {
    const link = await persistReportFile(_outReportFile);

    await persistReport({
      patientId,
      file: _outReportFile,
      link
    });
  }

  //persistent
  async function persistReportFile(_outReportFile: OutReportFile): Promise<OutReportLink> {
    const res = await requestUploadFile(_outReportFile);
    return res;
  }

  async function persistReport(userOutReport: UserOutReport) {
    await requestAddOutReport(userOutReport);
  }

  async function loadUserOutReports(_patientId?: number) {
    const reports: UserOutReports = await requestGetOutReportsByPatient(_patientId || patientId);

    userOutReports.value = reports;
  }

  async function removeOutReport(outReportId: OutReport["id"]) {
    if (outReportId) {
      await requestDeleteOutReport(outReportId);
    }
  }

  return {
    userOutReports,

    saveOutReport,
    removeOutReport,
    loadUserOutReports
  };
};
