<script lang="ts">
interface OrganMsg {
  name: string;
  abnormalInformation?: string;
}
interface InspectionProjectResult {
  date: string;
  itemUnit: string;
  itemValue: string;
  normalFlagName: string;
}
interface ExamineProjectResult {
  date: string;
  imagUrl: string;
  resultDescex: string;
  seedescex: string;
}
interface InspectionChildren {
  id: string;
  isCheck: boolean;
  itemCode: string;
  itemName: string;
  label: string;
  normalRefValueText: string | undefined;
  projectCode: string;
  projectName: string;
  projectResult: Record<string, InspectionProjectResult>;
  tableShow: number | boolean;
  valTable: [];
  isPrintChart: boolean;
}
interface ExamineChildren {
  id: string;
  isCheck: boolean;
  itemCode: string;
  itemName: string;
  label: string;
  normalRefValueText: string | undefined;
  projectCode: string;
  projectName: string;
  projectResult: Record<string, ExamineProjectResult>;
  tableShow: number | boolean;
  valTable: [];
  isPrintChart: boolean;
}
// 检验的原始数据类型
interface InspectionData {
  allCheck: boolean;
  checkComb: InspectionChildren[];
  // checkCombCode: string[];
  children: InspectionChildren[];
  isIndeterminate: boolean;
  itemCode: string;
  label: string;
}
// 检查的原始数据类型
interface ExamineData {
  allCheck: boolean;
  checkComb: ExamineChildren[];
  checkCombCode: string[];
  children: ExamineChildren[];
  isIndeterminate: boolean;
  itemCode: string;
  label: string;
}
export interface QiGuan {
  path_fill: string;
  options: QiGuanOtionItem[];
}
export interface QiGuanOtionItem {
  id: string;
  name: string;
  viewBoxWidth: Number;
  viewBoxHeight: Number;
  top: string;
  left: string;
  width: string;
  buttonPosition: string;
  path_d: string;
  color?: string;
}
</script>

<script lang="ts" setup>
import { Delete } from "@element-plus/icons-vue";
import { requestGetAccountOfDoctor } from "@/apis/account";
import MedicalRecord from "./components/MedicalRecord.vue";
import { organsData } from "@/assets/HealthRecord_svg.json";
import Inspections from "./components/inspections/index.vue";
import HealthRecord from "./components/HealthRecord.vue";
import SummaryComponent from "./components/Summary.vue";
import ReportsComponent from "./components/reportsComponent/index.vue";
import InspectionDetails from "./components/inspectionDetails/index.vue";
import ExamineDetails from "./components/examineDetails/index.vue";
import InspectionDetailsForPrint from "./components/inspectionDetailsForPrint/index.vue";
import ExamineDetailsForPrint from "./components/examineDetailsForPrint/index.vue";

import { useStore } from "@/store";

import Woman from "./components/Woman.vue";

import logo from "@/assets/logo_green.png";

import { Summary } from "@/types/summary";
import { useFileDialog } from "@vueuse/core";

import { useInspectionDetailPrint } from "./components/inspectionDetails/models/useInspectionDetailPrint";
import { useExamineDetailPrint } from "./components/examineDetails/models/useExamineDetailPrint";
import { useOutReports } from "./models/outReports";
import { OutReport } from "@/types/outReports";
import { useStorePatient } from "@/store/patient";
import { format } from "date-fns";
import { getAge } from "@/utils/human";
import InspectionsForPrintComponent from "./components/inspectionsForPrint/index.vue";
import { useStoreInspectionsForPrint } from "@/store/storeInspectionsForPrint";
import Examines from "./components/examines/index.vue";
import ExaminesForPrintComponent from "./components/examinesForPrint/index.vue";
import { useStoreExaminesForPrint } from "@/store/storeExaminesForPrint";
const { getInspectionDetailPrint } = useInspectionDetailPrint();
const { getExamineDetailPrint } = useExamineDetailPrint();

interface Organ {
  name: string;
  id: string;
}
 

const store = useStore();

const printRef = ref();
const { storePatientId, storePatient } = useStorePatient();

const patientInfo = computed(() => storePatient.value!);
// 出生日期
const birthdayString = computed(() => {
  if (storePatient.value !== null) {
    return format(storePatient.value.birthday, "yyyy-MM-dd");
  }
});
// 报告日期，默认取当天
const today = ref(format(new Date(), "yyyy-MM-dd"));

const age = computed(() => {
  if (storePatient.value !== null) {
    return getAge(storePatient.value.birthday);
  }
});

const loading = ref(false);

const activeName = ref("healthRecord");

// 患者综合视图
const onPatientViewBtnClick = () => {
  requestGetAccountOfDoctor().then(res => {
    const { roleId } = res.data;
    window.open(
      `http://192.168.10.134:8001/cdr/login/loginInpatientIntegrated.html?userId=${roleId}&styleId=&visitOrdNo=123456&patientId=${storePatientId}&domainId=01&inpatientType=02&sceneType=11`
    );
  });
};

const { storeInspectionsForPrint } = useStoreInspectionsForPrint();

const printDialogVisible = ref(false);
// 打印的配置
const printConfig = {
  id: "print-report",
  popTitle: "", // 打印配置页上方的标题
  extraHead: "", // 最上方的头部文字，附加在head标签上的额外标签，使用逗号分割
  preview: false, // 是否启动预览模式，默认是false
  previewTitle: "", // 打印预览的标题
  previewPrintBtnLabel: "预览结束，开始打印", // 打印预览的标题下方的按钮文本，点击可进入打印
  zIndex: 20002, // 预览窗口的z-index，默认是20002，最好比默认值更高
  previewBeforeOpenCallback() {}, // 预览窗口打开之前的callback
  previewOpenCallback() {}, // 预览窗口打开时的callback
  beforeOpenCallback() {}, // 开始打印之前的callback
  openCallback() {}, // 调用打印时的callback
  closeCallback() {}, // 关闭打印的callback(无法区分确认or取消)
  clickMounted() {},
  // url: 'http://localhost:8080/', // 打印指定的URL，确保同源策略相同
  // asyncUrl (reslove) {
  //   setTimeout(() => {
  //     reslove('http://localhost:8080/')
  //   }, 2000)
  // },
  standard: "",
  extarCss: ``
};

const isShowOrgans = ref<string[]>([]);

function printExamine() {
  if (storeExamineData.value.length) {
  }
}

function printInspectionsAndExamine() {
  printDialogVisible.value = true;
  printExamine();
  nextTick(() => {
    humanRef.value.getLiens();
  });
}

function onPrintClickBtn() {
  printInspectionsAndExamine();
  loadUserOutReports();
}

//#region 检查
const storeExamineData = ref<ExamineData[]>([]);

const { storeExaminesForPrint } = useStoreExaminesForPrint();

//#endregion
const storeSummaries = computed(() => store.state.summaryRecords);

//#region 人体器官异常信息
const patientOrganInfo = ref<OrganMsg[]>([]);

// 监听store里的patientOrganData，实时更新到报告里面的数据
watch(
  () => store.state.patientOrganData,
  () => {
    store.state.patientOrganData.forEach(ele => {
      patientOrganInfo.value.push(ele);
    });
  },
  {
    deep: true
  }
);
//#endregion
const printAtSummary = ref<Summary>();
const onSummaryPrintClick = (s: Summary) => {
  printAtSummary.value = s;
  printInspectionsAndExamine();
};
function initPrintAtSummary() {
  if (!storeSummaries.value.length) {
    return;
  }
  printAtSummary.value = storeSummaries.value[0];
}

const router = useRouter();

function onCheckFollowUpBtnClick() {
  router.push({
    name: "followUpUserDetail",
    params: {
      id: patientInfo.value.id
    },
    query: {
      pageName: `${patientInfo.value!.name}随访用户详情`
    }
  });
}
const selectedOrganIds = ref<string[]>([]);
function loadSelectedOrgans() {
  selectedOrganIds.value = organOptions.value.map(ele => ele.id);
}
const organOptions = ref<Organ[]>([]);
function loadOrganOptions() {
  organOptions.value = organsData.options.reduce<Organ[]>((result, organ) => {
    const isMalePatient = patientInfo.value?.sex === "男";
    const isExcludedOrgan = organ.name === "子宫";

    if (isMalePatient && isExcludedOrgan) {
      return result;
    }

    result.push({
      name: organ.name,
      id: organ.id
    });

    return result;
  }, []);
}

const isShowSelectedOrgans = ref(false);
const onConfirmSelectOrganClick = () => {
  isShowSelectedOrgans.value = false;
};
const onSelectOrganClick = () => {
  isShowSelectedOrgans.value = true;
};
const humanRef = ref();

watch(
  () => selectedOrganIds.value,
  () => {
    isShowOrgans.value = organsData.options.reduce<string[]>((sum, cur) => {
      if (selectedOrganIds.value.some(ele => ele == cur.id)) {
        sum.push(cur.name);
      }
      return sum;
    }, []);
  },
  { deep: true }
);

//#region 外院报告
const { onChange, open, reset } = useFileDialog({
  multiple: false,
  accept: "image/*"
});
const { userOutReports, saveOutReport, loadUserOutReports, removeOutReport } = useOutReports(
  storePatientId.value
);
const previewOutReports = computed(() => {
  return userOutReports.value.outReports.map(ele => {
    return {
      ...ele,
      url: `${import.meta.env.VITE_APP_API_URL}${ele.link}`
    };
  });
});
const previewOutReportsLinks = computed(() => {
  return previewOutReports.value.map(ele => ele.url);
});

function onPreviewAddImageClick() {
  onChange(async fs => {
    if (fs != null && fs.item(0) != null) {
      const file = fs.item(0);
      if (file) {
        await saveOutReport(file);
        await loadUserOutReports();
        reset();
      }
    }
  });
  open();
}
async function deletedImage(outReport: OutReport) {
  await ElMessageBox.confirm("确定此图片吗？", "警告", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning"
  });
  await removeOutReport(outReport.id);
  await loadUserOutReports();
}
const currentDeletedImageIndex = ref<number | null>(null);
function updateCurrentDeletedImageIndex(index: number | null) {
  currentDeletedImageIndex.value = index;
}
//#endregion 外院报告

onMounted(  () => {
    loadOrganOptions();
  loadSelectedOrgans();
  initPrintAtSummary();
});

// 使用计算属性来处理可能为空的情况
const printContent = computed({
  get: () => printAtSummary.value?.content ?? "",
  set: newValue => {
    if (printAtSummary.value) {
      printAtSummary.value.content = newValue;
    }
  }
});
</script>
<template>
  <div class="healthExaminationInfo_page">
    <div class="healthExaminationInfo_page__operation">
      <el-button type="primary" text bg @click="onSelectOrganClick">配置报告部位</el-button>
      <el-button type="primary" @click="onPrintClickBtn">健康报告</el-button>
      <el-button type="primary" text bg @click="onPatientViewBtnClick">患者综合视图</el-button>
      <el-button type="primary" text bg size="default" @click="onCheckFollowUpBtnClick"
        >随访任务</el-button
      >
    </div>
    <el-tabs v-model="activeName" class="demo_tabs">
      <el-tab-pane label="健康档案" name="healthRecord">
        <HealthRecord />
      </el-tab-pane>
      <el-tab-pane label="病历" name="medicalRecord">
        <MedicalRecord />
      </el-tab-pane>
      <el-tab-pane label="检查" name="first" v-loading="loading">
        <Examines></Examines>
      </el-tab-pane>
      <el-tab-pane label="检查明细对比" name="examineDetails" v-loading="loading">
        <ExamineDetails ref="examineDetailsRef" :patientId="patientInfo.id" />
      </el-tab-pane>
      <el-tab-pane label="检验" name="second" v-loading="loading">
        <Inspections ref="inspectionsRef" :patientId="patientInfo.id"></Inspections>
      </el-tab-pane>
      <el-tab-pane label="检验明细对比" name="inspectionDetails" v-loading="loading">
        <InspectionDetails ref="inspectionDetailsRef" :patientId="patientInfo.id" />
      </el-tab-pane>
      <el-tab-pane label="小结" name="third" v-loading="loading">
        <SummaryComponent @print="onSummaryPrintClick" />
      </el-tab-pane>
      <el-tab-pane label="上传健康报告" name="uploadReports" v-loading="loading">
        <ReportsComponent />
      </el-tab-pane>
    </el-tabs>

    <!-- 打印 -->
    <!-- class="custom-dialog" -->
    <el-dialog title="打印预览" width="50%" v-model="printDialogVisible">
      <div style="height: 40px">
        选择报告日期
        <el-date-picker
          v-model="today"
          type="date"
          placeholder="选择报告日期"
          value-format="YYYY-MM-DD"
          format="YYYY-MM-DD"
        />
      </div>
      <div class="print-warp" id="print-report" ref="printRef">
        <!-- 封面 -->
        <div style="page-break-after: always" class="first-page">
          <div class="block-header">
            <div class="header-top">
              <div class="block1"></div>
              <div class="block2"></div>
              <div class="block3"></div>
              <div class="block-title">Personal Health Report</div>
            </div>
            <div class="title">个人健康报告</div>
          </div>
          <div class="content">
            <div style="margin: 20px 0">
              <span class="content-label">病人编号</span>
              <span class="content-value">{{ patientInfo.id }}</span>
            </div>
            <div style="margin: 20px 0">
              <span class="content-label">姓名</span>
              <span class="content-value">{{ patientInfo!.name }}</span>
            </div>
            <div style="margin: 20px 0">
              <span class="content-label">性别</span>
              <span class="content-value">{{ patientInfo!.sex }}</span>
            </div>
            <div style="margin: 20px 0">
              <span class="content-label">出生日期</span>
              <span class="content-value">{{ birthdayString }} </span>
            </div>
            <div style="margin: 20px 0">
              <span class="content-label">年龄</span>
              <span class="content-value">{{ age }}</span>
            </div>
            <div style="margin: 20px 0">
              <span class="content-label">报告日期</span>
              <span class="content-value">{{ today }}</span>
            </div>
          </div>
          <div class="logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
        </div>

        <div style="page-break-after: always" id="human-id">
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">人体部位图</div>
          <div style="margin-top: 80px; overflow: hidden">
            <Woman
              class="human-wrap"
              ref="humanRef"
              v-model:isShowOrgans="isShowOrgans"
              :abnormal-organs="patientOrganInfo.map((ele: any) => ele.name)"
            ></Woman>
          </div>
        </div>

        <!-- 检验 -->
        <div
          style="page-break-after: always"
          id="print_table_batch"
          v-if="storeInspectionsForPrint.size > 0"
        >
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">检验结果</div>
          <InspectionsForPrintComponent></InspectionsForPrintComponent>
        </div>
        <!-- 检验明细 -->
        <div
          style="page-break-after: always"
          id="print-inspection"
          v-if="getInspectionDetailPrint().length > 0"
        >
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">检验明细</div>
          <InspectionDetailsForPrint></InspectionDetailsForPrint>
        </div>
        <!-- 检查 -->
        <div
          style="page-break-after: always"
          id="print_check"
          v-if="storeExaminesForPrint.size > 0"
        >
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">检查结果</div>
          <ExaminesForPrintComponent />
        </div>
        <!-- 检查明细 -->
        <div
          style="page-break-after: always"
          id="print-examine-detail"
          v-if="getExamineDetailPrint().length > 0"
        >
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">检查明细</div>
          <ExamineDetailsForPrint></ExamineDetailsForPrint>
        </div>

        <!-- 附加图片 -->
        <div
          v-if="userOutReports.outReports.length > 0"
          style="page-break-after: auto"
          id="images-id"
        >
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">
            <div style="width: 100%">附加资料</div>
          </div>

          <template v-for="(outReport, outReportIndex) in previewOutReports">
            <div
              style="position: relative"
              @mouseover="updateCurrentDeletedImageIndex(outReportIndex)"
              @mouseleave="updateCurrentDeletedImageIndex(null)"
            >
              <el-image
                style="height: 100%"
                :src="outReport.url"
                fit="contain"
                class="image-item"
                :preview-src-list="previewOutReportsLinks"
                :initial-index="currentDeletedImageIndex || outReportIndex"
                loading="lazy"
              />
              <!-- type="danger" -->
              <el-button
                v-if="currentDeletedImageIndex == outReportIndex"
                style="position: absolute; top: 10px; right: 10px; height: 40px; width: 40px"
                @click="deletedImage(outReport)"
                :icon="Delete"
                type="danger"
                text
                bg
                circle
                plain
              >
              </el-button>
            </div>
          </template>
        </div>
        <!-- 异常数据表格数据汇总 -->
        <div style="page-break-after: always" id="print_check" v-if="patientOrganInfo.length > 0">
          <!-- <h2>异常数据表格数据汇总</h2> -->
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">异常数据汇总</div>
          <div>
            <el-table
              :data="patientOrganInfo"
              header-row-class-name="table-first-row"
              style="width: 100%; margin-top: 20px"
              border
            >
              <el-table-column
                type="index"
                class-name="table-index"
                label-class-name="table-index-first"
                width="50"
                align="center"
              />
              <el-table-column label="部位" prop="name" width="100" />
              <el-table-column label="异常信息" prop="abnormalInformation" width="524" />
            </el-table>
          </div>
        </div>
        <!-- 病人小结 -->
        <div
          v-if="store.state.summaryRecords.length > 0 && printAtSummary?.content"
          style="page-break-after: always"
          id="summary-id"
        >
          <div class="header-logo">
            <el-image style="height: 100%" :src="logo" fit="contain" />
          </div>
          <div class="header-describe">病情小结</div>
          <el-card shadow="never" class="summary-item-wrap">
            <PreviewWangEdit v-model="printContent"></PreviewWangEdit>
          </el-card>
        </div>
      </div>
      <template #footer>
        <span>
          <el-button @click="printDialogVisible = false">取消</el-button>
          <el-button @click="onPreviewAddImageClick">添加外院报告图</el-button>
          <el-button type="primary" v-print="printConfig">打印</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="编辑" width="40%" v-model="isShowSelectedOrgans">
      <div class="select-qiguan-wrap">
        <el-checkbox-group v-model="selectedOrganIds">
          <el-checkbox v-for="item in organOptions" :key="item.id" :value="item.id">{{
            item.name
          }}</el-checkbox>
        </el-checkbox-group>
      </div>

      <template #footer>
        <span
          ><el-button @click="isShowSelectedOrgans = false">取消</el-button>
          <el-button type="primary" @click="onConfirmSelectOrganClick">确认</el-button></span
        >
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
@media print {
  @page {
    // margin: 10mm 16mm;
    // margin-bottom: 0mm;
    // margin-top: 8mm;

    size: B5(JIS);
    // 此处调整使其只显示页脚中的页码，具体根据页面调整
    // margin: 10mm 16mm;
    margin: 10mm 16mm;

    margin-bottom: 8mm;
    margin-top: 8mm;
  }
  .human-wrap {
    zoom: 0.86;
  }
  table {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
:deep(.print-dialog) {
  margin: 0 auto !important;
  height: 100% !important;
}
.brief_summary {
  color: #ff9800;
}
.page-number {
  margin-top: auto;
}
.human-wrap {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.human-wrap::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.inspectionList-label {
  font-family: PingFangSC-Semibold;
  font-size: 18px;
  color: #2d3436 !important;
  display: inline-block;
  text-indent: 23px;
}
.accordion_div__index {
  background: #138953;
  width: 47px;
  height: 38px;
  display: inline-flex;
  flex-direction: row;
  align-content: center;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-family: PingFangSC-Semibold;
  font-size: 18px;
}

:deep(.el-collapse-item__header) {
  font-family: PingFangSC-Semibold;
  font-size: 24px;
  color: #2d3436 !important;
}
.header-describe {
  background: #138953;
  width: 100%;
  height: 42px;
  font-family: PingFangSC-Semibold;
  font-size: 24px;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  margin-top: 45px;
  text-indent: 17px;
}
.header-logo {
  position: relative;
  // width: 154px;
  width: 100%;
  height: 24px;
}
.header-logo::after {
  content: "";
  width: 100%;
  height: 2px;
  background: #138953;
  position: absolute;
  top: 40px;
  left: 0px;
}
.logo {
  width: 270px;
  height: 42px;
  margin-top: 180px;
}
.first-page {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.content {
  margin-top: 70px;
}
.content-label {
  font-family: PingFangSC-Semibold;
  font-size: 22px;
  color: #2d3436;
  width: 144px;
  display: inline-block;
  text-align: center;
  letter-spacing: 4px;
  position: relative;
}
.content-value {
  font-family: PingFangSC-Regular;
  font-size: 18px;
  color: #2d3436;
  text-align: center;
  display: inline-block;
  width: 300px;
  border-bottom: 1px solid black;
}
.content-label::after {
  content: ":";
  position: absolute;
  right: 0px;
}
.title {
  font-family: PingFangSC-Semibold;
  font-size: 48px;
  color: #ffffff;
  margin: 0 auto;
}
.block-title {
  font-family: PingFangSC-Semibold;
  font-size: 22px;
  color: #ffffff;
  width: 40%;
  height: 40px;
  display: inline-block;
  text-align: center;
  vertical-align: bottom;
  position: relative;
}

.block-title::after {
  content: "";
  width: 120%;
  height: 2px;
  background: #fff;
  position: absolute;
  left: 19px;
  top: 34px;
}
.block-header {
  width: 100%;
  height: 250px;
  background: #138953;
  margin-top: 150px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: space-evenly;
}
.block1,
.block2,
.block3 {
  display: inline-block;
  background: white;
  margin-top: 20px;
  margin-right: 20px;
  height: 30px;
}
.block1 {
  width: 35%;
}
.block2 {
  width: 10px;
}
.block3 {
  width: 50px;
}

:deep(.custom-dialog > .el-dialog__body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  padding-bottom: 5rem !important;
  flex-direction: column;
}
:deep(.custom-dialog) {
  margin: 0 auto !important;
  height: 100% !important;
}
.print-warp {
  overflow-x: hidden;
  // display: flex;
  // flex-direction: column;
  // flex: 1;
}

.healthExaminationInfo_page {
  height: 100%;
  display: flex;
  flex-direction: column;
  // position: relative;
  .healthExaminationInfo_page__operation {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    // position: absolute;
    // right: 0;
    // top: 0;
    // z-index: 10;
  }
  .el-tab-pane {
    height: 100%;
    overflow: auto;
  }

  .demo_tabs {
    flex: 1;
    display: flex;
    overflow: auto;
    flex-direction: column-reverse;
  }

  .accordion_div h3 {
    text-align: center;
    margin: 0;
  }

  .p_flag {
    color: red;
  }
}
</style>
<style lang="less">
#print_table_batch table th {
  border: 1px solid #000;
}
.table-first-row {
  font-family: PingFangSC-Semibold;
  font-size: 18px;
  color: #2d3436;
}

.table-index {
  background: #138953 !important;
  color: #fff;
}
.table-index-first {
  background: #fff !important;
}
</style>

<style media="print_table_batch" lang="less" scoped>
@page {
  :deep(.el-table .cell) {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    line-height: 23px;
    padding: 0 12px;
  }
  .header-test {
    position: fixed;
    top: 0;
  }
  /* this affects the margin in the printer settings */
}
#print_table_batch table th {
  border: 1px solid #000;
}
#print_table_batch {
  table {
    table-layout: fixed !important;
    border-collapse: collapse;
    width: 100%;
    border: none;
    td,
    th {
      border: 1px solid #ccc;
      padding: 5px;
    }

    tr {
      page-break-inside: avoid !important;
    }

    .p_flag {
      color: red;
    }
  }

  .el-collapse-item__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .accordion_div h3 {
    text-align: center;
    margin: 0;
  }
}
</style>

<style media="summary-id" lang="less" scoped>
.header-describe {
  margin-bottom: 20px;
}
.summary-item-wrap {
  .label {
    text-align: center;
    font-weight: 600;
    color: var(--el-text-color-secondary);
  }
  :deep(.el-card__body) {
    // display: grid;
    // grid-template-columns: 100px auto;
    // align-items: center;
    // grid-gap: 10px;
    > .content {
      word-wrap: break-word;
      margin: 0;
    }
  }
}
</style>

<style media="print_check" lang="less" scoped>
@page {
  display: block;
  width: 100%;
  overflow: hidden;
  // margin: 1cm 1cm 1cm 1cm;
  size: auto;
}
#print_table_batch table th {
  border: 1px solid #000;
}
#print_check {
  .summary-timeline {
    margin: 10px 0;
  }
  .canvas_div div:last-child {
    display: none !important;
  }

  table {
    border: 1px solid #ccc;
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 15px;
  }

  tr {
    page-break-inside: avoid !important;
  }

  td,
  th {
    border: 1px solid #ccc;
    padding: 5px;
  }

  .p_flag {
    color: red;
  }

  .el-collapse-item__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .accordion_div h3 {
    text-align: center;
    margin: 0;
    height: auto !important;
  }

  .image_span {
    display: none !important;
  }
  .brief_summary {
    color: #ff9800;
  }
}
</style>
