<script lang="ts"></script>

<script lang="ts" setup>
import { requestGetDemands, CreateDemand, ResponseCreateDemand } from "@/apis/demands";

const route = useRoute();
const router = useRouter();

const patientPhone = computed(() => route.query.patientPhone as string);
const patientName = computed(() => route.query.patientName as string);
const patientId = computed(() => route.query.patientId as string);

const patientRequirements = ref<ResponseCreateDemand[]>([]);

const onGoFollowUoBtnCilck = () => {
  router.push({
    name: "followUpUserDetail",
    params: {
      id: patientId.value
    },
    query: {
      pageName: `${patientName.value}随访用户详情`
    }
  });
};

onMounted(async () => {
  const { data } = await requestGetDemands();
  patientRequirements.value = data.filter(ele => ele.patientPhone == patientPhone.value);
  console.log(patientRequirements.value);
});
</script>

<template>
  <div class="patient-requirements-wrap">
    <div class="patient-info">
      <h2>病人姓名：{{ patientName ?? "" }}</h2>
      <h2>手机号：{{ patientPhone ?? "" }}</h2>
      <el-button type="primary" size="default" @click="onGoFollowUoBtnCilck">随访跳转</el-button>
    </div>
    <div class="time-line">
      <el-timeline>
        <el-timeline-item
          v-for="item in patientRequirements"
          :timestamp="item.createTime"
          placement="top"
        >
          <el-card>
            <h4>内容：</h4>
            <p>{{ item.content }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>

<style lang="less" scoped>
.patient-requirements-wrap {
  display: flex;
  gap: 2rem;
  flex-direction: column;
  height: 100%;
  .time-line {
    flex: 1;
    overflow-y: scroll;
  }
  .patient-info {
    display: flex;

    align-items: center;
    gap: 5rem;
  }
}
</style>
