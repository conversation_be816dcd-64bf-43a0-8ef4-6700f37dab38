<script setup lang="ts">
import { body, organsData } from "@/assets/HealthRecord_svg.json";
import { ButtonInstance } from "element-plus";
const route = useRoute();

const props = defineProps<{
  abnormalOrgans: string[];
}>();

const emit = defineEmits<{
  (e: "click", name: string): void;
}>();

const { abnormalOrgans } = toRefs(props);
const qiguanRef = ref(organsData);
const lines = ref<
  {
    name: string;
    startPoint: [number, number];
    endPoint: [number, number];
  }[]
>([]);

const humanRef = ref<HTMLElement>();

const lineRefs = ref<HTMLElement[]>([]);

const buttonRefs = ref<ButtonInstance[]>([]);

const svgRefs = ref<HTMLElement[]>([]);

const buttonStyle = computed(() => (name: string) => {
  // 初始 无病 没激活
  const style = {
    "color": "var(--el-button-text-color)",
    "background-color": "var(--el-button-bg-color)",
    "border-color": "var(--el-button-border-color)"
  };
  // 没病且激活
  if (!isAbnormalOrgan(name) && isCurrentName(name)) {
    style.color = "var(--el-button-hover-text-color)";
    style["background-color"] = "var(--el-button-hover-bg-color)";
    style["border-color"] = "var(--el-button-hover-border-color)";
  }
  // 有病没激活
  if (isAbnormalOrgan(name) && !isCurrentName(name)) {
    style.color = "#fff";
    style["background-color"] = "red";
    style["border-color"] = "var(--el-color-danger)";
  }
  // 有病且激活
  if (isAbnormalOrgan(name) && isCurrentName(name)) {
    style.color = "#fff";
    style["background-color"] = "rgb(150, 0, 0)";
    style["border-color"] = "red";
  }
  return style;
});

const lineStyle = computed(() => (name: string) => {
  const style = {
    stroke: "#469966",
    strokeWidth: "2px",
    opacity: 0.6
  };

  if (isAbnormalOrgan(name)) {
    style.stroke = "red";
  }

  if (isCurrentName(name)) {
    style.strokeWidth = "4px";
    style.opacity = 1;
  }

  return style;
});

const organDivStyle = computed(() => {
  return (item: (typeof qiguanRef.value.options)[number]) => {
    const style = {
      top: item.top,
      left: item.left,
      width: item.width,
      opacity: 0.5,
      zIndex: 1
    };

    if (isCurrentName(item.id)) {
      style.zIndex = 9999;
    }
    return style;
  };
});

const organSvgStyle = computed(() => (name: string) => {
  const style = {
    stroke: "#18670d",
    strokeWidth: "0px",
    zIndex: 1
  };

  if (isAbnormalOrgan(name)) {
    style.stroke = "#f52023";
  }

  if (isCurrentName(name)) {
    style.strokeWidth = "30px";
    style.zIndex = 9999;
  }

  return style;
});

const currentName = ref("");

const isAbnormalOrgan = (name: string) => abnormalOrgans.value.some(ele => ele == name);

const isCurrentName = (name: string) => currentName.value == name;

const getLiens = () => {
  const humanDomOffsetLeft = humanRef.value?.offsetLeft;
  const getDomRelativePosition = (
    dom: HTMLElement,
    position: "rightCenter" | "leftCenter" | "center"
  ) => {
    const { offsetWidth, offsetHeight, offsetLeft, offsetTop } = dom;

    switch (position) {
      case "rightCenter":
        return [offsetWidth + offsetLeft, offsetHeight / 2 + offsetTop];
      case "leftCenter":
        return [offsetLeft + 15, offsetHeight / 2 + offsetTop];
      case "center":
        return [offsetWidth / 2 + offsetLeft, offsetHeight / 2 + offsetTop];
      default:
        throw new Error("position is error.");
    }
  };
  lines.value = qiguanRef.value.options.map(item => {
    const buttonDom: HTMLElement = buttonRefs.value?.find(
      i => i.$el.attributes.getNamedItem("name")?.value == item.name
    )?.$el!;

    const qiguanDom = svgRefs.value?.find(
      i => i.attributes.getNamedItem("name")?.value == item.name
    )!;
    const buttonDomRelativePosition = getDomRelativePosition(
      buttonDom,
      item.buttonPosition == "left" ? "rightCenter" : "leftCenter"
    );

    const qiguanDomRelativePosition = getDomRelativePosition(qiguanDom, "center");
    return {
      name: item.name,
      startPoint: [buttonDomRelativePosition[0], buttonDomRelativePosition[1]],
      endPoint: [qiguanDomRelativePosition[0] + humanDomOffsetLeft!, qiguanDomRelativePosition[1]]
    };
  });
};

const onBtnMoveOver = (name: string) => (currentName.value = name);

const onBtnMoveOut = () => (currentName.value = "");

const onOrganBtnClick = (name: string) => emit("click", name);

async function initPage() {
  getLiens();

  await nextTick();
  if (route.query.sex == "男") {
    svgRefs.value.find(ele => ele.id == "子宫")!.style.display = "none";
    lineRefs.value.find(ele => ele.id == "子宫")!.style.display = "none";
    buttonRefs.value.find(ele => ele.$el.id == "子宫")!.$el.style.display = "none";
    getLiens();
  } else {
    getLiens();
  }
}
onMounted(async () => {
  window.addEventListener("resize", initPage);
  // renderAll();
  await initPage();
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", initPage);
});
</script>
<template>
  <div class="chart">
    <svg id="lines" class="lines">
      <line
        v-for="item in lines"
        ref="lineRefs"
        :name="item.name"
        :x1="item.startPoint[0]"
        :y1="item.startPoint[1]"
        :x2="item.endPoint[0]"
        :y2="item.endPoint[1]"
        :style="lineStyle(item.name)"
        :id="item.name"
      />
    </svg>
    <div class="left button-wrap">
      <el-button
        v-for="item in qiguanRef.options.filter(ele => ele.buttonPosition == 'left')"
        ref="buttonRefs"
        :name="item.name"
        @mouseover="onBtnMoveOver(item.name)"
        @mouseout="onBtnMoveOut()"
        @click="onOrganBtnClick(item.name)"
        :style="buttonStyle(item.name)"
        type="success"
        plain
        :id="item.name"
      >
        {{ item.name }}</el-button
      >
      <el-button
        :style="buttonStyle('其他部位')"
        type="success"
        plain
        @click="onOrganBtnClick('其他部位')"
        >其他部位</el-button
      >
      <el-button
        :style="buttonStyle('实验室检查')"
        type="success"
        plain
        @click="onOrganBtnClick('实验室检查')"
        >实验室检查</el-button
      >
    </div>
    <div class="human" ref="humanRef">
      <svg style="height: 100%" viewBox="0 0 100 200">
        <path
          :d="body.path_d"
          :fill="body.path_fill"
          :style="{
            'z-index': 9
          }"
        />
      </svg>
      <div
        v-for="item in qiguanRef.options"
        ref="svgRefs"
        :name="item.name"
        style="position: absolute"
        :style="organDivStyle(item)"
        :id="item.name"
      >
        <svg :viewBox="`0 0 ${item.viewBoxWidth} ${item.viewBoxHeight}`">
          <path :d="item.path_d" :fill="item.color" :style="organSvgStyle(item.name)" />
        </svg>
      </div>
    </div>
    <div class="right button-wrap">
      <el-button
        v-for="item in qiguanRef.options.filter(ele => ele.buttonPosition == 'right')"
        ref="buttonRefs"
        :name="item.name"
        @mouseover="onBtnMoveOver(item.name)"
        @mouseout="onBtnMoveOut()"
        @click="onOrganBtnClick(item.name)"
        :style="buttonStyle(item.name)"
        type="success"
        plain
        :id="item.name"
      >
        {{ item.name }}</el-button
      >
      <el-button
        :style="buttonStyle('一般检查')"
        type="success"
        plain
        @click="onOrganBtnClick('一般检查')"
        >一般检查</el-button
      >
    </div>
  </div>
</template>

<style lang="less" scoped>
.right button:nth-child(1) {
  margin-left: 12px;
}
.left button:nth-child(1) {
  margin-left: 12px;
}

.chart {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  grid-template-rows: 1fr;
  align-items: center;
  justify-items: center;
  overflow-x: hidden;
  .lines {
    position: absolute;
    width: 100%;
    // height: 100%;
    height: 150%;

    top: 0;
    left: 0;
    z-index: 10;
  }
  > .human {
    height: 100%;
    overflow: hidden;
    position: relative;
    z-index: 9;
  }
  > .button-wrap {
    display: flex;
    flex-direction: column;
    gap: 50px;
    z-index: 11;
  }
  > .left {
    justify-self: flex-end;
  }
  > .right {
    justify-self: flex-start;
  }
}
</style>
