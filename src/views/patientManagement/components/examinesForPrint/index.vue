<script setup lang="ts">
import { useStoreExaminesForPrint } from "@/store/storeExaminesForPrint";
import { Examine } from "@/types/examine";
import { format } from "date-fns";
import { groupedToMap } from "@/utils/collection";
import { useStoreExaminesSort } from "@/store/storeExaminesSort";

defineOptions({
  name: "ExaminesForPrintComponent"
});

interface PrintItem {
  title: {
    itemClass: string;
    itemClassName: string;
  };
  examineGroups: ExamineGroup[];
}

interface ExamineGroup {
  itemCode: string;
  label: string;
  results: ExamineResult[];
}

interface ExamineResult {
  date: string;
  seedescex: string;
  resultDescex?: string;
}
const { storeExaminesForPrint } = useStoreExaminesForPrint();
const { storeExaminesSort, loadData } = useStoreExaminesSort();

const printItems = computed(() => {
  const result: PrintItem[] = [];

  if (!storeExaminesForPrint.value) return result;

  storeExaminesForPrint.value.forEach((innerMap, itemClass) => {
    if (!innerMap || innerMap.size === 0) return;

    const allExamines = Array.from(innerMap.values()).flat();
    if (!allExamines || allExamines.length === 0) return;

    const firstExamine = allExamines[0];
    if (!firstExamine) return;

    const item: PrintItem = {
      title: {
        itemClass,
        itemClassName: firstExamine.itemClassName || ""
      },
      examineGroups: createExamineGroups(innerMap)
    };

    result.push(item);
  });

  // 根据排序规则对整个检查大类进行排序
  if (storeExaminesSort.value?.detail) {
    result.sort((a, b) => {
      const aRule = storeExaminesSort.value!.detail.find(
        item => item.itemCode === a.title.itemClass
      );
      const bRule = storeExaminesSort.value!.detail.find(
        item => item.itemCode === b.title.itemClass
      );
      if (!aRule) return 1;
      if (!bRule) return -1;
      return bRule.sort - aRule.sort;
    });
  }

  return result;
});

function sortInspectionsByDate(examines: Examine[]) {
  return [...new Set(examines.map(i => i.reportDate))].sort(
    (a, b) => new Date(a).getTime() - new Date(b).getTime()
  );
}

function createExamineGroups(innerMap: Map<string, Examine[]>) {
  const groups: ExamineGroup[] = [];

  if (!innerMap) return groups;

  innerMap.forEach((examines, itemCode) => {
    if (!examines || examines.length === 0) return;

    const firstExamine = examines[0];
    if (!firstExamine) return;

    // 使用 groupedToMap 按日期分组
    const examinesByDate = groupedToMap(examines, "reportDate");
    const sortedDates = sortInspectionsByDate(examines);

    const group: ExamineGroup = {
      itemCode,
      label: firstExamine.itemName,
      results: sortedDates.map(date => {
        const examineList = examinesByDate.get(date);
        const examine = examineList?.[0];
        return {
          date,
          seedescex: examine?.seedescex || "",
          resultDescex: examine?.resultDescex || undefined
        };
      })
    };

    groups.push(group);
  });
  //排序
  if (storeExaminesSort.value?.detail) {
    groups.sort((a, b) => {
      const aRule = storeExaminesSort.value!.detail.find(item => item.itemCode === a.itemCode);
      const bRule = storeExaminesSort.value!.detail.find(item => item.itemCode === b.itemCode);
      if (!aRule) return 1;
      if (!bRule) return -1;
      return bRule.sort - aRule.sort;
    });
  }
  return groups;
}

const formatDate = (date: string) => {
  return format(new Date(date), "yyyy-MM-dd");
};

onMounted(async () => {
  await loadData();
});
</script>

<template>
  <div class="examines-for-print">
    <div class="examine-group" v-for="item in printItems" :key="item.title.itemClass">
      <!-- 检查大类标题 -->
      <div class="examine-group-title">{{ item.title.itemClassName }}</div>

      <!-- 检查项目列表 -->
      <div class="examine-item" v-for="(group, index) in item.examineGroups" :key="group.itemCode">
        <div class="examine-item-header">
          <span class="examine-index">{{ index + 1 }}</span>
          <span class="examine-label">{{ group.label }}</span>
        </div>

        <table class="examine-table">
          <thead>
            <tr>
              <th style="width: 30px">日期</th>
              <td v-for="result in group.results" :key="result.date">
                {{ formatDate(result.date) }}
              </td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th style="width: 30px">结果</th>
              <td v-for="result in group.results" :key="result.date">
                <p>{{ result.seedescex }}</p>
                <p class="brief-summary" v-if="result.resultDescex">
                  小结：{{ result.resultDescex }}
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.examines-for-print {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;

  .examine-group {
    page-break-inside: avoid;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .examine-group-title {
      font-size: 24px;
      color: #2d3436;
    }
  }

  .examine-item {
    page-break-inside: avoid;

    .examine-item-header {
      border: 1px solid #000;

      .examine-index {
        background: #138953;
        width: 47px;
        height: 38px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        color: #ffffff;
        font-family: PingFangSC-Semibold;
        font-size: 18px;
      }

      .examine-label {
        font-family: PingFangSC-Semibold;
        font-size: 18px;
        color: #2d3436;
        display: inline-block;
        padding-left: 23px;
      }
    }

    .examine-table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;

      th,
      td {
        border: 1px solid #ccc;
        padding: 8px;
        text-align: left;
      }

      th {
        background-color: #f5f7fa;
        font-weight: bold;
      }

      .brief-summary {
        color: #ff9800;
        margin-top: 8px;
      }
    }
  }
}

@media print {
  .examines-for-print {
    .examine-table {
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }
  }
}
</style>
