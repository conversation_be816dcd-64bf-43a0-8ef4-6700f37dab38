<script lang="ts" setup>
import { useDialog } from "@/composables/useDialog";
import { useReports } from "./useReports";
import { Report } from "@/types/reports";
import UploadPDFComponent from "@/components/UploadPDF.vue";
import { requestUploadReportFile, requestAddReports } from "@/apis/reports";
import { urlToFullUrl } from "@/utils";
import { useThrottleFn } from "@vueuse/core";
import moment from "moment";
import { useRoute } from "vue-router";
import { FormRules } from "element-plus";

defineOptions({
  name: "ReportsComponent"
});
const patientId = computed<number>(() => {
  return Number(route.query.patientId) ?? 0;
});
const {
  dialog: reportDialog,
  openDialog: openReportDialog,
  closeDialog: closeReportDialog
} = useDialog();

const isSaving = ref(false);
const route = useRoute();

//#region 报告列表

const { reports, loadReports, removeItem } = useReports(patientId.value);

const reportsFiltered = computed(() => {
  if (reports.value.length == 0) {
    return [];
  }
  return reports.value.filter(r => r.title.includes(filterCondition.value));
});

const filterCondition = ref("");
const throttledResetFiltersFn = useThrottleFn(() => {
  loadReports();
}, 1000);

function resetFilters() {
  filterCondition.value = "";

  throttledResetFiltersFn();
}

function onResetFilters() {
  resetFilters();
}

function onReportDialogClose() {
  report.value.title = "";
  report.value.url = "";
  closeReportDialog();
  isSaving.value = false;
}

function onAddReport() {
  report.value.isReadOnly = false;
  openReportDialog();
}

async function onReportsRemove(id: Report["id"]) {
  const isConfirm = await onReportDialogConfirm();
  if (!isConfirm) return;
  try {
    await removeItem(id);
    ElMessage.success("已删除");
    loadReports();
  } catch (error) {
    ElMessage.error(`${error}`);
  }
}

async function onReportDialogConfirm(): Promise<boolean> {
  try {
    await ElMessageBox.confirm("删除不可逆 是否继续", "警告", {
      confirmButtonText: "删除",
      cancelButtonText: "取消操作",
      type: "warning"
    });
    return true;
  } catch (error) {
    return false;
  }
}
//#endregion

//#region 报告操作

const reportFormRef = ref();
const report = ref({
  title: "",
  isReadOnly: false,
  url: ""
});

const rules = reactive<FormRules<typeof reportFormRef>>({
  title: [
    {
      validator: (_: any, value: string, cb: Function) => {
        if (!value.trim()) {
          cb("请输入标题");
        } else {
          cb();
        }
      },
      trigger: "change",
      required: true
    }
  ]
});

function onReportsItemClick({ url, title }: Report) {
  report.value = {
    url: urlToFullUrl(url),
    title: title || moment().format("YYYY-MM-DD HH:mm:ss"),
    isReadOnly: true
  };
  openReportDialog();
}

const currentReportFile = ref<File>();

function fileChange(file: File) {
  currentReportFile.value = file;
}

async function onSaveReport() {
  if (!currentReportFile.value) return;
  isSaving.value = true;
  const url = await uploadReport(currentReportFile.value);
  try {
    await requestAddReports({
      time: moment().toDate(),
      title: report.value.title.trim(),
      patientId: patientId.value,
      url
    });
    ElMessage.success("已保存");
  } catch (error) {
    ElMessage.error("保存失败");
  } finally {
    closeReportDialog();
    loadReports();
    isSaving.value = false;
  }
}

async function uploadReport(file: File) {
  const { url } = await requestUploadReportFile(file);
  return url;
}
//#endregion
onMounted(() => {
  loadReports();
});
</script>

<template>
  <div class="reports-component-page">
    <div class="reports-component-page__header">
      <div class="page__header__filters">
        <div class="page__header__filter">
          <el-input
            v-model="filterCondition"
            placeholder="请输入报告名称"
            clearable
            @clear="resetFilters"
            style="width: 220px"
          />
        </div>
      </div>

      <el-button type="primary" text bg @click="onResetFilters">重置</el-button>
    </div>
    <div class="reports-component-page__tool">
      <el-button type="primary" @click="onAddReport"> 新建 </el-button>
    </div>
    <el-table :data="reportsFiltered" style="width: 100%" height="100%">
      <el-table-column prop="title" label="报告标题" min-width="180" />
      <el-table-column prop="time" label="上传时间" width="180">
        <template #default="{ row }">
          <div>{{ row.time }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-button type="primary" bg text @click="onReportsItemClick(row)">查看</el-button>
          <el-button type="danger" bg text @click="onReportsRemove(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="reportDialog.visible"
      :title="reportDialog.title"
      width="800"
      :close-on-click-modal="false"
      destroy-on-close
      @close="onReportDialogClose"
      :close-on-press-escape="false"
    >
      <div class="report-dialog__body">
        <div class="report-dialog__body__title">
          <!-- <div class="label">标题</div> -->
          <el-form
            ref="reportFormRef"
            style="max-width: 600px"
            :model="report"
            :rules="rules"
            label-width="auto"
          >
            <el-form-item label="标题" prop="title">
              <el-input
                v-model="report.title"
                :disabled="report.isReadOnly"
                class="report-dialog__body__title__content"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <UploadPDFComponent
          v-model="report.url"
          @file-change="fileChange"
          :isReadOnly="report.isReadOnly"
        ></UploadPDFComponent>
      </div>
      <template #footer>
        <div>
          <el-button type="primary" text bg @click="onReportDialogClose"> 关闭 </el-button>
          <el-button
            type="primary"
            @click="onSaveReport"
            :loading="isSaving"
            :disabled="report.isReadOnly == true || report.url == '' || report.title.trim() == ''"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.reports-component-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  display: grid;
  grid-template-rows: 32px 48px 1fr;
  gap: 10px 0;
  .reports-component-page__header {
    display: flex;
    gap: 10px;
    .page__header__filters {
      display: flex;
    }
  }
  .report-dialog__body {
    height: 70vh;
    display: flex;
    flex-direction: column;
    gap: 10px;
    .report-dialog__body__title {
      display: flex;
      align-items: center;

      .report-dialog__body__title__content {
        width: 100%;
        flex: 1;
      }
    }
  }
  .reports-component-page__tool {
    display: flex;
    align-items: center;
    flex: 0 0 40px;
    padding: 4px;
    background-color: #f5f7fa;
  }
}
</style>
