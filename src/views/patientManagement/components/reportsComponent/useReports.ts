import { requestGetReportsByPatient, requestDeleteReports } from "@/apis/reports";
import { Reports, Report } from "@/types/reports";

export const useReports = (_patientId: number) => {
  const reports = ref<Reports>([]);
  const id = ref(_patientId);

  async function loadReports(patientId?: number) {
    const { data } = await requestGetReportsByPatient(patientId || id.value);
    reports.value = sortByTime(data);
  }

  function sortByTime(reports: Report[]) {
    if (reports.length == 0) {
      return [];
    }
    reports.sort((a, b) => {
      const aTime = new Date(a.time || 0).getTime();
      const bTime = new Date(b.time || 0).getTime();
      return bTime - aTime;
    });
    return reports;
  }

  async function removeItem(reportId: Report["id"]) {
    await requestDeleteReports(reportId);
  }

  return {
    reports,
    loadReports,
    removeItem
  };
};
