<script lang="ts" setup>
import { useStore } from "@/store";
import { FormInstance, FormRules } from "element-plus";
import {
  requestGetSummaryRecord,
  requestAddSummaryRecord,
  requestDeleteSummaryRecord,
  requestEditSummaryRecord
} from "@/apis/summaryRecord";
import { format } from "date-fns";
import WangEdit from "@/components/WangEdit/Index.vue";
import { SummaryForm, Summaries, Summary, TimelineSummary } from "@/types/summary";
import { useModeAddOrEdit } from "@/composables/useModeAddOrEdit";
import { useDialog } from "@/composables/useDialog";
import { useSummaryTemplates } from "@/models/useSummaryTemplates";
import { SummaryTemplate } from "@/types/summaryTemplate";

const route = useRoute();

const store = useStore();

const emits = defineEmits<{
  print: [summary: Summary];
}>();

const { setModeToAdd, setModeToEdit, modeIsAdd, modeIsEdit } = useModeAddOrEdit();

const patientId = computed<number>(() => {
  return Number(route.query.patientId) ?? 0;
});

const rules = reactive<FormRules>({
  content: [
    {
      required: true,
      message: "请输入内容",
      trigger: "blur"
    }
  ]
});

const summaryFormRef = ref<FormInstance>();

const summaryForm = ref<SummaryForm>({
  content: "",
  time: new Date()
});

const setSummaryForm = ({ id, content, time }: SummaryForm) => {
  summaryForm.value = JSON.parse(JSON.stringify({ id, content, time }));
};
const summaryIsEmpty = computed(() => {
  return summaryForm.value.content == "" || summaryForm.value.content == "<p><br></p>";
});

const summaries = ref<Summaries>([]);
const timelineSummaries = computed<TimelineSummary[]>(() => {
  if (summaries.value.length == 0) return [];
  return summaries.value.map(s => ({
    ...s,
    timeStr: format(s.time, "yyyy-MM-dd HH:mm:ss")
  }));
});
const currentPrintSummary = ref<TimelineSummary>();
const getPrintTag = (t: TimelineSummary): boolean => {
  if (!currentPrintSummary.value) {
    return false;
  }
  return t.id == currentPrintSummary.value?.id;
};

const setStoreSummaries = (summaries: Summaries) => {
  store.commit("setSummaryRecords", JSON.parse(JSON.stringify(summaries)));
};

const onCompleteSummaryBtnClick = async () => {
  const { id, content, time } = summaryForm.value;
  await summaryFormRef.value?.validate();
  switch (true) {
    case modeIsAdd():
      const { data: newSummaryRecord } = await requestAddSummaryRecord({
        patientId: patientId.value,
        content,
        time
      });
      summaries.value.unshift(newSummaryRecord);

      setStoreSummaries([newSummaryRecord]);
      setSummaryForm({ content: "", time: new Date() });

      break;
    case modeIsEdit():
      if (id) {
        await requestEditSummaryRecord(id, {
          content,
          time
        });
        const index = summaries.value.findIndex(s => s.id == id);
        summaries.value[index].content = content;
        summaries.value[index].time = time;

        setStoreSummaries([summaries.value[index]]);
        setSummaryForm({ id, content: "", time: new Date() });
      }
      break;
  }
  if (summaries.value.length > 0) {
    currentPrintSummary.value = {
      ...summaries.value[0],
      timeStr: format(summaries.value[0].time, "yyyy-MM-dd HH:mm:ss")
    };
    setStoreSummaries(summaries.value);
  }
};

const onEditSummaryBtnClick = async (summaryItem: Summary) => {
  if (!summaryIsEmpty.value) {
    await ElMessageBox.confirm("是否替换编辑区内容?", "提示", {
      confirmButtonText: "替换",
      cancelButtonText: "取消",
      type: "info"
    });
    ElMessage({
      type: "success",
      message: "已替换"
    });
  }
  setModeToEdit();

  setSummaryForm(summaryItem);
};

const onRemoveSummaryBtnClick = async (index: number, id: number) => {
  await requestDeleteSummaryRecord(id);
  summaries.value.splice(index, 1);
  if (summaries.value.length > 0) {
    currentPrintSummary.value = {
      ...summaries.value[0],
      timeStr: format(summaries.value[0].time, "yyyy-MM-dd HH:mm:ss")
    };
    setStoreSummaries(summaries.value);
  }
};

const onCancelSummaryBtnClick = () => {
  setSummaryForm({ content: "", time: new Date() });
  setModeToAdd();
};

const onPrint = (summary: TimelineSummary) => {
  currentPrintSummary.value = {
    ...summary,
    timeStr: format(summary.time, "yyyy-MM-dd HH:mm:ss")
  };
  setStoreSummaries([JSON.parse(JSON.stringify(summary))]);
  emits("print", summary);
};

const loadSummaries = async () => {
  const { data } = await requestGetSummaryRecord(patientId.value);
  summaries.value = data
    .map(s => ({
      ...s,
      time: new Date(s.time),
      timeStr: format(s.time, "yyyy-MM-dd HH:mm:ss")
    }))
    .sort((a, b) => b.time.valueOf() - a.time.valueOf());
  setStoreSummaries([summaries.value[0]]);
};

function onSummaryTemplateClick() {
  loadSummaryTemplates();
  openSummaryTemplateDialog();
}
const {
  dialog: summaryTemplateDialog,
  openDialog: openSummaryTemplateDialog,
  closeDialog: closeSummaryTemplateDialog
} = useDialog();
const { summaryTemplates, loadSummaryTemplates } = useSummaryTemplates();
const filterCondition = ref("");
const summaryTemplatesFiltered = computed(() => {
  if (summaryTemplates.value.length == 0) {
    return [];
  }
  return summaryTemplates.value.filter(
    s => s.content.includes(filterCondition.value) || s.title.includes(filterCondition.value)
  );
});
function onCloseSummaryTemplateDialog() {
  closeSummaryTemplateDialog();
}

async function onApplySummaryTemplate(summaryTemplate: SummaryTemplate) {
  if (!summaryIsEmpty.value) {
    await ElMessageBox.confirm("是否替换编辑区内容?", "提示", {
      confirmButtonText: "替换",
      cancelButtonText: "取消",
      type: "info"
    });
  }

  setSummaryForm({
    content: summaryTemplate.content,
    time: new Date()
  });
  closeSummaryTemplateDialog();
}

onMounted(async () => {
  await loadSummaries();
  currentPrintSummary.value = timelineSummaries.value[0];
});
</script>

<template>
  <div class="summaries-page">
    <div class="summaries-container">
      <span class="summary-title">小结列表</span>
      <el-empty v-if="summaries.length == 0" description="暂无数据" style="height: 100%" />
      <el-scrollbar v-else>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in timelineSummaries"
            :timestamp="item.timeStr"
            :key="item.id"
            placement="top"
          >
            <el-card shadow="hover" class="summary">
              <template #header>
                <div class="summary__header">
                  <div>
                    <el-tag type="primary" effect="dark" v-if="getPrintTag(item)">
                      当前打印
                    </el-tag>
                  </div>
                  <div class="summary__header__button-group">
                    <el-button type="success" bg text @click="onPrint(item)">选用打印</el-button>
                    <el-button type="primary" bg text @click="onEditSummaryBtnClick(item)"
                      >修改</el-button
                    >
                    <el-button
                      type="danger"
                      bg
                      text
                      @click="onRemoveSummaryBtnClick(index, item.id)"
                      >删除</el-button
                    >
                  </div>
                </div>
              </template>

              <div class="summary__content">
                <PreviewWangEditor :modelValue="item.content"></PreviewWangEditor>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-scrollbar>
    </div>
    <el-divider direction="vertical" class="divider" />
    <div class="summary-form-wrap">
      <div class="summary-title">
        <div>{{ summaryForm.id ? "编辑小结" : "新增小结" }}</div>
        <el-button type="primary" @click="onSummaryTemplateClick" style="margin-right: 10px"
          >小结模板</el-button
        >
      </div>
      <el-card shadow="never">
        <el-form ref="summaryFormRef" :model="summaryForm" :rules="rules" label-width="50px">
          <el-form-item label="时间" prop="time">
            <el-date-picker
              v-model="summaryForm.time"
              type="datetime"
              :editable="false"
              :clearable="false"
            />
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <!-- <el-input
              v-model="summaryForm.content"
              :rows="5"
              type="textarea"
              placeholder="请输入内容"
            /> -->
            <WangEdit style="min-height: 301px; height: 40rem" v-model="summaryForm.content" />
          </el-form-item>
        </el-form>
      </el-card>
      <el-button
        :disabled="summaryIsEmpty"
        type="primary"
        style="margin: 0"
        @click="onCompleteSummaryBtnClick"
        >完成</el-button
      >
      <el-button
        v-if="!summaryIsEmpty"
        type="info"
        style="margin: 0"
        @click="onCancelSummaryBtnClick"
        >取消</el-button
      >
    </div>
    <!-- 小结模板 -->
    <el-dialog
      v-model="summaryTemplateDialog.visible"
      :title="summaryTemplateDialog.title"
      width="800"
      :close-on-click-modal="false"
      destroy-on-close
      :close-on-press-escape="false"
    >
      <div class="summary-template-dialog__body">
        <el-table :data="summaryTemplatesFiltered" style="width: 100%" height="100%">
          <el-table-column prop="title" label="模板标题" width="180" />
          <el-table-column prop="content" label="模板内容" min-width="180">
            <template #default="{ row }">
              <PreviewWangEditor :modelValue="row.content"></PreviewWangEditor>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180">
            <template #default="{ row }">
              <el-button bg text type="primary" @click="onApplySummaryTemplate(row)"
                >应用</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCloseSummaryTemplateDialog">关闭</el-button>
          <!-- <el-button type="primary"> 确定 </el-button> -->
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.summaries-page {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 10px;
  .summaries-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 20px;
    :deep(.el-timeline-item__timestamp) {
      color: #222;
    }
    .summary {
      :deep(.el-card__header) {
        display: flex;
        padding: 8px;
        border: none;
        gap: 10px;
        .summary__header {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 10px;
        }
        .summary__content {
          word-wrap: break-word;
        }
      }
      :deep(.el-card__body) {
        display: grid;
        align-items: center;
        grid-gap: 10px;
      }
    }
  }
  .divider {
    height: 100%;
  }
  .summary-form-wrap {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    .summary-title {
      display: flex;
      align-items: center;
      gap: 10px;
      justify-content: space-between;
    }
  }
  .summary-template-dialog__body {
    height: calc(80vh - 15vh);
  }
}
</style>
