<script lang="ts" setup>
import moment from "moment";
import {
  InspectionDetail,
  InspectionDetails,
  validateUnusualItem
} from "../inspectionDetails/models/useViewInspectionDetails";
import { useInspectionDetailPrint } from "../inspectionDetails/models/useInspectionDetailPrint";
import { groupedToMap } from "@/utils/collection";

defineOptions({
  name: "InspectionDetailsForPrint"
});

const { inspectionDetailPrint } = useInspectionDetailPrint();

const data = computed<Map<InspectionDetail["itemNameCode"], InspectionDetails>>(() => {
  if (inspectionDetailPrint.value.length == 0) return new Map();

  const res = groupedToMap(inspectionDetailPrint.value, "itemNameCode");
  res.forEach(item => {
    item.sort((a, b) => a.sort - b.sort);
    if (item.length > 5) {
      item.slice(0, 5);
    }
  });

  return res;
});

type DataKeys = {
  title: InspectionDetail["itemNameCn"];
  code: InspectionDetail["itemNameCode"];
  reference: InspectionDetail["normalRefValueText"];
}[];

const dataKeys = computed<DataKeys>(() => {
  if (data.value.size == 0) return [];
  return Array.from(data.value.keys()).reduce<DataKeys>((result, d) => {
    const item = data.value.get(d);
    if (item?.length) {
      result.push({
        title: item[0].itemNameCn,
        code: d,
        reference: item[0].normalRefValueText
      });
    }
    return result;
  }, []);
});

function getUnusualItemStyle(hasUnusualItem: boolean) {
  return hasUnusualItem ? "color:#fc2d2d;" : "";
}
</script>

<template>
  <div class="print-inspection-view">
    <div
      class="print-inspection-view__contents"
      v-for="{ title, code, reference } in dataKeys"
      :key="code"
    >
      <div class="contents-header">
        <div class="contents-header__title">
          {{ title }}
        </div>
        <div class="contents-header__reference" v-if="reference">参考值：{{ reference }}</div>
      </div>

      <table border class="print-inspection-view__body">
        <tbody>
          <tr>
            <td v-for="item in data.get(code)" :key="item.id" class="inspection-date">
              {{ moment(item.reportDate).format("YYYY-MM-DD") }}
            </td>
          </tr>
          <tr>
            <td
              v-for="item in data.get(code)"
              :key="item.itemNameCode"
              :style="getUnusualItemStyle(validateUnusualItem(item))"
            >
              <div>
                <span>
                  {{ `${item.itemValue} ${item.itemUnit || ""}` }}
                </span>
                <span v-if="validateUnusualItem(item)">
                  {{ item.normalFlagName }}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.print-inspection-view {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px 0;
  .print-inspection-view__contents {
    @pintFontSize: 8pt;
    box-sizing: border-box;
    padding: 2px;
    width: 100%;
    // border: 0.5px solid #ebeef5;
    .contents-header {
      display: flex;
      flex-direction: column;
      // text-indent: @pintFontSize*1.2;
      font-size: @pintFontSize * 1.8;
      gap: 4px;

      .contents-header__title {
        font-weight: 600;
        padding: 4px;
      }
    }
    .print-inspection-view__body {
      width: 100%;
      border: none;
      display: grid;
      border-collapse: collapse;
      color: #000;
      table-layout: fixed;
      tbody {
        width: 100%;

        .inspection-date {
          // background-color: #e2efda;
          // border: #000 1px solid;
        }
        tr {
          display: flex;
          width: 100%;
          border-collapse: collapse;

          td {
            box-sizing: border-box;
            max-width: 20%;
            flex: 1 0 100px;
            text-align: center;
            border: 0.5px solid #ebeef5;
            font-size: @pintFontSize * 1.6;
            padding: 4px;
          }
        }
      }
    }
  }
  @media print {
    table {
      page-break-inside: auto;
    }
    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }
  }
}
</style>
