<script lang="ts" setup>
import { useStoreInspectionSort } from "@/store/storeInspectionSort";
import { Top, Bottom } from "@element-plus/icons-vue";

defineOptions({
  name: "InspectionPrintSortComponent"
});

const { storeInspectionSort, orderUp, orderDown, loadData } = useStoreInspectionSort();

onMounted(async () => {
  await loadData();
});
</script>

<template>
  <div class="print-sort-component">
    <el-table :data="storeInspectionSort?.detail">
      <el-table-column prop="itemName" label="项目名称" min-width="100" />
      <!-- <el-table-column prop="itemCode" label="项目编码" width="100" /> -->
      <!-- <el-table-column prop="sort" label="排序" width="100" /> -->
      <el-table-column label="是否新排序" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isOld == false ? 'primary' : 'info'">
            {{ scope.row.isOld ? "否" : "是" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button :icon="Top" circle @click="orderUp(scope.row)"> </el-button>
          <el-button :icon="Bottom" circle @click="orderDown(scope.row)"> </el-button>
          <!-- <el-button :icon="Delete" circle type="danger" @click="deleteRule(scope.row.id)">
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
