<script setup lang="ts">
import { useStoreInspectionsForPrint } from "@/store/storeInspectionsForPrint";
import { Inspection, validateUnusualItem } from "@/types/inspection";
import { format } from "date-fns";
import { useStoreInspectionSort } from "@/store/storeInspectionSort";

const { storeInspectionsForPrint } = useStoreInspectionsForPrint();
const { storeInspectionSort, loadData } = useStoreInspectionSort();

defineOptions({
  name: "InspectionsForPrintComponent"
});
interface PrintItem {
  title: {
    itemName: string;
    visitTypeName: string;
  };
  table: {
    headers: string[];
    rows: TableRow[];
  };
  itemCode: string;
}

interface TableCell {
  value: string;
  isUnusual: boolean;
  normalFlagName?: string;
}

interface TableRow {
  itemNameCn: string;
  itemCode: string;
  values: TableCell[];
  unit: string;
  normalRefValueText: string;
}

const printItems = computed<PrintItem[]>(() => {
  const result: PrintItem[] = [];

  storeInspectionsForPrint.value.forEach((innerMap, itemCode) => {
    if (innerMap.size === 0) return;

    const allInspections = Array.from(innerMap.values()).flat();
    if (allInspections.length === 0) return;

    const sortedDates = sortInspectionsByDate(allInspections).slice(0, 5);

    const item: PrintItem = {
      title: {
        itemName: allInspections[0].itemName,
        visitTypeName: allInspections[0].visitTypeName
      },
      table: {
        headers: createTableHeaders(sortedDates),
        rows: createTableRows(innerMap, sortedDates)
      },
      itemCode: itemCode
    };

    result.push(item);
  });

  if (storeInspectionSort.value?.detail) {
    result.sort((a, b) => {
      const aRule = storeInspectionSort.value!.detail.find(item => item.itemCode === a.itemCode);
      const bRule = storeInspectionSort.value!.detail.find(item => item.itemCode === b.itemCode);
      if (!aRule) return 1;
      if (!bRule) return -1;
      return bRule.sort - aRule.sort;
    });
  }

  return result;
});

function sortInspectionsByDate(inspections: Inspection[]) {
  return [...new Set(inspections.map(i => i.reportDate))].sort(
    (a, b) => new Date(a).getTime() - new Date(b).getTime()
  );
}

function createTableHeaders(sortedDates: string[]) {
  return [
    "检查项目",
    ...sortedDates.map(date => format(new Date(date), "yyyy-MM-dd")),
    "单位",
    "参考值"
  ];
}

function createTableRows(
  innerMap: Map<Inspection["itemNameCode"], Inspection[]>,
  sortedDates: string[]
): TableRow[] {
  const rows: TableRow[] = [];

  innerMap.forEach(inspections => {
    const inspectionsByDate = new Map(inspections.map(i => [i.reportDate, i]));

    rows.push({
      itemNameCn: inspections[0].itemNameCn,
      itemCode: inspections[0].itemCode,
      values: sortedDates.map(date => {
        const inspection = inspectionsByDate.get(date);
        if (!inspection) {
          return { value: "", isUnusual: false };
        }
        const isUnusual = validateUnusualItem(inspection);
        return {
          value: `${inspection.itemValue}${isUnusual ? inspection.normalFlagName : ""}`,
          isUnusual,
          normalFlagName: inspection.normalFlagName
        };
      }),
      unit: inspections[0].itemUnit,
      normalRefValueText: inspections[0].normalRefValueText
    });
  });

  return rows;
}

function getUnusualItemStyle(hasUnusualItem: boolean) {
  return hasUnusualItem ? "color: red;" : "";
}

onMounted(async () => {
  await loadData();
  // if (storeInspectionSort.value != null) {
  //   sortInspectionsForPrint(storeInspectionSort.value.detail);
  // }
});
</script>

<template>
  <div class="inspections-for-print">
    <div v-for="(item, index) in printItems" :key="index" class="print-item">
      <!-- 标题部分 -->
      <div class="print-item-title">
        <span class="item-name">{{ item.title.itemName }}</span>
        <span class="visit-type">({{ item.title.visitTypeName }})</span>
      </div>

      <!-- 表格部分 -->
      <table class="print-item-table">
        <thead>
          <tr>
            <th
              v-for="(header, headerIndex) in item.table.headers"
              :key="headerIndex"
              :class="{
                'col-item-name': headerIndex === 0,
                'col-unit': header === '单位',
                'col-ref': header === '参考值'
              }"
            >
              {{ header }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in item.table.rows" :key="rowIndex">
            <td class="col-item-name">{{ row.itemNameCn }}</td>
            <td
              v-for="(cell, valueIndex) in row.values"
              :key="valueIndex"
              class="col-value"
              :style="getUnusualItemStyle(cell.isUnusual)"
            >
              {{ cell.value }}
            </td>
            <td class="col-unit">{{ row.unit }}</td>
            <td class="col-ref">{{ row.normalRefValueText }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style lang="less" scoped>
.inspections-for-print {
  padding: 20px;
}

.print-item {
  margin-bottom: 20px;
  page-break-inside: avoid;
}

.print-item-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;

  .visit-type {
    margin-left: 8px;
    color: #666;
  }
}

.print-item-table {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    border: 1px solid #cccccc;
    padding: 8px;
    text-align: left;
  }

  th {
    background-color: #e2efda;
    font-weight: bold;
  }

  .col-item-name {
    min-width: 120px;
    max-width: 200px;
  }

  .col-value {
    min-width: 80px;
    max-width: 120px;
    position: relative;

    &:hover {
      cursor: pointer;
    }
  }

  .col-unit {
    width: 100px;
  }

  .col-ref {
    width: 100px;
  }

  // 确保列宽在范围内自适应
  table-layout: fixed;
}

@media print {
  .inspections-for-print {
    padding: 0;
  }

  .print-item {
    page-break-inside: avoid;
  }

  .print-item-table {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
</style>
