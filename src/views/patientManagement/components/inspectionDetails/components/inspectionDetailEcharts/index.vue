<script lang="ts" setup>
import {
  InspectionDetail,
  InspectionDetails,
  validateUnusualItem
} from "../../models/useViewInspectionDetails";
import * as ECharts from "echarts/core";
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  ToolboxComponent
} from "echarts/components";
import { LineChart } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { format } from "date-fns/format";
import { useThrottleFn } from "@vueuse/core";

ECharts.use([
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  ToolboxComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition
]);
defineOptions({
  name: "InspectionDetailEcharts"
});

const props = defineProps<{
  data: InspectionDetails;
  chartTitle?: string;
}>();

const _id = useId();

const dataCanRender = computed(() => {
  return props.data.every(item => !isNaN(Number(item.itemValue)));
});

const chartInstance = shallowRef<ECharts.ECharts | null>(null);

let resizeObserver: ResizeObserver | null = null;
function getUnusualItemStyle(hasUnusualItem: boolean) {
  return hasUnusualItem ? "background-color: #fcd3d3;font-size: 16px;" : "";
}
const echartsConfigOption = computed(() => ({
  title: {
    text: props.chartTitle || "",
    left: "center"
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "line",
      snap: true,
      axis: "x",
      crossStyle: {
        color: "#3b2c34"
      },
      label: {
        backgroundColor: "#5184F7"
      }
    },
    formatter: (data: any[]) => {
      const item = data[0].data;
      return `<span style="${getUnusualItemStyle(validateUnusualItem(item))}">${item.itemValue} ${
        item.itemUnit || ""
      } ${item.normalFlagName || ""}</span><br/><div style="max-width:400px;white-space: normal; word-break: break-word;">参考值： ${
        item.normalRefValueText
      }</div><br/>${item.itemName} <br/> ${item.itemNameCn}<br/>${format(
        item.reportDate,
        "yyyy-MM-dd"
      )}`;
    }
  },
  grid: {
    left: "40px",
    right: "40px",
    bottom: "0",
    containLabel: true
  },
  xAxis: {
    type: "category",
    boundaryGap: false,
    axisTick: {
      interval: 0,
      inside: true
    },
    data: props.data.map(item => format(item.reportDate, "yyyy-MM-dd"))
  },
  yAxis: {
    type: "value",
    axisLabel: {
      formatter: "{value}"
    }
  },
  series: [
    {
      type: "line",
      symbolSize: 10,
      label: {
        show: true,
        position: "top",
        formatter: (params: any) => params.value
      },
      data: props.data.map(item => ({
        value: Number(item.itemValue),
        ...item
      }))
    }
  ]
}));
const renderEcharts = useThrottleFn(async () => {
  if (props.data.length === 0) return;
  if (!dataCanRender.value || chartInstance.value == null) return;

  chartInstance.value.setOption(echartsConfigOption.value, {
    notMerge: true,
    lazyUpdate: true
  });
  chartInstance.value.resize();
}, 100);

onBeforeUnmount(() => {
  if (chartInstance.value != null) {
    if (resizeObserver != null) {
      resizeObserver.disconnect();
    }
    chartInstance.value.dispose();
    chartInstance.value = null;
  }
  resizeObserver = null;
});

watch(
  () => props.data,
  () => {
    renderEcharts();
  }
);

function onChartMounted() {
  const chartContainer = chartInstance.value?.getDom();
  if (chartContainer && chartInstance.value != null) {
    resizeObserver = new ResizeObserver(
      useThrottleFn(() => {
        if (chartInstance.value) {
          chartInstance.value.resize();
        }
      }, 100)
    );
    resizeObserver.observe(chartContainer);
  }
}

const isComponentVisible = ref(true);

onActivated(() => {
  isComponentVisible.value = true;
  nextTick(() => {
    if (chartInstance.value) {
      chartInstance.value.resize();
    }
  });
});

onDeactivated(() => {
  isComponentVisible.value = false;
});

onMounted(async () => {
  await nextTick();
  if (!document.getElementById(_id)) return;

  chartInstance.value = ECharts.init(document.getElementById(_id), undefined, {
    renderer: "canvas",
    useDirtyRect: true
  });

  renderEcharts();
  onChartMounted();
});
</script>

<template>
  <div
    v-if="dataCanRender"
    :id="_id"
    style="opacity: 1; width: 100%; height: 300px"
    :key="_id"
    v-bind="$attrs"
  ></div>
</template>
