<script lang="ts" setup>
import { InspectionDetails, validateUnusualItem } from "../../models/useViewInspectionDetails";
import { format } from "date-fns";

defineOptions({
  name: "InspectionDetailTable"
});
const props = defineProps<{ data: InspectionDetails }>();
const viewInspectionDetail = computed(() => props.data);

function getUnusualItemStyle(hasUnusualItem: boolean) {
  return hasUnusualItem ? "background-color: #fcd3d3;font-size: 16px;" : "";
}

function getVisitType(visitTypeCode: string) {
  return visitTypeCode == "1" ? " c" : " h";
}
</script>

<template>
  <div class="inspection-details-time-view">
    <table border class="inspection__table">
      <tbody>
        <tr>
          <th scope="row"><div class="table-title">日期</div></th>
          <template v-for="g in viewInspectionDetail">
            <td>
              <span>{{ format(g.reportDate, "yyyy-MM-dd") }}</span>
              <span style="color: #444">{{ getVisitType(g.visitTypeCode) }}</span>
            </td>
          </template>
        </tr>
        <tr>
          <th scope="row">结果</th>
          <template v-for="g in viewInspectionDetail">
            <td :style="getUnusualItemStyle(validateUnusualItem(g))">
              <div :title="`${g.itemName}\n ${g.itemValue}`">
                <span :title="`${g.itemName}\n ${g.itemValue}`">
                  {{ g.itemValue }}
                </span>
                <!-- <span> {{ g.itemUnit }}</span> -->
                <span v-if="validateUnusualItem(g)" :title="`${g.itemName}\n ${g.itemValue}`">
                  {{ g.normalFlagName }}
                </span>
              </div>
            </td>
          </template>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style lang="less" scoped>
.inspection-details-time-view {
  min-width: 100%;
  .inspection__table {
    border: none;
    display: grid;
    border-collapse: collapse;
    min-width: 100%;

    th,
    td {
      padding: 4px;
    }
    th {
      font-weight: 350;
      color: hsl(0, 0%, 20%);
    }
    td {
      text-align: center;
      min-width: 90px;
    }
    .table-title {
      min-width: 50px;
    }
  }
}
</style>
