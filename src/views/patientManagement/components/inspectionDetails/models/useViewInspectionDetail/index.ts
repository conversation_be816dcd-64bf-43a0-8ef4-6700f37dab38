import type { Inspection } from "@/types/inspection";

export interface ViewInspectionDetail extends Inspection {
  id: number;
}
export type ViewInspectionDetails = ViewInspectionDetail[];

const defaultViewInspectionDetail: ViewInspectionDetail = {
  id: 0,
  compositeItemSn: "",
  execDeptName: "",
  highValue: "",
  inpatientOrdNo: "",
  itemCode: "",
  itemName: "",
  itemNameCn: "",
  itemNameCode: "",
  itemNameEn: "",
  itemUnit: "",
  itemValue: "",
  labReportLid: "",
  labReportSn: 0,
  lowValue: "",
  normalFlagName: "",
  normalRefValueText: "",
  orderLid: "",
  orderPersonName: "",
  patientDomain: "",
  patientLid: "",
  patientName: "",
  phenomenonPerformance: "",
  reportDate: "",
  reporterId: "",
  reporterName: "",
  requestDate: "",
  requestNo: "",
  reviewDate: "",
  reviewerId: "",
  reviewerName: "",
  sampleNo: "",
  sampleTypeName: "",
  submittingPersonId: "",
  submittingPersonName: "",
  testResults: "",
  visitOrdNo: "",
  visitTimes: 0,
  visitTypeCode: "",
  visitTypeName: "",
  warnHighValue: "",
  warnLowValue: ""
};

export const useViewInspectionDetail = () => {
  const viewInspectionDetail = ref<ViewInspectionDetail>();
  function resetViewInspectionDetail() {
    viewInspectionDetail.value = defaultViewInspectionDetail;
  }

  return {
    viewInspectionDetail,
    resetViewInspectionDetail
  };
};
