import { getInspectionsApi } from "@/apis/InspectionCheck";
import type { InspectionDetail, InspectionDetails } from "@/types/inspection";
import { validateUnusualItem, filterInspectionDetailsTop } from "@/types/inspection";
import { getTimeForSomeYear } from "@/utils";
import { groupedToMap } from "@/utils/collection";
import { endOfDay } from "date-fns/endOfDay";

interface InspectionDetailsFiler {
  top: 0 | 1 | 2 | 3 | 4 | 5;
  itemName: string;
  startTime: Date;
  endTime: Date;
}

export { validateUnusualItem, InspectionDetail, InspectionDetails, filterInspectionDetailsTop };

const useFilterInspectionDetails = () => {
  const filters = ref<InspectionDetailsFiler>({
    top: 0,
    itemName: "",
    startTime: getTimeForSomeYear(1),
    endTime: endOfDay(new Date())
  });
  function resetFilters() {
    const time = endOfDay(new Date());
    filters.value = {
      top: 0,
      itemName: "",
      startTime: getTimeForSomeYear(1),
      endTime: time
    };
  }
  function changeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
    filters.value.top = top;
  }

  return {
    filters,
    resetFilters,
    changeTop
  };
};

// export const filterInspectByReportDate = (
//   data: InspectionDetails,
//   { startTime, endTime }: { startTime: Date; endTime: Date }
// ): InspectionDetails => {
//   return data.filter(
//     ({ reportDate }) =>
//       new Date(reportDate || 0).getTime() >= startTime.getTime() &&
//       new Date(reportDate || 0).getTime() <= endTime.getTime()
//   );
// };

interface GroupedInspectionDetails<K = string> extends Map<K, InspectionDetails> {}
interface GroupedByItemCode extends GroupedInspectionDetails<InspectionDetail["itemNameCode"]> {}

type ViewInspectionDetails = Map<
  InspectionDetail["itemNameCn"] | InspectionDetail["itemNameCode"],
  InspectionDetail[]
>;
export const useViewInspectionDetails = () => {
  const { filters, resetFilters, changeTop } = useFilterInspectionDetails();

  const rawData = ref<InspectionDetails>([]);

  // const filteredRawDataReportDate = computed<InspectionDetails>(() => {
  //   if (rawData.value.length == 0) return [];
  //   const { startTime, endTime } = filters.value;
  //   return filterInspectByReportDate(rawData.value, {
  //     startTime,
  //     endTime
  //   });
  // });

  const groupedItemNameCode = computed(() => groupByItemNameCode(rawData.value));

  // watch(groupedItemNameCode, v => {
  //   setIndexer(v);
  // });

  const filteredByTop = computed<GroupedByItemCode>(() => {
    if (filters.value.top == 0) {
      return groupedItemNameCode.value;
    }
    return filterInspectionDetailsTop(groupedItemNameCode.value, filters.value.top);
  });

  const viewInspectionDetails = ref<ViewInspectionDetails>(new Map());

  watch(
    () => filteredByTop.value,
    v => {
      viewInspectionDetails.value = v;
      setIndexer(v);
    }
  );

  //#region 索引器
  type Indexer = Map<InspectionDetail["itemNameCn"], InspectionDetail["itemNameCode"]>;

  //记录itemNameCn和itemNameCode的映射
  const indexer = ref<Indexer>(new Map());

  function setIndexer(data: GroupedByItemCode) {
    const result: Indexer = new Map();
    const grouped = groupByItemNameCn(Array.from(data.values()).flat());
    for (const [itemNameCn, inspections] of grouped) {
      if (inspections.length > 0) {
        result.set(itemNameCn, inspections[0].itemNameCode);
      }
    }
    indexer.value = result;
  }
  //#endregion

  //#region 已选择的
  const rawCheckStates = ref<Map<InspectionDetail["itemNameCode"], boolean>>(new Map());
  const checkStates = computed(() => rawCheckStates.value);

  function setCheckState(itemNameCode: InspectionDetail["itemNameCode"], isChecked: boolean) {
    rawCheckStates.value.set(itemNameCode, isChecked);
  }
  function getCheckState(itemNameCode: InspectionDetail["itemNameCode"]): boolean {
    return checkStates.value.get(itemNameCode) ?? false;
  }

  function clearCheckStates() {
    rawCheckStates.value = new Map();
  }

  function getChecked(): InspectionDetails {
    const result: InspectionDetails = [];
    for (const [itemNameCode, isChecked] of checkStates.value) {
      if (isChecked) {
        const inspections = viewInspectionDetails.value.get(itemNameCode);
        if (inspections) {
          result.push(...getSliceByTop(inspections, filters.value.top));
        }
      }
    }
    return result;
  }
  //#endregion

  function getSliceByTop(data: InspectionDetails, top: number) {
    if (data.length === 0) return [];
    return data.slice(0, top || 5);
  }

  function resetViewInspectionDetails() {
    viewInspectionDetails.value = filteredByTop.value;
    setIndexer(filteredByTop.value);
  }

  function searchViewInspectionDetails(keyword: InspectionDetail["itemNameCn"]) {
    const result = new Map();
    for (const itemNameCn of indexer.value.keys()) {
      const targetItem = itemNameCn.includes(keyword.trim());

      if (targetItem) {
        const itemNameCode = indexer.value.get(itemNameCn);
        result.set(itemNameCode, viewInspectionDetails.value.get(itemNameCode!));
      }
    }

    viewInspectionDetails.value = result;
  }

  async function loadViewInspectionDetails(
    patientId: number,
    { startTime, endTime }: { startTime: Date; endTime: Date }
  ) {
    rawData.value = await getInspectionsApi(patientId, { startTime, endTime });
  }

  interface GroupedByItemNameCn extends GroupedInspectionDetails<InspectionDetail["itemNameCn"]> {}

  function groupByItemNameCn(data: InspectionDetails): GroupedByItemNameCn;
  function groupByItemNameCn(data: GroupedInspectionDetails): GroupedByItemNameCn;
  function groupByItemNameCn(
    data: GroupedInspectionDetails | InspectionDetails
  ): GroupedByItemNameCn {
    if (Array.isArray(data)) {
      return groupedToMap(data, "itemNameCn");
    } else {
      const result: GroupedByItemNameCn = new Map();
      for (const inspections of data.values()) {
        const grouped = groupedToMap(inspections, "itemNameCn");
        for (const [innerKey, innerInspections] of grouped) {
          const existingInspections = result.get(innerKey);
          if (existingInspections) {
            existingInspections.push(...innerInspections);
          } else {
            result.set(innerKey, innerInspections);
          }
        }
      }
      return result;
    }
  }

  function groupByItemNameCode(data: InspectionDetails): GroupedByItemCode {
    return groupedToMap(data, "itemNameCode");
  }

  return {
    viewInspectionDetails,
    loadViewInspectionDetails,
    searchViewInspectionDetails,
    resetViewInspectionDetails,
    groupByItemNameCn,
    groupByItemNameCode,

    filters,
    resetFilters,
    changeTop,

    checkStates,
    getChecked,
    setCheckState,
    getCheckState,
    clearCheckStates
  };
};
