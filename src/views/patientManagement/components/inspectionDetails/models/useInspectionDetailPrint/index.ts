import { useStore } from "@/store";
import type { InspectionDetailForReport, InspectionDetails } from "@/types/inspection";

export const useInspectionDetailPrint = () => {
  const store = useStore();

  const inspectionDetailPrint = computed<InspectionDetailForReport[]>(
    () => store.state.needPrintInspectionDetails
  );

  async function saveToStore(p: InspectionDetailForReport[]) {
    return new Promise(resolve => {
      store.state.needPrintInspectionDetails = p;
      resolve(store.state.needPrintInspectionDetails);
    });
  }

  async function save(p: InspectionDetails, top: number) {
    const res = p.map((item, index) => ({ sort: index, ...item }));
    res.sort((a, b) => Number(a.itemNameCode) - Number(b.itemNameCode));

    await saveToStore(res);
  }

  function clear() {
    store.state.needPrintInspectionDetails = [];
  }

  function getInspectionDetailPrint() {
    return store.state.needPrintInspectionDetails;
  }

  return { inspectionDetailPrint, save, getInspectionDetailPrint, clear };
};
