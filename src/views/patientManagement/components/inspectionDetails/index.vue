<script lang="ts" setup>
import { PatientId } from "@/types/patient";
import { useViewInspectionDetails, InspectionDetail } from "./models/useViewInspectionDetails";
import { useInspectionDetailPrint } from "./models/useInspectionDetailPrint";
import LazyLoadComponent from "@/components/LazyLoadComponent.vue";
import EmptyComponent from "@/components/empty/index.vue";
import { useTopXTimesOptions } from "../../models/timesOptions";

//检验明细对比
defineOptions({
  name: "InspectionDetails"
});

const props = defineProps<{ patientId: PatientId }>();
const { topXTimesOptions } = useTopXTimesOptions();
const patientId = computed(() => props.patientId);

const {
  viewInspectionDetails,
  loadViewInspectionDetails,
  resetViewInspectionDetails,
  searchViewInspectionDetails,

  filters,
  resetFilters,
  changeTop,

  setCheckState,
  getChecked,
  getCheckState,
  clearCheckStates
} = useViewInspectionDetails();

const inspectionDetailTableComponent = defineAsyncComponent(
  () => import("./components/inspectionDetailTable/index.vue")
);
const inspectionDetailEchartsComponent = defineAsyncComponent(
  () => import("./components/inspectionDetailEcharts/index.vue")
);

//#region 是否禁用等状态
const isShowCharts = ref<boolean>(true);
const pageIsLoading = ref<boolean>(true);
const isDisabledQuery = computed<boolean>(() => pageIsLoading.value);
//#endregion

const inspectionDetailsInspections = ref();
watch(
  () => [filters.value.startTime, filters.value.endTime],
  () => {
    onSearch();
  }
);
async function onChangeIsShowCharts() {
  await loadData();
}

//#region 选择和打印
const {
  save: inspectionPrintSave,
  getInspectionDetailPrint,
  clear: clearInspectionDetailPrint
} = useInspectionDetailPrint();

function onSelectDetail(item: BaseInfo) {
  const isCheck = getCheckState(item.itemNameCode);

  setCheckState(item.itemNameCode, !isCheck);
  if (filters.value.top == 0) {
    filters.value.top = 5;
    changeTop(filters.value.top);
  }

  saveToPrint();
}

async function saveToPrint() {
  if (getChecked().length == 0) {
    clearInspectionDetailPrint();
    return;
  }
  const data = getInspectionDetailPrint();

  const uniqueData = Array.from(
    new Map([...getChecked(), ...data].map(item => [item.id, item])).values()
  );
  await inspectionPrintSave(uniqueData, filters.value.top || 5);
}

watch(
  () => filters.value.top,
  _ => {
    saveToPrint();
  }
);
//#endregion

//#region view

interface BaseInfo {
  id: InspectionDetail["id"];
  itemNameCode: InspectionDetail["itemNameCode"];
  itemNameCn: InspectionDetail["itemNameCn"];
  normalRefValueText: InspectionDetail["normalRefValueText"];
  itemUnit: InspectionDetail["itemUnit"];
}

const baseInfo = ref<BaseInfo[]>([]);

function initBaseInfo() {
  const result: BaseInfo[] = [];
  if (viewInspectionDetails.value.size == 0) return;
  viewInspectionDetails.value.forEach(v => {
    const item = v[0];
    result.push({
      id: item.id,
      itemNameCode: item.itemNameCode,
      itemNameCn: item.itemNameCn,
      normalRefValueText: item.normalRefValueText,
      itemUnit: item.itemUnit
    });
  });
  baseInfo.value = result;
}

watch(
  () => viewInspectionDetails.value,
  () => {
    initBaseInfo();
  }
);
//#endregion

async function loadData() {
  pageIsLoading.value = true;
  try {
    await loadViewInspectionDetails(patientId.value, {
      startTime: filters.value.startTime,
      endTime: filters.value.endTime
    });
  } catch (error) {
    console.error(error);
  }
  clearCheckStates();
  clearInspectionDetailPrint();
  pageIsLoading.value = false;
}

async function onResetFilters() {
  resetFilters();
  await loadData();
}
function onFiltersItemNameClear() {
  filters.value.itemName = "";
  inspectionDetailsInspections.value?.scrollTo({ top: 0, behavior: "smooth" });
  resetViewInspectionDetails();
}

function valueIsValid(data: InspectionDetail[]): boolean {
  return data.length > 1 && data.every(item => !isNaN(Number(item.itemValue)));
}

function notInspectionDetail(): boolean {
  return viewInspectionDetails.value.size == 0;
}

async function onSearch() {
  inspectionDetailsInspections.value?.scrollTo({ top: 0, behavior: "smooth" });

  if (filters.value.itemName.trim()) {
    searchViewInspectionDetails(filters.value.itemName);
    return;
  }
  await loadData();
}

async function onChangeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
  isShowCharts.value = false;
  inspectionDetailsInspections.value?.scrollTo({ top: 0, behavior: "smooth" });
  if (top == 0) {
    clearCheckStates();
    saveToPrint();
  }
  changeTop(top);
  await nextTick();
  isShowCharts.value = true;
}

onMounted(async () => {
  await loadData();
});
</script>

<template>
  <div class="inspection-details-component">
    <header class="inspection-details-component__header">
      <ul class="header__filters">
        <li class="header__filter">
          <!-- <div class="filter__label">开始日期</div> -->
          <div class="filter__value">
            <el-date-picker
              v-model="filters.startTime"
              type="date"
              placeholder="开始日期"
              :clearable="false"
              :editable="false"
              :disabled-date="(time: Date) => time.getTime() > filters.endTime.getTime()"
              style="width: 126px"
              :disabled="isDisabledQuery && pageIsLoading"
            />
          </div>
        </li>
        <li class="header__filter">
          <!-- <div class="filter__label">结束日期</div> -->
          <div style="width: 1rem">-</div>
          <div class="filter__value">
            <el-date-picker
              v-model="filters.endTime"
              type="date"
              placeholder="结束日期"
              :clearable="false"
              :editable="false"
              :disabled-date="(time: Date) => time.getTime() < filters.startTime.getTime()"
              style="width: 126px"
              :disabled="isDisabledQuery && pageIsLoading"
            />
          </div>
        </li>
        <li class="header__filter">
          <!-- <div class="filter__label">前X次结果</div> -->
          <div class="filter__value">
            <el-select
              v-model="filters.top"
              @change="onChangeTop"
              :disabled="isDisabledQuery && pageIsLoading"
              placeholder="请选择次数"
              style="width: 140px"
            >
              <el-option
                v-for="option in topXTimesOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </li>
        <li class="header__filter">
          <!-- <div class="filter__label">项目名称</div> -->
          <div class="filter__value">
            <el-input
              style="width: 240px"
              v-model="filters.itemName"
              :disabled="isDisabledQuery && pageIsLoading"
              clearable
              @clear="onFiltersItemNameClear"
              @keyup.enter="onSearch"
              placeholder="请输入项目名称"
            ></el-input>
          </div>
        </li>
      </ul>
      <div class="operations">
        <el-switch
          v-model="isShowCharts"
          active-text="查看图表"
          inactive-text="查看图表"
          inline-prompt
          @change="onChangeIsShowCharts"
          :disabled="isDisabledQuery && pageIsLoading"
          size="large"
          style="margin-right: 20px"
        />

        <el-button
          type="primary"
          @click="onSearch"
          clearable
          :loading="pageIsLoading"
          :disabled="isDisabledQuery"
          >搜索</el-button
        >
        <el-button
          type="primary"
          @click="onResetFilters"
          clearable
          :loading="pageIsLoading"
          :disabled="isDisabledQuery"
          text
          bg
          >重置</el-button
        >
      </div>
    </header>

    <section
      class="inspection-details-component__inspections"
      v-loading="pageIsLoading"
      ref="inspectionDetailsInspections"
    >
      <EmptyComponent
        v-if="notInspectionDetail()"
        class="empty-container"
        description="暂无检验明细 请尝试修改查询条件"
      ></EmptyComponent>
      <template v-if="!notInspectionDetail()">
        <div v-for="item in baseInfo" :key="item.itemNameCode" class="inspection-container">
          <div class="title-container">
            <div class="title">
              <el-checkbox
                :key="item.itemNameCode"
                :model-value="getCheckState(item.itemNameCode)"
                @change="onSelectDetail(item)"
              />
              {{ item.itemNameCn }}
            </div>

            <el-tag type="primary" size="large">
              参考值：
              {{ item.normalRefValueText }}
            </el-tag>
            <el-tag type="info" effect="plain" size="large" v-if="item.itemUnit">
              单位：
              {{ item.itemUnit }}
            </el-tag>
          </div>
          <LazyLoadComponent
            :key="`table_${item.itemNameCode}`"
            :asyncComponent="inspectionDetailTableComponent"
            :data="viewInspectionDetails.get(item.itemNameCode)"
            style="height: 61px"
            class="inspection-table-container"
          />

          <LazyLoadComponent
            v-if="isShowCharts && valueIsValid(viewInspectionDetails.get(item.itemNameCode) || [])"
            :asyncComponent="inspectionDetailEchartsComponent"
            :data="viewInspectionDetails.get(item.itemNameCode)"
            :chartTitle="item.itemNameCn"
            :skeletonRows="4"
          />
        </div>
      </template>
    </section>
  </div>
</template>

<style lang="less" scoped>
.inspection-details-component {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  gap: 10px 0;
  box-sizing: border-box;
  padding: 0 10px 10px 10px;

  .inspection-details-component__header {
    height: 52px;
    display: flex;
    // background-color: hsl(0, 0%, 100%);
    // padding: 4px;
    border-radius: 4px;
    gap: 10px;

    .header__filters {
      display: flex;
      gap: 10px;
      .header__filter {
        display: flex;
        // gap: 10px;
        align-items: center;
      }
    }
    .operations {
      display: flex;
      gap: 10px;
      align-items: center;
      .operations__switch {
        display: flex;
        align-items: center;
      }
    }
  }
  ul,
  li {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .inspection-details-component__inspections {
    height: 100%;
    width: 100%;
    display: grid;
    overflow: auto;
    gap: 20px;
    padding: 10px 10px 10px 10px;
    box-sizing: border-box;
    .inspection-container {
      display: grid;
      grid-template-rows: 32px 61px auto;
      width: max-content;
      // max-width: 100%;
      outline: 1px solid #e4e7ed;
      border-radius: 8px;
      gap: 10px;
      padding: 10px;
      .title-container {
        display: flex;
        align-items: center;
        gap: 10px;
        .title {
          flex-shrink: 0;
          font-size: 1.1em;
          :deep(.el-checkbox) {
            width: 20px;
            height: 20px;
            transform: translateY(3px);
            .el-checkbox__inner {
              width: 20px;
              height: 20px;
              &::after {
                height: 9px;
                transform: rotate(45deg) scaleY(1) translateX(3px);
              }
            }
          }
        }
      }
      .inspection-table-container {
        display: flex;
        min-width: max-content;
        max-width: auto;
      }
    }
  }
  .card {
    padding: 10px;
    border-radius: 8px;
    outline: 1px solid #e4e7ed;
    &:hover {
      box-shadow: var(--el-box-shadow-light);
    }
  }
}
</style>
