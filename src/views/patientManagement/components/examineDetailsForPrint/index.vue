<script lang="ts" setup>
import { format } from "date-fns";
import { useExamineDetailPrint } from "../examineDetails/models/useExamineDetailPrint";
import { groupedToMap } from "@/utils/collection";
import { ExamineDetail, ExamineDetails } from "@/types/examine";
defineOptions({
  name: "ExamineDetailsForPrint"
});
const { getExamineDetailPrint } = useExamineDetailPrint();
const data = computed<Map<ExamineDetail["itemCode"], ExamineDetails>>(() => {
  if (getExamineDetailPrint().length == 0) return new Map();
  return groupedToMap(getExamineDetailPrint(), "itemCode");
});
type DataKeys = {
  title: ExamineDetail["itemName"];
  code: ExamineDetail["itemCode"];
}[];
const dataKeys = computed<DataKeys>(() => {
  if (data.value.size == 0) return [];
  return Array.from(data.value.keys()).reduce<DataKeys>((result, d) => {
    const item = data.value.get(d);
    if (item?.length) {
      result.push({
        title: item[0].itemName,
        code: d
      });
    }
    return result;
  }, []);
});
</script>

<template>
  <div class="print-examine-view">
    <div v-for="{ title, code } in dataKeys" :key="code" class="print-examine-view__contents">
      <div class="contents-header">
        <div class="contents-header__title">
          {{ title }}
        </div>
      </div>
      <table border class="examine__table">
        <tbody>
          <tr class="examine__table__dates">
            <template v-for="r in data.get(code)">
              <td>{{ format(r.reportDate, "yyyy-MM-dd") }}</td>
            </template>
          </tr>
          <tr class="examine__table__results">
            <template v-for="r in data.get(code)">
              <td class="examine__table__result">
                <p>
                  {{ r.seedescex }}
                </p>
                <p class="brief_summary">小结：{{ r.resultDescex }}</p>
              </td>
            </template>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.print-examine-view {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  .print-examine-view__contents {
    @pintFontSize: 8pt;
    box-sizing: border-box;
    padding: 2px;
    width: 100%;
    .contents-header {
      display: flex;
      flex-direction: column;
      // text-indent: @pintFontSize*1.2;
      font-size: @pintFontSize * 1.8;
      gap: 4px;
      .contents-header__title {
        font-weight: 600;
        padding: 4px;
      }
    }
    .examine__table {
      width: 100%;
      border: none;
      display: grid;
      border-collapse: collapse;
      table-layout: fixed;

      .table-title {
        width: 50px;
      }

      .examine__table__results {
        width: 100%;
        .brief_summary {
          color: #ff9800;
        }
      }
      tr {
        display: flex;
        width: 100%;
        border-collapse: collapse;
      }
      th,
      td {
        padding: 6px;
      }
      th {
        font-weight: 350;
        color: hsl(0, 0%, 20%);
      }
      td {
        flex: 1;
        border: 0.5px solid #ebeef5;
        color: hsl(0, 0%, 0%);
        // font-size: @pintFontSize * 1.8;
      }
    }
  }
  @media print {
    table {
      page-break-inside: auto;
    }
    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }
  }
}
</style>
