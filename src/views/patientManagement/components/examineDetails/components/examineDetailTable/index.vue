<script lang="ts" setup>
import { Examine, ExamineDetails } from "@/types/examine/index";
import { format } from "date-fns";
defineOptions({
  name: "ExamineDetailTable"
});
const props = defineProps<{ data: ExamineDetails }>();
const examineDetailResult = computed(() => props.data.filter(i => i.reportDate));

function getVisitTypeTag(visitTypeName: Examine["visitTypeName"]) {
  return `${visitTypeName == "门诊" ? " c" : " h"}`;
}
</script>
<template>
  <div class="examine-details-results">
    <table border class="examine__table">
      <tbody>
        <tr>
          <th scope="row"><div class="table-title">日期</div></th>
          <template v-for="r in examineDetailResult">
            <td>
              {{ format(r.reportDate, "yyyy-MM-dd")
              }}<span class="visit-type-tag">{{ getVisitTypeTag(r.visitTypeName) }}</span>
            </td>
          </template>
        </tr>
        <tr class="examine__table__results">
          <th scope="row"><div class="table-title">结果</div></th>
          <template v-for="r in examineDetailResult">
            <td class="examine__table__result">
              <p>
                {{ r.seedescex }}
              </p>
              <p class="brief_summary">小结:{{ r.resultDescex }}</p>
              <a :href="r.imagUrl" v-if="r.imagUrl" target="_blank" class="image_span">查看影像</a>
            </td>
          </template>
        </tr>
      </tbody>
    </table>
  </div>
</template>
<style lang="less" scoped>
.examine-details-results {
  width: 100%;
  .examine__table {
    width: 100%; // 确保表格占据父容器的宽度
    border: none;
    display: grid;
    border-collapse: collapse;

    table-layout: fixed; // 固定表格布局
    color: var(--el-table-text-color);

    .table-title {
      width: 50px;
    }

    .examine__table__results {
      width: 100%;
      .brief_summary {
        color: #ff9800;
      }
      .image_span {
        color: #409eff;
        cursor: pointer;
      }
    }
    th,
    td {
      padding: 4px;
    }
    th {
      font-weight: 350;
      color: hsl(0, 0%, 20%);
    }
    td {
      min-width: 200px;
      max-width: 100%;
      overflow-wrap: break-word;
    }
  }
}
</style>
