<script setup lang="ts">
import { PatientId } from "@/types/patient";
import { useExamineDetails } from "./models/useExamineDetails/index";
import { ExamineDetail } from "@/types/examine";
import { useExamineDetailPrint } from "./models/useExamineDetailPrint";
import EmptyComponent from "@/components/empty/index.vue";
import LazyLoadComponent from "@/components/LazyLoadComponent.vue";
import { useTopXTimesOptions } from "../../models/timesOptions";

//检查明细对比
defineOptions({
  name: "ExamineDetails"
});

const props = defineProps<{ patientId: PatientId }>();
const { topXTimesOptions } = useTopXTimesOptions();
const patientId = computed(() => props.patientId);

const examineDetailTableComponent = defineAsyncComponent(
  () => import("./components/examineDetailTable/index.vue")
);

const {
  examineDetails,
  loadExamineDetails,
  searchExamineDetails,
  resetExamineDetails,

  filters,
  changeTop,
  resetFilters,

  getCheckState,
  setCheckState,
  getChecked,
  clearCheckStates
} = useExamineDetails();

//#region 是否禁用等状态
const pageIsLoading = ref<boolean>(true);
const isDisabledQuery = computed<boolean>(() => pageIsLoading.value);
//#endregion
const examineDetailsInspections = ref();
watch(
  () => [filters.value.startTime, filters.value.endTime],
  () => {
    onSearch();
  }
);
const loadData = async () => {
  pageIsLoading.value = true;
  await loadExamineDetails(patientId.value, filters.value);
  clearCheckStates();
  clearExamineDetailPrint();
  pageIsLoading.value = false;
};

function onChangeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
  examineDetailsInspections.value?.scrollTo({ top: 0, behavior: "smooth" });
  if (top == 0) {
    clearCheckStates();
    // clear
  }
  changeTop(top);
}

async function onResetFilters() {
  resetFilters();
  await loadData();
}
function onFiltersItemNameClear() {
  filters.value.itemName = "";
  examineDetailsInspections.value?.scrollTo({ top: 0, behavior: "smooth" });
  resetExamineDetails();
}

async function onSearch() {
  examineDetailsInspections.value?.scrollTo({ top: 0, behavior: "smooth" });
  if (filters.value.itemName.trim()) {
    searchExamineDetails(filters.value.itemName, "itemName");
    return;
  }
  await loadData();
}

function notExamineDetail(): boolean {
  return examineDetails.value.size == 0;
}

//#region view
interface BaseInfo {
  id: number;
  itemName: ExamineDetail["itemName"];
  itemCode: ExamineDetail["itemCode"];
  isChecked: boolean;
}

const baseInfo = ref<BaseInfo[]>([]);

function initBaseInfo() {
  const result: BaseInfo[] = [];
  if (examineDetails.value.size == 0) return;
  examineDetails.value.forEach(v => {
    const item = v[0];
    result.push({
      id: item.id,
      itemName: item.itemName,
      itemCode: item.itemCode,
      isChecked: false
    });
  });
  baseInfo.value = result;
}
watch(
  () => examineDetails.value,
  () => {
    initBaseInfo();
  }
);
//#endregion

//#region 选择和打印
const {
  save: examinePrintSave,
  getExamineDetailPrint,
  clear: clearExamineDetailPrint
} = useExamineDetailPrint();

function onSelectDetail(item: BaseInfo) {
  const isCheck = getCheckState(item.itemCode);
  setCheckState(item.itemCode, !isCheck);
  if (filters.value.top == 0) {
    filters.value.top = 5;
    changeTop(5);
    clearExamineDetailPrint();
  }
  saveToPrint();
}

function saveToPrint() {
  if (getChecked().length == 0) {
    clearExamineDetailPrint();
    return;
  }
  const data = getExamineDetailPrint();

  //去重
  const uniqueData = Array.from(
    new Map([...getChecked(), ...data].map(item => [item.id, item])).values()
  );
  examinePrintSave(uniqueData, filters.value.top || 5);
}

watch(
  () => filters.value.top,
  _ => {
    saveToPrint();
  }
);

//#endregion

onMounted(() => {
  loadData();
});
</script>
<template>
  <div class="examine-details-component">
    <header class="examine-details-component__header">
      <ul class="header__filters">
        <li class="header__filter">
          <!-- <div class="filter__label">开始日期</div> -->
          <div class="filter__value">
            <el-date-picker
              v-model="filters.startTime"
              type="date"
              placeholder="开始日期"
              :clearable="false"
              :editable="false"
              :disabled-date="(time: Date) => time.getTime() > filters.endTime.getTime()"
              style="width: 126px"
              :disabled="isDisabledQuery && pageIsLoading"
            />
          </div>
        </li>
        <li class="header__filter">
          <!-- <div class="filter__label">结束日期</div> -->
          <div style="width: 1rem">-</div>
          <div class="filter__value">
            <el-date-picker
              v-model="filters.endTime"
              type="date"
              placeholder="结束日期"
              :clearable="false"
              :editable="false"
              :disabled-date="(time: Date) => time.getTime() < filters.startTime.getTime()"
              style="width: 126px"
              :disabled="isDisabledQuery && pageIsLoading"
            />
          </div>
        </li>
        <li class="header__filter">
          <!-- <div class="filter__label">前X次结果</div> -->
          <div class="filter__value">
            <el-select
              v-model="filters.top"
              @change="onChangeTop"
              :disabled="isDisabledQuery && pageIsLoading"
              placeholder="请选择次数"
              style="width: 140px"
            >
              <el-option
                v-for="option in topXTimesOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </li>
        <li class="header__filter">
          <!-- <div class="filter__label">项目名称</div> -->
          <div class="filter__value">
            <el-input
              style="width: 240px"
              v-model="filters.itemName"
              :disabled="isDisabledQuery && pageIsLoading"
              clearable
              @clear="onFiltersItemNameClear"
              @keyup.enter="onSearch"
              placeholder="请输入项目名称"
            ></el-input>
          </div>
        </li>
      </ul>
      <div class="operations">
        <el-button
          type="primary"
          @click="onSearch"
          clearable
          :loading="pageIsLoading"
          :disabled="isDisabledQuery"
          >搜索</el-button
        >
        <el-button
          type="primary"
          @click="onResetFilters"
          clearable
          :loading="pageIsLoading"
          :disabled="isDisabledQuery"
          text
          bg
          >重置</el-button
        >
      </div>
    </header>
    <section
      class="examine-details-component__body"
      v-loading="pageIsLoading"
      ref="examineDetailsInspections"
    >
      <EmptyComponent
        v-if="notExamineDetail()"
        class="empty-container"
        description="暂无检查明细 请尝试修改查询条件"
      ></EmptyComponent>
      <template v-if="!notExamineDetail()">
        <div class="examine-details" v-for="item in baseInfo" :key="item.itemCode">
          <div class="examine-details__header">
            <el-checkbox
              :model-value="getCheckState(item.itemCode)"
              @change="onSelectDetail(item)"
            />
            <div class="title">
              {{ item.itemName }}
            </div>
          </div>
          <!-- {{ viewExamineDetails.get(key) }} -->
          <LazyLoadComponent
            :key="`table_${item.itemCode}`"
            :asyncComponent="examineDetailTableComponent"
            :data="examineDetails.get(item.itemCode)"
          />
        </div>
      </template>
    </section>
  </div>
</template>
<style lang="less" scoped>
.examine-details-component {
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  gap: 10px 0;
  box-sizing: border-box;
  padding: 0 10px 10px 10px;
  .examine-details-component__header {
    height: 52px;
    display: flex;
    // padding: 6px;
    border-radius: 4px;
    gap: 10px;

    .header__filters {
      display: flex;
      gap: 10px;
      .header__filter {
        display: flex;
        // gap: 10px;
        align-items: center;
      }
    }
    .operations {
      display: flex;
      gap: 10px;
      align-items: center;
      .operations__switch {
        display: flex;
        align-items: center;
      }
    }
  }
  .examine-details-component__body {
    height: 100%;
    width: 100%;
    display: grid;
    overflow: auto;
    gap: 20px;
    padding: 10px 10px 10px 10px;
    box-sizing: border-box;
    .examine-details {
      width: fit-content;
      display: grid;
      grid-template-rows: 32px 1fr;
      outline: 1px solid #e4e7ed;
      border-radius: 8px;
      gap: 10px;
      padding: 10px;
      .examine-details__header {
        display: flex;
        align-items: center;
        gap: 10px;

        .title {
          font-size: 1.1em;
        }
        :deep(.el-checkbox) {
          width: 20px;
          height: 20px;
          .el-checkbox__inner {
            width: 20px;
            height: 20px;
            &::after {
              height: 9px;
              transform: rotate(45deg) scaleY(1) translateX(3px);
            }
          }
        }
      }
    }
  }
  ul,
  li {
    list-style: none;
    padding: 0;
    margin: 0;
  }
}
</style>
