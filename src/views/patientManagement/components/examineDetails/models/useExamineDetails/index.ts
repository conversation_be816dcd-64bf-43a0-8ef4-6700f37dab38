import { getTimeForSomeYear } from "@/utils";
import { groupedToMap } from "@/utils/collection";
import { ExaminationDataProvider } from "@/apis/InspectionCheck";
import {
  ExamineDetail,
  ExamineDetails,
  ExamineDetailKey,
  ExamineDetailValue
} from "@/types/examine/index";
import { endOfDay } from "date-fns/endOfDay";

interface ExamineDetailsFilter {
  top: number;
  itemName: string;
  startTime: Date;
  endTime: Date;
}

export const useExamineDetails = () => {
  const { filters, resetFilters: resetExamineFilters, changeTop } = useFilterExamineDetails();

  const raw = ref<ExamineDetails>([]);
  const groupedItemCode = computed(() => groupedToMap(raw.value, "itemCode"));

  const filteredByTop = computed(() => {
    if (filters.value.top == 0) {
      return groupedItemCode.value;
    }
    return filterExamineDetailsTop(groupedItemCode.value, filters.value.top);
  });

  const examineDetails = ref<Map<ExamineDetailKey, ExamineDetailValue>>(new Map());

  watch(
    () => filteredByTop.value,
    v => {
      examineDetails.value = v;
      setIndexer(v);
    }
  );
  //#region 索引器
  type Indexer = Map<ExamineDetail["itemName"], ExamineDetail["itemCode"]>;

  const indexer = ref<Indexer>(new Map());

  function setIndexer(data: Map<ExamineDetailKey, ExamineDetailValue>) {
    const itemNameIndex: Indexer = new Map();
    for (const element of data) {
      if (element[1].length != 0) {
        itemNameIndex.set(element[1][0].itemName, element[1][0].itemCode);
      }
    }

    indexer.value = itemNameIndex;
  }
  //#endregion

  //#region 已选择的
  const rawCheckStates = ref<Map<ExamineDetail["itemCode"], boolean>>(new Map());
  const checkStates = computed(() => rawCheckStates.value);

  function setCheckState(itemCode: ExamineDetail["itemCode"], isChecked: boolean) {
    rawCheckStates.value.set(itemCode, isChecked);
  }

  function getCheckState(itemCode: ExamineDetail["itemCode"]): boolean {
    return checkStates.value.get(itemCode) ?? false;
  }

  function clearCheckStates() {
    rawCheckStates.value.clear();
  }

  function getChecked(): ExamineDetails {
    const result: ExamineDetails = [];
    for (const [key, value] of examineDetails.value) {
      if (getCheckState(key)) {
        result.push(...value);
      }
    }
    return result;
  }
  //#endregion

  function resetFilters() {
    resetExamineFilters();
    examineDetails.value = filteredByTop.value;
  }

  async function loadExamineDetails(
    patientId: number,
    { startTime, endTime }: { startTime: Date; endTime: Date }
  ) {
    raw.value = await ExaminationDataProvider.getExaminations(patientId, { startTime, endTime });
  }

  // watchEffect(() => {
  //   // 用于查看初始化状态
  //   console.log("examineDetailsMap", examineDetails.value);
  //   console.log("indexer", indexer.value);
  // });
  function searchExamineDetails(keyword: ExamineDetail["itemName"], type: "itemName"): void;
  function searchExamineDetails(keyword: ExamineDetail["itemCode"], type: "itemCode"): void;
  function searchExamineDetails(
    keyword: ExamineDetail["itemName"] | ExamineDetail["itemCode"],
    type: "itemCode" | "itemName" = "itemName"
  ) {
    switch (type) {
      case "itemCode":
        examineDetails.value = findExamineDetailsByItemCode(keyword);
        break;
      case "itemName":
        examineDetails.value = findExamineDetailsByItemName(keyword);
        break;
      default:
        throw new Error("type error");
    }
  }

  function findExamineDetailsByItemName(
    keyword: ExamineDetail["itemName"]
  ): Map<ExamineDetail["itemCode"], ExamineDetails> {
    const result = new Map();
    for (const itemName of indexer.value.keys()) {
      const targetItem = itemName.includes(keyword);

      if (targetItem) {
        const itemCode = indexer.value.get(itemName);
        if (itemCode) {
          result.set(itemCode, examineDetails.value.get(itemCode));
        }
      }
    }
    return result;
  }

  function findExamineDetailsByItemCode(keyword: ExamineDetail["itemCode"]) {
    const result = new Map();
    if (examineDetails.value.size == 0) return result;
    for (const key of examineDetails.value.keys()) {
      if (key.includes(keyword)) {
        result.set(key, examineDetails.value.get(key));
      }
    }
    return result;
  }

  function filterExamineDetailsTop(
    examineDetails: Map<ExamineDetailKey, ExamineDetailValue>,
    top: number
  ) {
    if (top == 0) {
      return examineDetails;
    }
    const result = new Map<ExamineDetailKey, ExamineDetailValue>();
    examineDetails.forEach((values, key) => {
      const slicedValues = values.slice(0, top);
      result.set(key, slicedValues);
    });
    return result;
  }

  function resetExamineDetails() {
    examineDetails.value = filteredByTop.value;
    setIndexer(filteredByTop.value);
  }

  return {
    examineDetails,
    loadExamineDetails,
    searchExamineDetails,
    resetExamineDetails,

    filterExamineDetailsTop,
    filters,
    resetFilters,
    changeTop,

    getCheckState,
    setCheckState,
    clearCheckStates,
    getChecked
  };
};

function useFilterExamineDetails() {
  const filters = ref<ExamineDetailsFilter>({
    top: 0,
    itemName: "",
    startTime: getTimeForSomeYear(1),
    endTime: endOfDay(new Date())
  });

  function resetFilters() {
    const time = endOfDay(new Date());
    filters.value = {
      top: 0,
      itemName: "",
      startTime: getTimeForSomeYear(1),
      endTime: time
    };
  }

  function changeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
    filters.value.top = top;
  }

  return {
    filters,
    resetFilters,
    changeTop
  };
}
