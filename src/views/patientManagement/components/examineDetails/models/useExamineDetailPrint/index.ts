import { useStore } from "@/store";
import type { ExamineDetailForReport, ExamineDetails } from "@/types/examine";

export const useExamineDetailPrint = () => {
  const store = useStore();
  const { state } = store;

  const examineDetailPrint = computed<ExamineDetailForReport[]>(
    () => state.needPrintExamineDetails
  );

  function saveToStore(p: ExamineDetailForReport[]) {
    state.needPrintExamineDetails = JSON.parse(JSON.stringify(p));
  }

  function save(p: ExamineDetails, top: number) {
    const result = p.map(i => ({ sort: 0, ...i }));
    result.sort((a, b) => Number(a.itemCode) - Number(b.itemCode));

    saveToStore(result.slice(0, top || 5));
  }

  function clear() {
    state.needPrintExamineDetails = [];
  }

  function getExamineDetailPrint() {
    return state.needPrintExamineDetails;
  }

  return { examineDetailPrint, save, getExamineDetailPrint, clear };
};
