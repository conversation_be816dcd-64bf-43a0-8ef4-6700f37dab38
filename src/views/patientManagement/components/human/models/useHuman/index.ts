import Konva from "konva";
import {
  humanImage,
  thyroidImage
} from "@/views/patientManagement/components/human/data/images.json" with { type: "json" };

export const useHuman = () => {
  let stage: Konva.Stage | null = null;
  const humanLayer = new Konva.Layer();
  const organsLayer = new Konva.Layer();

  function init(elementId: string, config?: { width: number; height: number }) {
    stage = new Konva.Stage({
      container: elementId,
      width: config?.width || 600,
      height: config?.height || 840,
      fill: "#e8e8e8"
    });

    initHuman();
    initThyroid();
  }

  function initHuman() {
    if (!stage) return;
    const { width, height } = stage.getAttrs();
    console.log(1, width, height);

    const path = new Konva.Path({
      data: humanImage.path,
      fill: humanImage.fill,
      strokeWidth: 2,
      x: width! / 2 - 200,
      y: 20,
      height: height,
      width: width,
      scale: {
        x: 4,
        y: 4
      }
    });
    humanLayer.add(path);
    humanLayer.add(
      new Konva.Rect({
        width: width,
        height: height,
        stroke: "red",
        strokeWidth: 1
      })
    );
    stage.add(humanLayer);
  }

  function initThyroid() {
    if (!stage) return;
    const humanPath = humanLayer.findOne("Path");
    if (!humanPath) return;
    const { x: humanX, y: humanY } = humanPath.getAbsolutePosition();
    console.log(humanX, humanY);

    const thyroid = {
      path: {
        data: thyroidImage.path,
        fill: thyroidImage.fill,
        width: 184,
        height: 100,
        x: humanX + 184,
        y: humanY + 100,
        scale: {
          x: 0.03,
          y: 0.03
        }
      },
      text: {
        text: "甲状腺",
        x: 500,
        y: 200,
        width: 400,
        height: 20,
        fontSize: 20,
        fill: "#222"
      },
      line: {
        stroke: "#222",
        strokeWidth: 2
      }
    };

    const path = new Konva.Path(thyroid.path);
    const { x, y } = path.getPosition();
    const r = path.getClientRect();
    const text = new Konva.Text(thyroid.text);
    const { x: ox, y: oy } = text.getPosition();
    const line = new Konva.Line({
      ...thyroid.line,
      points: [x + r.width / 2, y + r.height / 2, ox, oy]
    });

    const group = new Konva.Group();
    group.add(path);
    group.add(text);
    group.add(line);
    group.on("mouseover", () => {
      setThyroidHover(path, text, line);
    });
    group.on("mouseout", () => {
      clearThyroidHover(path, text, line);
    });

    organsLayer.add(group);
    stage.add(organsLayer);
  }

  function setThyroidHover(path: Konva.Path, text: Konva.Text, line: Konva.Line) {
    const hoverThyroid = {
      path: {
        stroke: "#469966",
        strokeWidth: 60
      },
      text: {
        shadowColor: "#469966",
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowOffsetY: 0
      },
      line: {
        shadowBlur: 6,
        shadowColor: "#469966",
        shadowOffsetX: 0,
        shadowOffsetY: 0
      }
    };
    path.setAttrs(hoverThyroid.path);
    text.setAttrs(hoverThyroid.text);
    line.setAttrs(hoverThyroid.line);
  }

  function clearThyroidHover(path: Konva.Path, text: Konva.Text, line: Konva.Line) {
    if (!stage) return;
    const thyroid = organsLayer.findOne("Group");
    if (!thyroid) return;
    path.setAttrs({
      stroke: "",
      strokeWidth: 0
    });
    text.setAttrs({
      shadowColor: "",
      shadowBlur: 0,
      shadowOffsetX: 0,
      shadowOffsetY: 0
    });
    line.setAttrs({
      shadowBlur: 0,
      shadowColor: "",
      shadowOffsetX: 0,
      shadowOffsetY: 0
    });
  }

  return {
    humanLayer,
    init
  };
};

interface Organ {
  path: OrganPath;
  text: OrganText;
  line: OrganLine;
}
interface OrganPath extends Konva.Path {}
interface OrganText {}
interface OrganLine {}

const useOrgan = () => {
  const organ = null;

  const init = (organ: Organ) => {};
  function initOrgan() {}

  return {
    init
  };
};
