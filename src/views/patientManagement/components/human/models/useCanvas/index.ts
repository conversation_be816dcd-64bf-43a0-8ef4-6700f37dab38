const useCanvas = () => {
  const odom = ref<HTMLElement | null>(null);
  const canvas = ref<HTMLCanvasElement | null>(null);
  const ctx = ref<CanvasRenderingContext2D | null>(null);
  const dpr = window.devicePixelRatio;

  function initCanvas(
    elementId: string,
    { options }: { options: { width: number; height: number } }
  ) {
    odom.value = document.getElementById(elementId);
    canvas.value = document.createElement("canvas");
    canvas.value.width = options.width;
    canvas.value.height = options.height;

    ctx.value = canvas.value?.getContext("2d");
  }
  return {
    initCanvas,
    canvas,
    ctx
  };
};
