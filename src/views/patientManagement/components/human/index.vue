<script setup lang="ts">
import { useHuman } from "./models/useHuman";
defineOptions({
  name: "HumanComponent"
});
// const humanComponentRef = useTemplateRef("human-component");
// const humanRef = useTemplateRef("human");
const { humanLayer, init } = useHuman();

// const ctx = ref<CanvasRenderingContext2D | null>(null);
// function initCanvas() {
//   if (!humanRef.value || !humanComponentRef.value) return;
//   humanRef.value.width = humanComponentRef.value?.clientWidth - 4;
//   humanRef.value.height = humanComponentRef.value?.clientHeight - 4;
//   ctx.value = humanRef.value.getContext("2d");
//   if (!ctx.value) return;
//   //填充样式-透明背景
//   ctx.value.fillStyle = "transparent";
//   //描边
//   ctx.value.strokeStyle = "#000";
//   ctx.value.lineWidth = 1;
//   //绘制矩形
//   ctx.value.strokeRect(0, 0, humanRef.value.width, humanRef.value.height);
//   initHuman();
// }

// function initHuman() {
//   if (!ctx.value || !humanRef.value) return;
//   const img = new Image();
//   img.src = `${humanImage.base64.prefix}${humanImage.base64.data}`;
//   const width = humanRef.value.width / 2.5;
//   console.log(width);

//   img.onload = () => {
//     ctx.value?.drawImage(
//       img,
//       2,
//       2,
//       width * dpr.value,
//       (humanRef.value?.height || 200) * dpr.value - 10
//     );
//   };
// }

onMounted(() => {
  // initCanvas();
  init("human-component-canvas");
});
</script>
<template>
  <div class="human-component" ref="human-component">
    <!-- <svg width="400" height="848" viewBox="0 0 100 200" id="human-component-svg">
      <path
        :d="body.path_d"
        :fill="body.path_fill"
        :style="{
          'z-index': 9
        }"
      />
    </svg> -->
    <div id="human-component-canvas" ref="human"></div>
  </div>
</template>
<style lang="less" scoped>
.human-component {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  #human-component-svg {
    position: absolute;
    top: 0;
    left: 0;
    margin: 0;
  }
  #human-component-canvas {
    width: 100%;
    height: 100%;
    // position: absolute;
    // top: 0;
    // left: 0;
  }
}
</style>
