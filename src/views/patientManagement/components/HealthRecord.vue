<script lang="ts" setup>
import { requestGetAccountOfDoctor } from "@/apis/account";
import {
  requestAddandUpdateOrgan,
  requestDelete<PERSON>rgan,
  requestGetPatientOrganInfo
} from "@/apis/healthRecord";
import Human from "./human.vue";
import { useStore } from "@/store";
import moment from "moment";
import { useDialog } from "@/composables/useDialog";
import { useStorePatient } from "@/store/patient";
import { getAge } from "@/utils/human";
interface OrganMsg {
  name: string;
  abnormalInformation?: string;
}
const store = useStore();

// const patientId = computed<number>(() => {
//   return Number(route.query.patientId) ?? 0;
// });
const { storePatientId, storePatient } = useStorePatient();

const patientInfo = computed(() => storePatient.value!);
const age = computed(() => {
  if (storePatient.value !== null) {
    // storePatient.value.birthday
    return getAge(storePatient.value.birthday);
    // return getAge("2024-01-10 00:00:00");
  }
});
const {
  dialog: editDialog,
  closeDialog: closeEditDialog,
  openDialog: openEditDialog
} = useDialog();
const {
  dialog: printDialog,
  closeDialog: closePrintDialog,
  openDialog: openPrintDialog
} = useDialog();

const dialogFormData = reactive<{
  name: string;
  abnormalInformation: string | undefined;
}>({
  name: "",
  abnormalInformation: ""
});

// 打印的配置
const printConfig = {
  id: "print",
  popTitle: "", // 打印配置页上方的标题
  extraHead: "", // 最上方的头部文字，附加在head标签上的额外标签，使用逗号分割
  preview: false, // 是否启动预览模式，默认是false
  previewTitle: "", // 打印预览的标题
  previewPrintBtnLabel: "预览结束，开始打印", // 打印预览的标题下方的按钮文本，点击可进入打印
  zIndex: 20002, // 预览窗口的z-index，默认是20002，最好比默认值更高
  previewBeforeOpenCallback() {
    console.log("正在加载预览窗口！");
  }, // 预览窗口打开之前的callback
  previewOpenCallback() {
    console.log("已经加载完预览窗口，预览打开了！");
  }, // 预览窗口打开时的callback
  beforeOpenCallback() {
    console.log("开始打印之前！");
  }, // 开始打印之前的callback
  openCallback() {
    console.log("执行打印了！");
  }, // 调用打印时的callback
  closeCallback() {
    console.log("关闭了打印工具！");
  }, // 关闭打印的callback(无法区分确认or取消)
  clickMounted() {
    console.log("点击v-print绑定的按钮了！");
  },
  // url: 'http://localhost:8080/', // 打印指定的URL，确保同源策略相同
  // asyncUrl (reslove) {
  //   setTimeout(() => {
  //     reslove('http://localhost:8080/')
  //   }, 2000)
  // },
  standard: "",
  extarCss: ``
};

const secretIdCard = computed(() => {
  const startStr = patientInfo.value?.idCard.substring(0, 3); //前面截取3位
  const ecdStr = patientInfo.value?.idCard.substring(patientInfo.value?.idCard.length - 3); //后面截取3位
  const resStr = `${startStr}************${ecdStr}`;
  return resStr;
});

const isAdmin = ref(false); // 判断是否管理员，用于显示身份证

const patientOrganInfo = ref<OrganMsg[]>([]);

// 判断是否为管理员，用于隐藏身份证号

async function loadIsAdmin() {
  const res = await requestGetAccountOfDoctor();
  isAdmin.value = res.data.isAdmin;
}
// 取消异常
const onEditDialogCancelOrganBtnClick = async () => {
  await requestDeleteOrgan(dialogFormData.name, {
    patientId: storePatientId.value
  });
  ElMessage({
    message: "取消成功！",
    type: "success"
  });
  // 对patientOrganInfo存储的数据进行删除
  const index = patientOrganInfo.value.findIndex(ele => {
    return ele.name == dialogFormData.name;
  });
  patientOrganInfo.value.splice(index, 1); //删除
  closeEditDialog();
};

// 保存
const onEditDialogSaveOrganBtnClick = async (name: string) => {
  await requestAddandUpdateOrgan(dialogFormData.name, {
    patientId: storePatientId.value,
    abnormalInformation: dialogFormData.abnormalInformation
  });
  ElMessage({
    message: "保存成功！",
    type: "success"
  });

  const editPatientOrganInfoIndex = patientOrganInfo.value.findIndex(ele => ele.name == name);
  if (editPatientOrganInfoIndex == -1) {
    patientOrganInfo.value.push({
      name: dialogFormData.name,
      abnormalInformation: dialogFormData.abnormalInformation
    });
  }
  patientOrganInfo.value[editPatientOrganInfoIndex].abnormalInformation =
    dialogFormData.abnormalInformation;

  closeEditDialog();
};

// 接受子组件传来的name值
const onOrganBtnClick = (name: string) => {
  dialogFormData.abnormalInformation = "";
  const index = patientOrganInfo.value.findIndex(ele => {
    return ele.name == name;
  });
  if (index != -1) {
    dialogFormData.abnormalInformation = patientOrganInfo.value[index].abnormalInformation;
  }
  dialogFormData.name = name;
  openEditDialog();
};

// 初始化请求数据
const getBadPatientOrgan = async () => {
  const { data } = await requestGetPatientOrganInfo(storePatientId.value);
  if (data.length == 0) return;
  patientOrganInfo.value = data;
};

// 监听patientOrganInfo数据变化，提交到store里，用于更新报告里面的数据
watch(
  () => patientOrganInfo,
  v => {
    store.commit("setPatientOrganData", v);
  },
  { deep: true }
);

onMounted(() => {
  loadIsAdmin();
  getBadPatientOrgan();
});
</script>
<template>
  <div class="health-record">
    <div class="info_form">
      <template v-if="patientInfo !== null">
        <el-form :model="patientInfo" label-width="90px">
          <el-form-item label="病人号：">
            {{ storePatientId }}
          </el-form-item>
          <el-form-item label="姓名：">
            {{ patientInfo!.name }}
          </el-form-item>
          <el-form-item label="性别：">
            {{ patientInfo!.sex }}
          </el-form-item>
          <el-form-item label="出生日期：">
            {{ moment(patientInfo!.birthday).format("YYYY-MM-DD") }}
          </el-form-item>
          <el-form-item label="年龄：">
            {{ age }}
          </el-form-item>
          <el-form-item label="身份证：">
            {{ isAdmin ? patientInfo!.idCard : secretIdCard }}
          </el-form-item>
          <el-form-item label="手机号：">
            {{ patientInfo!.phone }}
          </el-form-item>
          <el-form-item label="地址：">
            {{ patientInfo!.address }}
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="openPrintDialog">打印</el-button>
          </el-form-item>
        </el-form></template
      >
    </div>
    <human :abnormal-organs="patientOrganInfo.map(i => i.name)" @click="onOrganBtnClick" />
    <!-- <HumanComponent /> -->
    <div>
      <el-dialog
        custom-class="custom-dialog"
        title="预览"
        width="750px"
        v-model="printDialog.visible"
      >
        <div class="print-warp" id="print">
          <div style="page-break-after: always">
            <human :abnormal-organs="patientOrganInfo.map(i => i.name)" />
          </div>
        </div>
        <template #footer>
          <span
            ><el-button @click="closePrintDialog">取消</el-button>
            <el-button type="primary" v-print="printConfig">打印</el-button></span
          >
        </template>
      </el-dialog>
    </div>
    <el-dialog title="编辑" v-model="editDialog.visible" width="30%" :close-on-click-modal="false">
      <el-form :model="dialogFormData">
        <el-form-item label="异常信息" prop="abnormalInformation">
          <el-input
            v-model="dialogFormData.abnormalInformation"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            type="danger"
            @click="onEditDialogCancelOrganBtnClick"
            v-if="patientOrganInfo.findIndex(ele => ele.name == dialogFormData.name) != -1"
            >取消异常</el-button
          >
          <el-button @click="closeEditDialog">取消</el-button>
          <el-button type="primary" @click="onEditDialogSaveOrganBtnClick(dialogFormData.name)"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.health-record {
  height: 100%;
  display: grid;
  grid-template-columns: 3fr 7fr;
  grid-template-rows: 1fr;
  box-sizing: border-box;
}
// :deep(.custom-dialog > .el-dialog__body) {
//   flex: 1;
//   overflow: hidden;
//   display: flex;
//   gap: 1rem;
//   padding-bottom: 0.5rem !important;
// }
// .print-warp {
//   overflow-x: hidden;
// }
// .right button:nth-child(1) {
//   margin-left: 12px;
// }
// .left button:nth-child(1) {
//   margin-left: 12px;
// }

// .lines {
//   position: absolute;
//   width: 100%;
//   height: 100%;
//   top: 0;
//   left: 0;
//   z-index: 10;
// }
// .chart {
//   position: relative;
//   display: grid;
//   grid-template-columns: 1fr 50% 1fr;
//   align-items: center;
//   justify-items: center;

//   > .human {
//     height: 100%;
//     overflow: hidden;
//     position: relative;
//     z-index: 9;
//   }
//   > .button-wrap {
//     display: flex;
//     flex-direction: column;
//     gap: 53px;
//     z-index: 11;
//   }
//   > .left {
//     justify-self: flex-end;
//   }
//   > .right {
//     justify-self: flex-start;
//   }
// }
</style>
//
<style media="print" lang="less" scoped>
// @page {
//   /* this affects the margin in the printer settings */
//   display: block;
//   overflow: hidden;
//   margin: 1cm 1cm 1cm 1cm;
//   size: auto;
// }
// #chart {
//   position: relative;
//   display: grid;
//   grid-template-columns: 1fr 85% 1fr;
//   align-items: center;
//   justify-items: center;
//   // width: 600px;
//   // height: 842px;
//   > .human {
//     height: 100%;
//     overflow: hidden;
//     position: relative;
//     z-index: 9;
//   }
//   > .button-wrap {
//     display: flex;
//     flex-direction: column;
//     gap: 53px;
//     z-index: 11;
//   }
//   > .left {
//     justify-self: flex-end;
//   }
//   > .right {
//     justify-self: flex-start;
//   }

//   .right button:nth-child(1) {
//     margin-left: 12px;
//   }
//   .left button:nth-child(1) {
//     margin-left: 12px;
//   }
//   .container {
//     height: 100%;
//     display: grid;
//     grid-template-columns: 30% 70%;
//     box-sizing: border-box;
//   }
//   .lines {
//     position: absolute;
//     width: 100%;
//     height: 100%;
//     top: 0;
//     left: 0;
//     z-index: 10;
//   }
// }
//
</style>
