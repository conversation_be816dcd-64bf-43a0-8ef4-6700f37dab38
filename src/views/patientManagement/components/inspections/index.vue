<script lang="ts" setup>
import { useStorePatient } from "@/store/patient";
import { useInspections } from "./models/useInspections";
import { Inspection } from "@/types/inspection";
import EmptyComponent from "@/components/empty/index.vue";
import LazyLoadComponent from "@/components/LazyLoadComponent.vue";
import { ClassifyItem } from "./models/useClassify";
import { useTopXTimesOptions } from "../../models/timesOptions";
import { useDialog } from "@/composables/useDialog";
import InspectionPrintSortComponent from "@/views/patientManagement/components/inspectionPrintSort/index.vue";
import { useStoreInspectionsForPrint } from "@/store/storeInspectionsForPrint";
import { useStoreInspectionSort } from "@/store/storeInspectionSort";
import { useDebounceFn } from "@vueuse/core";
import { deepClone } from "@/utils/collection";

const inspectionDetailTableComponent = defineAsyncComponent(
  () => import("../inspectionDetails/components/inspectionDetailTable/index.vue")
);

const inspectionDetailEchartsComponent = defineAsyncComponent(
  () => import("../inspectionDetails/components/inspectionDetailEcharts/index.vue")
);
const pageLoading = ref(false);
const { storePatientId } = useStorePatient();
const {
  classify,
  filterByTopX,
  filterByItemName,
  filters,
  inspectionsDetails,
  resetInspections,
  loadInspections,
  viewInspectionsDetails
} = useInspections();

const currentClassifyItem = ref<ClassifyItem | null>(null);
const isShowCharts = ref<boolean>(true);

const classifyRef = useTemplateRef("classifyRef");
const classifyItemsRef = useTemplateRef("classifyItemsRef");

const { topXTimesOptions } = useTopXTimesOptions();
async function onFiltersDateChange() {
  const { startTime, endTime } = filters.value;
  await loadInspections(storePatientId.value, {
    startTime,
    endTime
  });
}
function onActiveClassifyItem(classifyItem: ClassifyItem) {
  classifyItemsRef.value?.scrollTo({ top: 0, behavior: "smooth" });
  currentClassifyItem.value = classifyItem;
  viewInspectionsDetails(classifyItem.itemCode);
}

const { setInspectionsForPrint, storeInspectionsForPrint, clearInspectionsForPrint } =
  useStoreInspectionsForPrint();

// 主函数
function onCheckClassifyItem(itemCode: Inspection["itemCode"], index: number) {
  ensureTopFilter();
  ensureActiveClassify(itemCode, index);

  if (currentClassifyItem.value == null) return;

  toggleClassifySelection(itemCode, index);
}

function ensureTopFilter() {
  if (filters.value.top === 0) {
    filters.value.top = 5;
    onChangeTop(5);
  }
}
// 处理分类的选中状态切换
function toggleClassifySelection(itemCode: Inspection["itemCode"], index: number) {
  const checkedItem = storeInspectionsForPrint.value.get(itemCode);
  if (checkedItem == undefined) {
    // 全选：深拷贝数据以避免引用问题
    const detailsMap = new Map<Inspection["itemNameCode"], Inspection[]>();
    classify.value[index].data.forEach((value, key) => {
      detailsMap.set(key, deepClone(value));
    });
    storeInspectionsForPrint.value.set(itemCode, detailsMap);
  } else {
    // 取消选中：如果是部分选中状态，则变为全不选
    if (checkedItem.size < classify.value[index].data.size) {
      storeInspectionsForPrint.value.delete(itemCode);
    } else {
      // 如果是全选状态，则变为全不选
      storeInspectionsForPrint.value.delete(itemCode);
    }
    setInspectionsForPrint(storeInspectionsForPrint.value);
  }
}
function ensureActiveClassify(itemCode: Inspection["itemCode"], index: number) {
  if (currentClassifyItem.value?.itemCode !== itemCode) {
    onActiveClassifyItem(classify.value[index]);
  }
}

// 主函数
async function onCheckDetail(itemNameCode: Inspection["itemNameCode"], inspections: Inspection[]) {
  const result = handleTopFilter(inspections);
  const itemCode = inspections[0].itemCode;

  ensureActiveClassifyForDetail(itemCode);

  if (currentClassifyItem.value === null) return;

  toggleDetailSelection(itemNameCode, result, currentClassifyItem.value.itemCode);
}

// 处理顶部过滤并返回结果
function handleTopFilter(inspections: Inspection[]): Inspection[] {
  if (filters.value.top === 0) {
    filters.value.top = 5;
    onChangeTop(5);
    return inspections.slice(0, filters.value.top);
  }
  return inspections;
}

// 确保正确的分类项被激活
function ensureActiveClassifyForDetail(itemCode: Inspection["itemCode"]) {
  if (currentClassifyItem.value?.itemCode !== itemCode) {
    const index = classify.value.findIndex(i => i.itemCode === itemCode);
    onActiveClassifyItem(classify.value[index]);
  }
}

// 处理检验明细的选中状态
function toggleDetailSelection(
  itemNameCode: Inspection["itemNameCode"],
  inspections: Inspection[],
  classifyItemCode: Inspection["itemCode"]
) {
  const checkedItem = storeInspectionsForPrint.value.get(classifyItemCode);

  if (checkedItem === undefined) {
    // 新增分类和明细
    const item = new Map<Inspection["itemNameCode"], Inspection[]>();
    item.set(itemNameCode, inspections);
    storeInspectionsForPrint.value.set(classifyItemCode, deepClone(item));
  } else {
    // 处理已存在分类的明细
    const checkedDetail = checkedItem.get(itemNameCode);
    if (checkedDetail === undefined) {
      checkedItem.set(itemNameCode, inspections);
    } else {
      checkedItem.delete(itemNameCode);
    }
    setInspectionsForPrint(storeInspectionsForPrint.value);
  }
}

// 更新显示内容
function updateDisplay(top: 0 | 1 | 2 | 3 | 4 | 5) {
  isShowCharts.value = false;
  filterByTopX(top);
  onActiveClassifyItem(classify.value[0]);
  setTimeout(() => {
    isShowCharts.value = true;
  });
}

async function onChangeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
  if (top == 0 && storeInspectionsForPrint.value.size != 0) {
    try {
      const action = await ElMessageBox.confirm("已勾选打印项不可查看全部", "提示", {
        confirmButtonText: "继续查看",
        cancelButtonText: "保留勾选",
        type: "warning"
      });

      if (action === "confirm") {
        storeInspectionsForPrint.value = new Map();
        filters.value.top = top;
        updateDisplay(top);
        pageLoading.value = true;
      } else {
        filters.value.top = top;
      }
    } catch (error) {
      console.error(error);
    } finally {
      pageLoading.value = false;
    }

    return;
  }
  pageLoading.value = false;
  filters.value.top = top;
  updateDisplay(top);
}

function onFiltersItemNameClear() {
  filters.value.itemName = "";
  classifyItemsRef.value?.scrollTo({ top: 0, behavior: "smooth" });
  classifyRef.value?.scrollTo({ top: 0, behavior: "smooth" });
  onSearch();
}

const onSearch = useDebounceFn(() => {
  filterByItemName();
  filterByTopX(filters.value.top);
  onActiveClassifyItem(classify.value[0]);
}, 300); // 300ms 的防抖延迟

function detailIsChecked(itemNameCode: Inspection["itemNameCode"]): boolean {
  if (currentClassifyItem.value == null) return false;
  const { itemCode } = currentClassifyItem.value;
  const checkedItem = storeInspectionsForPrint.value.get(itemCode);
  if (checkedItem == undefined) {
    return false;
  }
  return checkedItem.has(itemNameCode);
}

function classifyItemIsChecked(itemCode: Inspection["itemCode"]): boolean {
  const item = storeInspectionsForPrint.value.get(itemCode);
  const classifyItem = classify.value.find(item => item.itemCode === itemCode);

  return classifyItem?.data.size == item?.size;
}

function getIndeterminateState(itemCode: Inspection["itemCode"]): boolean {
  const checkedItem = storeInspectionsForPrint.value.get(itemCode);
  const targetClassifyItem = classify.value.find(item => item.itemCode === itemCode);

  if (checkedItem != undefined && targetClassifyItem) {
    return checkedItem.size > 0 && checkedItem.size < targetClassifyItem.data.size;
  }
  return false;
}

function getVisitTypeTag(visitTypeName: Inspection["visitTypeName"]) {
  return `${visitTypeName == "门诊" ? "c" : "h"}`;
}

function valueIsValid(data: Inspection[]): boolean {
  return data.length > 1 && data.every(item => !isNaN(Number(item.itemValue)));
}

const { dialog, openDialog, closeDialog } = useDialog();
const { loadData, save } = useStoreInspectionSort();

async function onSaveSort() {
  await save();
  await loadData();
  setInspectionsForPrint(storeInspectionsForPrint.value);
  ElMessage.success("保存成功");
  closeDialog();
}

function onOpenSortDialog() {
  if (storeInspectionsForPrint.value.size == 0) {
    ElMessage.warning("请先选择要打印的检验项目");
    return;
  }
  openDialog();
}

async function onReset() {
  pageLoading.value = true;
  resetInspections();
  clearInspectionsForPrint();
  await loadInspections(storePatientId.value, {
    startTime: filters.value.startTime,
    endTime: filters.value.endTime
  });
  onActiveClassifyItem(classify.value[0]);

  pageLoading.value = false;
}

onMounted(async () => {
  pageLoading.value = true;
  await loadInspections(storePatientId.value, {
    startTime: filters.value.startTime,
    endTime: filters.value.endTime
  });

  if (classify.value.length != 0) {
    onActiveClassifyItem(classify.value[0]);
  }
  if (filters.value.top == 0) {
    storeInspectionsForPrint.value = new Map();
  }

  pageLoading.value = false;
});
</script>
<template>
  <div class="inspections-page" v-loading="pageLoading">
    <header class="inspections-page__header">
      <ul class="header__filters">
        <li class="filters__condition">
          <el-date-picker
            v-model="filters.startTime"
            type="date"
            placeholder="检验报告开始日期"
            style="width: 126px"
            @change="onFiltersDateChange"
            :clearable="false"
            :editable="false"
          />
        </li>
        <li class="filters__condition">
          <div style="width: 1rem">-</div>
          <el-date-picker
            v-model="filters.endTime"
            type="date"
            placeholder="检验报告结束日期"
            style="width: 126px"
            @change="onFiltersDateChange"
            :clearable="false"
            :editable="false"
          />
        </li>
        <li class="header__filter">
          <div class="filter__value">
            <el-select
              :model-value="filters.top"
              @change="onChangeTop"
              placeholder="全部结果"
              style="width: 140px"
            >
              <el-option
                v-for="option in topXTimesOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </li>
        <li class="header__filter">
          <div class="filter__value">
            <el-input
              style="width: 240px"
              v-model="filters.itemName"
              placeholder="搜索检验分类"
              clearable
              @clear="onFiltersItemNameClear"
              @keyup.enter="onSearch"
            ></el-input>
          </div>
        </li>
      </ul>
      <div class="operation">
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button type="primary" text bg @click="onReset">重置</el-button>
        <el-divider direction="vertical" />
        <el-button @click="onOpenSortDialog">打印排序</el-button>
      </div>
    </header>

    <div class="inspections-page__classify" ref="classifyRef">
      <EmptyComponent
        v-if="classify.length == 0"
        class="empty-container"
        description="暂无检验分类 请尝试修改查询条件"
      ></EmptyComponent>
      <template v-if="classify.length != 0">
        <div
          v-for="(classifyItem, index) in classify"
          class="classifyItem"
          :class="
            classifyItem.itemCode == currentClassifyItem?.itemCode ? 'classifyItem-active' : ''
          "
          @click="onActiveClassifyItem(classifyItem)"
        >
          <el-checkbox
            :key="classifyItem.itemCode"
            :model-value="classifyItemIsChecked(classifyItem.itemCode)"
            @change="onCheckClassifyItem(classifyItem.itemCode, index)"
            :indeterminate="getIndeterminateState(classifyItem.itemCode)"
          />
          {{ classifyItem.itemName }} {{ classifyItem.viewVisitTypeName }}
        </div>
      </template>
    </div>
    <div class="inspections-page__details" ref="classifyItemsRef">
      <EmptyComponent
        v-if="inspectionsDetails.size == 0 || currentClassifyItem == null"
        class="empty-container"
        description="暂无检验明细 请尝试修改查询条件"
      ></EmptyComponent>
      <template v-for="detail in inspectionsDetails">
        <div class="inspectionsDetail">
          <div class="base-info">
            <div class="base-info-header">
              <el-checkbox
                :key="detail[0]"
                :model-value="detailIsChecked(detail[0])"
                @change="onCheckDetail(detail[0], detail[1])"
              />
              <div class="title">
                {{ detail[1][0].itemNameCn }} {{ getVisitTypeTag(detail[1][0].visitTypeName) }}
              </div>
            </div>

            <el-tag type="primary" size="large">
              参考值：
              {{ detail[1][0].normalRefValueText }}
            </el-tag>
            <el-tag type="info" effect="plain" size="large" v-if="detail[1][0].itemUnit">
              单位：
              {{ detail[1][0].itemUnit }}
            </el-tag>
          </div>
          <LazyLoadComponent
            :asyncComponent="inspectionDetailTableComponent"
            :data="detail[1]"
            style="height: 61px"
          />

          <LazyLoadComponent
            v-if="!pageLoading && isShowCharts && valueIsValid(detail[1])"
            :asyncComponent="inspectionDetailEchartsComponent"
            :data="detail[1]"
            :chartTitle="detail[1][0].itemNameCn"
            :skeletonRows="4"
          />
        </div>
      </template>
    </div>
    <el-dialog v-model="dialog.visible" title="排序" width="50%">
      <InspectionPrintSortComponent />
      <template #footer>
        <el-button @click="onSaveSort" type="primary">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.inspections-page {
  height: 100%;
  display: grid;
  grid-template-columns: 260px 1fr;
  grid-template-rows: 48px 1fr;
  grid-template-areas:
    "header header"
    "classify details";
  gap: 10px;
  box-sizing: border-box;
  .inspections-page__header {
    grid-area: header;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 10px;
    border-radius: 4px;
    .header__filters {
      display: flex;
      align-items: center;
      gap: 10px;
      .filters__condition {
        display: flex;
        align-items: center;
      }
    }
  }
  .inspections-page__classify {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: auto;
    border-radius: 4px;
    grid-area: classify;
    gap: 10px 0;
    box-sizing: border-box;
    padding: 10px;
    background-color: hsl(0, 0%, 98%);
    :deep(.el-checkbox) {
      width: 20px;
      height: 20px;
      transform: translateY(3px);
      .el-checkbox__inner {
        display: flex;
        width: 20px;
        height: 20px;
        &::after {
          height: 9px;
          transform: rotate(45deg) scaleY(1) translateX(3px);
        }
        &::before {
          transform: translateY(3px) scale(0.5);
        }
      }
    }
    .classifyItem {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 14px;
      background-color: #fff;
      color: #272727;
      border-radius: 4px;
      outline: 1px solid #e4e7ed;
      &:hover {
        cursor: pointer;
        background-color: var(--el-color-primary-light-5);
      }
    }
    .classifyItem-active {
      background-color: var(--el-color-primary-light-7);
    }
  }
  .inspections-page__details {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    grid-area: details;
    background-color: hsl(0, 0%, 98%);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 10px;
    gap: 10px;
    .inspectionsDetail {
      display: grid;
      grid-template-rows: 32px 61px auto;
      gap: 10px;
      outline: 1px solid #e4e7ed;
      padding: 10px;
      border-radius: 8px;
      background-color: #fff;
      // min-width: 100%;
      width: max-content;
      box-sizing: border-box;

      .base-info {
        display: flex;
        align-items: center;
        gap: 10px;

        .base-info-header {
          display: flex;
          align-items: center;
          gap: 10px;
          flex-shrink: 0;
          font-size: 1.2rem;

          :deep(.el-checkbox) {
            width: 20px;
            height: 20px;

            .el-checkbox__inner {
              width: 20px;
              height: 20px;
              &::after {
                height: 9px;
                transform: rotate(45deg) scaleY(1) translateX(3px);
              }
            }
          }
          .title {
            padding: 2px;
            border-radius: 4px;
            background-color: var(--el-color-primary-light-9);
          }
        }
      }
    }
  }
}
</style>
