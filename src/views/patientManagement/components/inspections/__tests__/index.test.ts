import { describe, it, expect, beforeEach, vi } from "vitest";
import { nextTick, ref } from "vue";
import { ClassifyItem } from "../models/useClassify";
import { Inspection } from "@/types/inspection";
import { deepClone } from "@/utils/collection";

function createMockInspection(
  id: number,
  itemCode = "001",
  itemNameCode = `test${id}`
): Inspection {
  return {
    id,
    itemCode,
    itemName: "test",
    itemNameCn: "test",
    itemNameEn: "test",
    itemNameCode,
    itemUnit: "",
    compositeItemSn: "",
    execDeptName: "",
    highValue: null,
    inpatientOrdNo: null,
    warnLowValue: null
  } as Inspection;
}

describe("检验项目组件选择功能", () => {
  const mockClassify = ref([
    {
      itemCode: "001",
      itemName: "血常规",
      data: new Map([["test1", []]]),
      viewVisitTypeName: "c"
    },
    {
      itemCode: "002",
      itemName: "尿常规",
      data: new Map([["test2", []]]),
      viewVisitTypeName: "c"
    }
  ] as unknown as ClassifyItem[]);

  const mockFilters = ref({
    top: 0,
    itemName: "",
    startTime: new Date(),
    endTime: new Date()
  });

  const mockStoreInspectionsForPrint = ref(new Map<string, Map<string, Inspection[]>>());

  const mockSetInspectionsForPrint = vi.fn();
  const mockOnChangeTop = vi.fn();

  const currentClassifyItem = ref<ClassifyItem | null>(null);

  beforeEach(() => {
    mockStoreInspectionsForPrint.value.clear();
    currentClassifyItem.value = null;
    mockFilters.value.top = 0;
    vi.clearAllMocks();
  });

  describe("顶部过滤器功能", () => {
    it("当顶部过滤器为0时，应该设置为5条", () => {
      function ensureTopFilter() {
        if (mockFilters.value.top === 0) {
          mockFilters.value.top = 5;
          mockOnChangeTop();
        }
      }

      ensureTopFilter();

      expect(mockFilters.value.top).toBe(5);
      expect(mockOnChangeTop).toHaveBeenCalledTimes(1);
    });

    it("当顶部过滤器已设置时，不应该改变值", () => {
      mockFilters.value.top = 3;

      function ensureTopFilter() {
        if (mockFilters.value.top === 0) {
          mockFilters.value.top = 5;
          mockOnChangeTop();
        }
      }

      ensureTopFilter();

      expect(mockFilters.value.top).toBe(3);
      expect(mockOnChangeTop).not.toHaveBeenCalled();
    });
  });

  describe("分类项目激活功能", () => {
    it("当选择不同的分类项目时，应该激活新项目", () => {
      function ensureActiveClassify(itemCode: string, index: number) {
        if (currentClassifyItem.value?.itemCode !== itemCode) {
          currentClassifyItem.value = mockClassify.value[index];
        }
      }

      ensureActiveClassify("001", 0);

      expect(currentClassifyItem.value).toEqual(mockClassify.value[0]);
    });

    it("当选择相同的分类项目时，不应该改变当前激活项", () => {
      currentClassifyItem.value = mockClassify.value[0];

      function ensureActiveClassify(itemCode: string, index: number) {
        if (currentClassifyItem.value?.itemCode !== itemCode) {
          currentClassifyItem.value = mockClassify.value[index];
        }
      }

      ensureActiveClassify("001", 0);

      expect(currentClassifyItem.value).toEqual(mockClassify.value[0]);
    });
  });

  describe("分类选择切换功能", () => {
    it("当分类未被选中时，应该全选所有明细", () => {
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockClassify.value[0].data = classifyData;

      function toggleClassifySelection(itemCode: string, index: number) {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (checkedItem == undefined) {
          // 全选：深拷贝数据以避免引用问题
          const detailsMap = new Map<Inspection["itemNameCode"], Inspection[]>();
          mockClassify.value[index].data.forEach((value, key) => {
            detailsMap.set(key, deepClone(value));
          });
          mockStoreInspectionsForPrint.value.set(itemCode, detailsMap);
        } else {
          // 取消选中：如果是部分选中状态，则变为全不选
          if (checkedItem.size < mockClassify.value[index].data.size) {
            mockStoreInspectionsForPrint.value.delete(itemCode);
          } else {
            // 如果是全选状态，则变为全不选
            mockStoreInspectionsForPrint.value.delete(itemCode);
          }
        }
        mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
      }

      toggleClassifySelection("001", 0);

      const selectedCategory = mockStoreInspectionsForPrint.value.get("001");
      expect(selectedCategory?.size).toBe(2); // 应该包含所有明细
      expect(selectedCategory?.get("test1")).toBeDefined();
      expect(selectedCategory?.get("test2")).toBeDefined();
      expect(mockSetInspectionsForPrint).toHaveBeenCalled();
    });

    it("当分类处于部分选中状态时，应该变为全不选", () => {
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockClassify.value[0].data = classifyData;

      // 设置部分选中状态
      const partialChecked = new Map([["test1", [createMockInspection(1)]]]);
      mockStoreInspectionsForPrint.value.set("001", partialChecked);

      function toggleClassifySelection(itemCode: string, index: number) {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (checkedItem == undefined) {
          const detailsMap = new Map<Inspection["itemNameCode"], Inspection[]>();
          mockClassify.value[index].data.forEach((value, key) => {
            detailsMap.set(key, deepClone(value));
          });
          mockStoreInspectionsForPrint.value.set(itemCode, detailsMap);
        } else {
          if (checkedItem.size < mockClassify.value[index].data.size) {
            mockStoreInspectionsForPrint.value.delete(itemCode);
          } else {
            mockStoreInspectionsForPrint.value.delete(itemCode);
          }
        }
        mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
      }

      toggleClassifySelection("001", 0);

      expect(mockStoreInspectionsForPrint.value.get("001")).toBeUndefined();
      expect(mockSetInspectionsForPrint).toHaveBeenCalled();
    });

    it("当分类处于全选状态时，应该变为全不选", () => {
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockClassify.value[0].data = classifyData;

      // 设置全选状态
      const allChecked = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockStoreInspectionsForPrint.value.set("001", allChecked);

      function toggleClassifySelection(itemCode: string, index: number) {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (checkedItem == undefined) {
          const detailsMap = new Map<Inspection["itemNameCode"], Inspection[]>();
          mockClassify.value[index].data.forEach((value, key) => {
            detailsMap.set(key, deepClone(value));
          });
          mockStoreInspectionsForPrint.value.set(itemCode, detailsMap);
        } else {
          if (checkedItem.size < mockClassify.value[index].data.size) {
            mockStoreInspectionsForPrint.value.delete(itemCode);
          } else {
            mockStoreInspectionsForPrint.value.delete(itemCode);
          }
        }
        mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
      }

      toggleClassifySelection("001", 0);

      expect(mockStoreInspectionsForPrint.value.get("001")).toBeUndefined();
      expect(mockSetInspectionsForPrint).toHaveBeenCalled();
    });
  });

  describe("检验项目选择完整流程", () => {
    it("应该正确处理完整的选择流程", async () => {
      async function onCheckClassifyItem(itemCode: string, index: number) {
        if (mockFilters.value.top === 0) {
          mockFilters.value.top = 5;
          mockOnChangeTop();
        }

        if (currentClassifyItem.value?.itemCode !== itemCode) {
          currentClassifyItem.value = mockClassify.value[index];
        }

        if (currentClassifyItem.value == null) return;

        await nextTick();

        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (checkedItem == undefined) {
          mockStoreInspectionsForPrint.value.set(itemCode, mockClassify.value[index].data);
        } else {
          mockStoreInspectionsForPrint.value.delete(itemCode);
        }
        mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
      }

      await onCheckClassifyItem("001", 0);

      expect(mockFilters.value.top).toBe(5);
      expect(currentClassifyItem.value).toEqual(mockClassify.value[0]);
      expect(mockStoreInspectionsForPrint.value.get("001")).toEqual(mockClassify.value[0].data);
      expect(mockOnChangeTop).toHaveBeenCalled();
      expect(mockSetInspectionsForPrint).toHaveBeenCalled();
    });
  });

  describe("检验明细选择功能", () => {
    describe("顶部过滤器处理", () => {
      it("当顶部过滤器为0时，应该返回前5条数据", () => {
        const mockInspections = Array(10)
          .fill(null)
          .map((_, i) => createMockInspection(i));

        function handleTopFilter(inspections: Inspection[]): Inspection[] {
          if (mockFilters.value.top === 0) {
            mockFilters.value.top = 5;
            mockOnChangeTop();
            return inspections.slice(0, mockFilters.value.top);
          }
          return inspections;
        }

        const result = handleTopFilter(mockInspections);

        expect(result.length).toBe(5);
        expect(mockFilters.value.top).toBe(5);
        expect(mockOnChangeTop).toHaveBeenCalled();
      });

      it("当顶部过滤器已设置时，应该返回原始数据", () => {
        mockFilters.value.top = 3;
        const mockInspections = Array(5)
          .fill(null)
          .map((_, i) => createMockInspection(i));

        function handleTopFilter(inspections: Inspection[]): Inspection[] {
          if (mockFilters.value.top === 0) {
            mockFilters.value.top = 5;
            mockOnChangeTop();
            return inspections.slice(0, mockFilters.value.top);
          }
          return inspections;
        }

        const result = handleTopFilter(mockInspections);

        expect(result.length).toBe(5);
        expect(mockFilters.value.top).toBe(3);
        expect(mockOnChangeTop).not.toHaveBeenCalled();
      });
    });

    describe("明细分类激活功能", () => {
      it("当选择不同分类的明细时，应该激活对应分类", () => {
        function ensureActiveClassifyForDetail(itemCode: string) {
          if (currentClassifyItem.value?.itemCode !== itemCode) {
            const index = mockClassify.value.findIndex(i => i.itemCode === itemCode);
            currentClassifyItem.value = mockClassify.value[index];
          }
        }

        ensureActiveClassifyForDetail("002");

        expect(currentClassifyItem.value).toEqual(mockClassify.value[1]);
      });

      it("当选择当前分类的明细时，不应该改变激活分类", () => {
        currentClassifyItem.value = mockClassify.value[0];

        function ensureActiveClassifyForDetail(itemCode: string) {
          if (currentClassifyItem.value?.itemCode !== itemCode) {
            const index = mockClassify.value.findIndex(i => i.itemCode === itemCode);
            currentClassifyItem.value = mockClassify.value[index];
          }
        }

        ensureActiveClassifyForDetail("001");

        expect(currentClassifyItem.value).toEqual(mockClassify.value[0]);
      });
    });

    describe("明细选择切换功能", () => {
      it("当分类未被选中时，应该创建新分类并添加明细", () => {
        const mockInspections = [createMockInspection(1, "001", "detail1")];

        function toggleDetailSelection(
          itemNameCode: string,
          inspections: Inspection[],
          classifyItemCode: string
        ) {
          const checkedItem = mockStoreInspectionsForPrint.value.get(classifyItemCode);

          if (checkedItem === undefined) {
            const item = new Map();
            item.set(itemNameCode, inspections);
            mockStoreInspectionsForPrint.value.set(classifyItemCode, item);
          } else {
            const checkedDetail = checkedItem.get(itemNameCode);
            if (checkedDetail === undefined) {
              checkedItem.set(itemNameCode, inspections);
            } else {
              checkedItem.delete(itemNameCode);
            }
          }

          mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
        }

        toggleDetailSelection("detail1", mockInspections, "001");

        const selectedCategory = mockStoreInspectionsForPrint.value.get("001");
        expect(selectedCategory?.get("detail1")).toEqual(mockInspections);
        expect(mockSetInspectionsForPrint).toHaveBeenCalled();
      });

      it("当明细未被选中时，应该添加到已存在的分类中", () => {
        const existingMap = new Map();
        mockStoreInspectionsForPrint.value.set("001", existingMap);

        const mockInspections = [createMockInspection(1, "001", "detail1")];

        function toggleDetailSelection(
          itemNameCode: string,
          inspections: Inspection[],
          classifyItemCode: string
        ) {
          const checkedItem = mockStoreInspectionsForPrint.value.get(classifyItemCode);

          if (checkedItem === undefined) {
            const item = new Map();
            item.set(itemNameCode, inspections);
            mockStoreInspectionsForPrint.value.set(classifyItemCode, item);
          } else {
            const checkedDetail = checkedItem.get(itemNameCode);
            if (checkedDetail === undefined) {
              checkedItem.set(itemNameCode, inspections);
            } else {
              checkedItem.delete(itemNameCode);
            }
          }

          mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
        }

        toggleDetailSelection("detail1", mockInspections, "001");

        const selectedCategory = mockStoreInspectionsForPrint.value.get("001");
        expect(selectedCategory?.get("detail1")).toEqual(mockInspections);
        expect(mockSetInspectionsForPrint).toHaveBeenCalled();
      });

      it("当明细已被选中时，应该从分类中移除", () => {
        const existingMap = new Map();
        const mockInspections = [createMockInspection(1, "001", "detail1")];
        existingMap.set("detail1", mockInspections);
        mockStoreInspectionsForPrint.value.set("001", existingMap);

        function toggleDetailSelection(
          itemNameCode: string,
          inspections: Inspection[],
          classifyItemCode: string
        ) {
          const checkedItem = mockStoreInspectionsForPrint.value.get(classifyItemCode);

          if (checkedItem === undefined) {
            const item = new Map();
            item.set(itemNameCode, inspections);
            mockStoreInspectionsForPrint.value.set(classifyItemCode, item);
          } else {
            const checkedDetail = checkedItem.get(itemNameCode);
            if (checkedDetail === undefined) {
              checkedItem.set(itemNameCode, inspections);
            } else {
              checkedItem.delete(itemNameCode);
            }
          }

          mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
        }

        toggleDetailSelection("detail1", mockInspections, "001");

        const selectedCategory = mockStoreInspectionsForPrint.value.get("001");
        expect(selectedCategory?.get("detail1")).toBeUndefined();
        expect(mockSetInspectionsForPrint).toHaveBeenCalled();
      });
    });
  });

  describe("分类项目半选状态", () => {
    it("当分类下部分明细被选中时，应该显示半选状态", () => {
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);

      function isClassifyIndeterminate(itemCode: string): boolean {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (!checkedItem) return false;

        const currentClassify = mockClassify.value.find(item => item.itemCode === itemCode);
        if (!currentClassify) return false;

        const totalDetails = currentClassify.data.size;
        const checkedDetails = checkedItem.size;

        return checkedDetails > 0 && checkedDetails < totalDetails;
      }

      // 设置分类数据
      mockClassify.value[0].data = classifyData;
      // 选中部分明细
      const partialChecked = new Map([["test1", [createMockInspection(1)]]]);
      mockStoreInspectionsForPrint.value.set("001", partialChecked);

      expect(isClassifyIndeterminate("001")).toBe(true);
    });

    it("当分类下所有明细被选中时，不应该显示半选状态", () => {
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);

      function isClassifyIndeterminate(itemCode: string): boolean {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (!checkedItem) return false;

        const currentClassify = mockClassify.value.find(item => item.itemCode === itemCode);
        if (!currentClassify) return false;

        const totalDetails = currentClassify.data.size;
        const checkedDetails = checkedItem.size;

        return checkedDetails > 0 && checkedDetails < totalDetails;
      }

      // 设置分类数据
      mockClassify.value[0].data = classifyData;
      // 选中所有明细
      const allChecked = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockStoreInspectionsForPrint.value.set("001", allChecked);

      expect(isClassifyIndeterminate("001")).toBe(false);
    });

    it("当分类下没有明细被选中时，不应该显示半选状态", () => {
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);

      function isClassifyIndeterminate(itemCode: string): boolean {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (!checkedItem) return false;

        const currentClassify = mockClassify.value.find(item => item.itemCode === itemCode);
        if (!currentClassify) return false;

        const totalDetails = currentClassify.data.size;
        const checkedDetails = checkedItem.size;

        return checkedDetails > 0 && checkedDetails < totalDetails;
      }

      // 设置分类数据
      mockClassify.value[0].data = classifyData;
      // 不选中任何明细
      mockStoreInspectionsForPrint.value.delete("001");

      expect(isClassifyIndeterminate("001")).toBe(false);
    });
  });

  describe("分类与明细联动", () => {
    it("当取消某个明细时，分类应该变为半选状态", () => {
      // 设置初始数据
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockClassify.value[0].data = classifyData;

      // 初始全选状态
      const allChecked = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockStoreInspectionsForPrint.value.set("001", allChecked);

      function toggleDetailSelection(
        itemNameCode: string,
        inspections: Inspection[],
        classifyItemCode: string
      ) {
        const checkedItem = mockStoreInspectionsForPrint.value.get(classifyItemCode);

        if (checkedItem === undefined) {
          const item = new Map();
          item.set(itemNameCode, inspections);
          mockStoreInspectionsForPrint.value.set(classifyItemCode, item);
        } else {
          const checkedDetail = checkedItem.get(itemNameCode);
          if (checkedDetail === undefined) {
            checkedItem.set(itemNameCode, inspections);
          } else {
            checkedItem.delete(itemNameCode);
            // 如果分类下没有选中的明细了，删除整个分类
            if (checkedItem.size === 0) {
              mockStoreInspectionsForPrint.value.delete(classifyItemCode);
            }
          }
        }

        mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
      }

      // 取消一个明细的选中
      toggleDetailSelection("test1", [createMockInspection(1)], "001");

      // 验证分类变为半选状态
      function isClassifyIndeterminate(itemCode: string): boolean {
        const checkedItem = mockStoreInspectionsForPrint.value.get(itemCode);
        if (!checkedItem) return false;

        const currentClassify = mockClassify.value.find(item => item.itemCode === itemCode);
        if (!currentClassify) return false;

        const totalDetails = currentClassify.data.size;
        const checkedDetails = checkedItem.size;

        return checkedDetails > 0 && checkedDetails < totalDetails;
      }

      expect(isClassifyIndeterminate("001")).toBe(true);
    });

    it("当取消所有明细时，分类应该变为未选中状态", () => {
      // 设置初始数据
      const classifyData = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockClassify.value[0].data = classifyData;

      // 初始全选状态
      const allChecked = new Map([
        ["test1", [createMockInspection(1)]],
        ["test2", [createMockInspection(2)]]
      ]);
      mockStoreInspectionsForPrint.value.set("001", allChecked);

      function toggleDetailSelection(
        itemNameCode: string,
        inspections: Inspection[],
        classifyItemCode: string
      ) {
        const checkedItem = mockStoreInspectionsForPrint.value.get(classifyItemCode);

        if (checkedItem === undefined) {
          const item = new Map();
          item.set(itemNameCode, inspections);
          mockStoreInspectionsForPrint.value.set(classifyItemCode, item);
        } else {
          const checkedDetail = checkedItem.get(itemNameCode);
          if (checkedDetail === undefined) {
            checkedItem.set(itemNameCode, inspections);
          } else {
            checkedItem.delete(itemNameCode);
            // 如果分类下没有选中的明细了，删除整个分类
            if (checkedItem.size === 0) {
              mockStoreInspectionsForPrint.value.delete(classifyItemCode);
            }
          }
        }

        mockSetInspectionsForPrint(mockStoreInspectionsForPrint.value);
      }

      // 取消所有明细的选中
      toggleDetailSelection("test1", [createMockInspection(1)], "001");
      toggleDetailSelection("test2", [createMockInspection(2)], "001");

      // 验证分类变为未选中状态
      expect(mockStoreInspectionsForPrint.value.get("001")).toBeUndefined();
    });
  });
});
