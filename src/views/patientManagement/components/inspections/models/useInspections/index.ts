import { getInspectionsApi } from "@/apis/InspectionCheck";
import { Inspection } from "@/types/inspection";
import { useInspectionsDetails } from "../useInspectionsDetails";
import { useClassify } from "../useClassify";
import { useFilterInspectionDetails } from "../useFilterInspectionDetails";
import { deepClone, groupedToMap } from "@/utils/collection";

export const useInspections = () => {
  const { filters, resetFilters } = useFilterInspectionDetails();
  const { inspectionsDetails, initInspectionsDetails } = useInspectionsDetails();

  const { classify, initClassify, classifyFilterByTopX } = useClassify();

  const raw = shallowRef<Inspection[]>([]);

  const filteredRaw = computed<Inspection[]>(() => {
    if (!filters.value.itemName) {
      return raw.value;
    }
    const grouped = groupedToMap(raw.value, "itemCode");
    const res: Inspection[] = [];
    grouped.forEach(v => {
      res.push(...v.filter(item => item.itemName.includes(filters.value.itemName)));
    });
    return res;
  });

  function initInspections() {
    console.log(filteredRaw.value);
    initClassify(filteredRaw.value);
  }

  function viewInspectionsDetails(itemCode: Inspection["itemCode"]) {
    const res = classify.value.find(c => c.itemCode == itemCode);
    if (res != undefined) {
      initInspectionsDetails(deepClone(res.data));
    }
  }

  function filterByItemName() {
    if (!filters.value.itemName) {
      initClassify(filteredRaw.value);
    } else {
      const filteredData = filteredRaw.value.filter(item =>
        item.itemName.includes(filters.value.itemName)
      );
      initClassify(filteredData);
    }

    if (classify.value.length > 0) {
      viewInspectionsDetails(classify.value[0].itemCode);
    } else {
      initInspectionsDetails(new Map());
    }
  }

  function filterByTopX(top: 0 | 1 | 2 | 3 | 4 | 5) {
    if (top == 0) {
      initClassify(filteredRaw.value);
    } else {
      classifyFilterByTopX(filteredRaw.value, top);
    }
  }

  function resetInspections() {
    resetFilters();
    initClassify([]);
    initInspectionsDetails(new Map());
  }

  async function loadInspections(
    patientId: number,
    { startTime, endTime }: { startTime: Date; endTime: Date }
  ) {
    raw.value = await getInspectionsApi(patientId, { startTime, endTime });
    initInspections();
  }

  return {
    loadInspections,
    resetInspections,

    classify,
    filterByTopX,
    filterByItemName,

    inspectionsDetails,
    viewInspectionsDetails,

    filters
  };
};
