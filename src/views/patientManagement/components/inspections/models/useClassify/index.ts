import { Inspection } from "@/types/inspection";
import { groupedToMap } from "@/utils/collection";

export interface ClassifyItem {
  itemCode: Inspection["itemCode"];
  itemName: Inspection["itemName"];
  sortNumber: number;
  viewVisitTypeName: string;
  data: Map<Inspection["itemNameCode"], Inspection[]>;
}

export type Classify = ClassifyItem[];

export const useClassify = (topX?: number) => {
  const classify = ref<Classify>([]);
  const _topX = ref(topX || 0);

  function initClassify(inspections: Inspection[]) {
    _topX.value = 0;
    if (inspections.length == 0) {
      classify.value = [];
      return;
    }
    const grouped = groupedToMap(inspections, "itemCode");
    classify.value = itemCodeGroupedToClassify(grouped);
  }

  function initClassifyItem(data: Inspection[], sortNumber: number) {
    classify.value.push(createClassifyItem(data, sortNumber));
  }

  function itemCodeGroupedToClassify(
    groupedByItemCode: Map<Inspection["itemNameCode"], Inspection[]>
  ): Classify {
    const result: Classify = [];
    let sortNumber = 0;
    groupedByItemCode.forEach(v => {
      result.push(createClassifyItem(v, sortNumber));
      sortNumber++;
    });
    return result;
  }

  function createClassifyItem(data: Inspection[], sortNumber: number): ClassifyItem {
    const { itemName, visitTypeName, itemCode } = data[0];
    const grouped = new Map();
    const g = groupedToMap(data, "itemNameCode");
    g.forEach((v, k) => {
      if (_topX.value == 0) {
        grouped.set(k, v);
      } else {
        grouped.set(k, v.slice(0, _topX.value));
      }
    });
    return {
      itemCode,
      itemName,
      viewVisitTypeName: getVisitTypeTag(visitTypeName),
      sortNumber,
      data: grouped
    };
  }
  function classifyFilterByTopX(inspections: Inspection[], top: 0 | 1 | 2 | 3 | 4 | 5) {
    classify.value = getFilterByTopX(inspections, top);
  }

  function getFilterByTopX(inspections: Inspection[], top: 0 | 1 | 2 | 3 | 4 | 5): Classify {
    _topX.value = top;
    const grouped = groupedToMap(inspections, "itemCode");

    const result: Classify = [];
    let sortNumber = 0;
    grouped.forEach(v => {
      result.push(createClassifyItem(v, sortNumber));
      sortNumber++;
    });
    return result;
  }

  return {
    classify,
    initClassifyItem,
    initClassify,
    classifyFilterByTopX
  };
};
function getVisitTypeTag(visitTypeName: Inspection["visitTypeName"]) {
  return `${visitTypeName == "门诊" ? "c" : "h"}`;
}
