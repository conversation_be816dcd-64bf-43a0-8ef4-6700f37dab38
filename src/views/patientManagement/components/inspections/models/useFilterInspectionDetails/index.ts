import { getTimeForSomeYear } from "@/utils";
import { endOfDay } from "date-fns";

interface InspectionsFiler {
  top: 0 | 1 | 2 | 3 | 4 | 5;
  itemName: string;
  startTime: Date;
  endTime: Date;
}

export const useFilterInspectionDetails = () => {
  const filters = ref<InspectionsFiler>({
    top: 0,
    itemName: "",
    startTime: getTimeForSomeYear(1),
    endTime: endOfDay(new Date())
  });

  function resetFilters() {
    const time = endOfDay(new Date());
    filters.value = {
      top: 0,
      itemName: "",
      startTime: getTimeForSomeYear(1),
      endTime: time
    };
  }

  function changeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
    filters.value.top = top;
  }

  return {
    filters,
    resetFilters,
    changeTop
  };
};
