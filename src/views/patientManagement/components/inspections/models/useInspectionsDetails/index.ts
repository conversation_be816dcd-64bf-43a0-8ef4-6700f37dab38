import { Inspection } from "@/types/inspection";

export type InspectionsDetails = Map<Inspection["itemNameCode"], Inspection[]>;

export const useInspectionsDetails = () => {
  const inspectionsDetails = ref<InspectionsDetails>(new Map());
  const _topX = ref(0);

  function initInspectionsDetails(ins: InspectionsDetails) {
    inspectionsDetails.value = ins;
  }

  function inspectionsDetailsFilterByTopX(
    classifyItem: InspectionsDetails,
    top: 0 | 1 | 2 | 3 | 4 | 5
  ) {
    _topX.value = top;
    if (classifyItem.size == 0) {
      inspectionsDetails.value = new Map();
      return;
    }
    if (_topX.value == 0) {
      inspectionsDetails.value = classifyItem;
    } else {
      const result = new Map();
      classifyItem.forEach((v, k) => {
        result.set(k, v.slice(0, _topX.value));
      });
      inspectionsDetails.value = result;
    }
  }

  return {
    inspectionsDetails,
    initInspectionsDetails,
    inspectionsDetailsFilterByTopX
  };
};
