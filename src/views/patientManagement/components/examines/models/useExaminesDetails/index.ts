import { Examine } from "@/types/examine";

export type ExaminesDetails = Map<Examine["itemCode"], Examine[]>;

export const useExaminesDetails = () => {
  const examinesDetails = ref<ExaminesDetails>(new Map());
  const _topX = ref(0);

  function initExaminesDetails(ins: ExaminesDetails) {
    examinesDetails.value = ins;
  }

  function examinesDetailsFilterByTopX(classifyItem: ExaminesDetails, top: 0 | 1 | 2 | 3 | 4 | 5) {
    _topX.value = top;
    if (classifyItem.size == 0) {
      examinesDetails.value = new Map();
      return;
    }
    if (_topX.value == 0) {
      examinesDetails.value = classifyItem;
    } else {
      const result = new Map();
      classifyItem.forEach((v, k) => {
        result.set(k, v.slice(0, _topX.value));
      });
      examinesDetails.value = result;
    }
  }

  return {
    examinesDetails,
    initExaminesDetails,
    examinesDetailsFilterByTopX
  };
};
