import { Examine } from "@/types/examine";
import { useExaminesFilters } from "../useExaminesFilters";
import { ExaminationDataProvider } from "@/apis/InspectionCheck";
import { groupedToMap } from "@/utils/collection";
import { useClassify } from "../useClassify";
import { useExaminesDetails } from "../useExaminesDetails";
// import { getExamines } from "@/apis/InspectionCheck";

const test = [
  {
    itemClass: "1",
    itemClassName: "1",
    sortNumber: 1,
    viewVisitTypeName: "1",
    isIndeterminate: false,
    children: new Map([
      [
        "73198",
        [
          {
            id: 1,
            examReportLid: "56394125862531068",
            examinationDate: "2022-07-22 09:39:08",
            execDepartmentName: "妇产超声科南院区",
            filePath: "/founder/cdr/doc/cda/202207/22/BS320/12790154.xml",
            functionRequestId: "245264902",
            imagUrl:
              "http://**************:8080/ContentsViewer/viewer.html?uname=ipacsex&pass=kmmg5678&pid=7214104&acceptno=202207220440",
            ipseqnoText: null,
            itemClass: "0101",
            itemClassName: "超声检查",
            itemCode: "73198",
            itemName: "经阴道妇科及外阴生殖道超声检查",
            orderDept: "2748",
            orderDeptName: "南院陈勍专家团队门诊",
            orderLid: "245264902",
            orderPerson: "00404",
            orderPersonName: "陈勍",
            orderTime: "2022-07-22 08:58:34",
            patientDomain: "HIS3.0",
            patientDomainId: "01",
            patientLid: "7214104",
            patientName: "李可奕",
            reportDate: "2022-07-22 09:44:19",
            reportDoctor: "02995",
            resultDescex:
              "子宫大小正常；\n右卵巢内团状高回声区：畸胎瘤与其它相鉴别，建议追踪复查；\n右卵巢内散在片状高回声区：考虑术后改变声像。",
            rportDoctorName: "黄兰婷",
            seedescex:
              "子宫轮廓清楚，前位，子宫体长径：36mm，宫颈长径：25mm，子宫前后径：23mm，\n横径：43mm。宫肌回声均匀，内膜厚3mm，回声欠均。\n右卵巢大小约29mm×23mm×21mm，内见1团状高回声区10mm×9mm×9mm，边界尚清，内另见散在片状高回声区，边界不清，范围约10mm×8mm×7mm，CDFI：内均未见明显血流信号；\n左卵巢缺如（术后）。\nCDFI：子宫与左附件区未见明显异常血流信号。\n外阴未见明显异常肿块。",
            sickbedNo: null,
            status: "检查报告已审核",
            visitTypeCode: "01",
            visitTypeName: "门诊"
          }
        ]
      ]
    ])
  }
];
//现根据itemClass分组然后按照itemCode分组
export const useExamines = () => {
  const raw = shallowRef<Examine[]>([]);
  const { filters, resetFilters, changeTop } = useExaminesFilters();
  const filteredRaw = computed(() => {
    if (!filters.value.className) {
      return raw.value;
    }
    const grouped = groupedToMap(raw.value, "itemClass");
    const res: Examine[] = [];
    grouped.forEach((v, k) => {
      const tmp = v.filter(item => item.itemClassName?.includes(filters.value.className));
      res.push(...tmp);
    });

    return res;
  });

  const { classify, initClassify, classifyFilterByTopX } = useClassify();
  const { examinesDetails, initExaminesDetails } = useExaminesDetails();

  async function loadExamines(
    patientId: number,
    { startTime, endTime }: { startTime: Date; endTime: Date }
  ) {
    raw.value = await ExaminationDataProvider.getExaminations(patientId, {
      startTime,
      endTime
    });
    console.log(filteredRaw.value);
    initExamines();
  }

  function filterByClassName() {
    if (!filters.value.className) {
      initClassify(filteredRaw.value);
    } else {
      const filteredData = filteredRaw.value.filter(item =>
        item.itemClassName?.includes(filters.value.className)
      );
      initClassify(filteredData);
    }

    if (classify.value.length > 0) {
      viewExaminesDetails(classify.value[0].itemClass);
    } else {
      initExaminesDetails(new Map());
    }
  }

  function filterByTopX(_top: 0 | 1 | 2 | 3 | 4 | 5) {
    if (_top == 0) {
      initClassify(filteredRaw.value);
    } else {
      classifyFilterByTopX(filteredRaw.value, _top);
    }
  }

  function initExamines() {
    initClassify(filteredRaw.value);
  }

  function viewExaminesDetails(itemClass: Examine["itemClass"]) {
    const res = classify.value.find(c => c.itemClass == itemClass);
    if (res != null) {
      initExaminesDetails(res.data);
    }
  }

  function resetExamines() {
    resetFilters();
    initClassify([]);
    initExaminesDetails(new Map());
  }

  return {
    loadExamines,
    resetExamines,

    classify,
    filterByTopX,
    filterByClassName,

    examinesDetails,
    viewExaminesDetails,

    filters
  };
};
