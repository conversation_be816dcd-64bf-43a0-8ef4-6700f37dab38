import { Examine } from "@/types/examine";
import { groupedToMap } from "@/utils/collection";

export interface ClassifyItem {
  itemClass: Examine["itemClass"];
  itemClassName: Examine["itemClassName"];
  sortNumber: number;
  viewVisitTypeName: string;
  data: Map<Examine["itemCode"], Examine[]>;
}

export type Classify = ClassifyItem[];

export const useClassify = () => {
  const classify = ref<Classify>([]);
  const _topX = ref(0);

  function initClassify(examines: Examine[]) {
    _topX.value = 0;
    if (examines.length == 0) {
      classify.value = [];
      return;
    }
    const grouped = groupedToMap(examines, "itemClass");
    classify.value = groupedByItemCodeToClassifyItem(grouped);
  }
  function initClassifyItem(data: Examine[], sortNumber: number) {
    classify.value.push(createClassifyItem(data, sortNumber));
  }
  function createClassifyItem(data: Examine[], sortNumber: number): ClassifyItem {
    const { itemClass, itemClassName, visitTypeName } = data[0];
    const grouped = new Map();
    const g = groupedToMap(data, "itemCode");
    g.forEach((v, k) => {
      if (_topX.value == 0) {
        grouped.set(k, v);
      } else {
        grouped.set(k, v.slice(0, _topX.value));
      }
    });
    return {
      itemClass,
      itemClassName,
      sortNumber,
      viewVisitTypeName: getVisitTypeTag(visitTypeName),
      data: grouped
    };
  }
  function groupedByItemCodeToClassifyItem(
    groupedByItemCode: Map<Examine["itemClass"], Examine[]>
  ): Classify {
    const result: Classify = [];
    let sortNumber = 0;
    groupedByItemCode.forEach(v => {
      result.push(createClassifyItem(v, sortNumber));
      sortNumber++;
    });
    return result;
  }

  function classifyFilterByTopX(examines: Examine[], top: 0 | 1 | 2 | 3 | 4 | 5) {
    classify.value = getFilterByTopX(examines, top);
  }
  function getFilterByTopX(examines: Examine[], top: 0 | 1 | 2 | 3 | 4 | 5): Classify {
    _topX.value = top;
    const grouped = groupedToMap(examines, "itemClass");
    const result: Classify = [];
    let sortNumber = 0;
    grouped.forEach(v => {
      result.push(createClassifyItem(v, sortNumber));
      sortNumber++;
    });
    return result;
  }

  return {
    classify,
    initClassifyItem,
    initClassify,
    classifyFilterByTopX
  };
};

function getVisitTypeTag(visitTypeName: Examine["visitTypeName"]) {
  return `${visitTypeName == "门诊" ? "c" : "h"}`;
}
