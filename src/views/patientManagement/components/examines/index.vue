<script lang="ts" setup>
import { useStorePatient } from "@/store/patient";
import { useTopXTimesOptions } from "../../models/timesOptions";
import { useExamines } from "./models/useExamines";
import { deepClone } from "@/utils/collection";
import { Examine } from "@/types/examine";
import EmptyComponent from "@/components/empty/index.vue";
import ExaminesPrintSortComponent from "../examinesPrintSort/index.vue";
import { ClassifyItem } from "./models/useClassify";
import { useStoreExaminesForPrint } from "@/store/storeExaminesForPrint";
import { useDialog } from "@/composables/useDialog";
import { useStoreExaminesSort } from "@/store/storeExaminesSort";
import { useDebounceFn } from "@vueuse/core";

defineOptions({
  name: "Examines"
});

const examineDetailTableComponent = defineAsyncComponent(
  () => import("../examineDetails/components/examineDetailTable/index.vue")
);
const { storePatientId } = useStorePatient();
const pageLoading = ref(false);

const {
  loadExamines,
  resetExamines,
  classify,
  filters,
  examinesDetails,

  viewExaminesDetails,
  filterByTopX,
  filterByClassName
} = useExamines();
const { topXTimesOptions } = useTopXTimesOptions();
const classifyRef = useTemplateRef("classifyRef");
const classifyItemsRef = useTemplateRef("classifyItemsRef");

const currentClassifyItem = ref<ClassifyItem | null>(null);

function activeClassify(classifyItem: ClassifyItem) {
  currentClassifyItem.value = deepClone(classifyItem);
  viewExaminesDetails(classifyItem.itemClass);
}

const { setExaminesForPrint, storeExaminesForPrint, clearExaminesForPrint } =
  useStoreExaminesForPrint();

function onSelectClassifyItem(itemClass: Examine["itemClass"], index: number) {
  if (filters.value.top == 0) {
    filters.value.top = 5;
    onChangeTop(5);
  }
  if (currentClassifyItem.value?.itemClass != itemClass) {
    onActiveClassifyItem(classify.value[index]);
  }

  if (currentClassifyItem.value == null) {
    return;
  }

  const checkedItem = storeExaminesForPrint.value.get(itemClass);
  if (checkedItem == undefined) {
    storeExaminesForPrint.value.set(itemClass, deepClone(classify.value[index].data));
  } else {
    storeExaminesForPrint.value.delete(itemClass);
  }
  setExaminesForPrint(storeExaminesForPrint.value);
}

function onSelectDetail(k: Examine["itemCode"], v: Examine[]) {
  let result: Examine[] = v;
  if (filters.value.top == 0) {
    filters.value.top = 5;
    result = v.slice(0, filters.value.top);
    onChangeTop(5);
  }
  const itemClass = v[0].itemClass;
  if (currentClassifyItem.value?.itemClass != itemClass) {
    const index = classify.value.findIndex(i => i.itemClass == itemClass);
    onActiveClassifyItem(classify.value[index]);
  }
  if (currentClassifyItem.value == null) {
    return;
  }
  const checkedItem = storeExaminesForPrint.value.get(currentClassifyItem.value.itemClass);
  const item = new Map<Examine["itemCode"], Examine[]>();
  if (checkedItem == undefined) {
    item.set(k, result);
    storeExaminesForPrint.value.set(currentClassifyItem.value.itemClass, item);
  } else {
    const checkedItemItem = checkedItem.get(k);

    if (checkedItemItem == undefined) {
      checkedItem.set(k, result);
    } else {
      checkedItem.delete(k);
    }
  }
  setExaminesForPrint(storeExaminesForPrint.value);
}

async function onChangeTop(top: 0 | 1 | 2 | 3 | 4 | 5) {
  if (top == 0 && storeExaminesForPrint.value.size > 0) {
    try {
      const action = await ElMessageBox.confirm("已勾选打印项不可查看全部", "提示", {
        confirmButtonText: "继续查看",
        cancelButtonText: "保留勾选",
        type: "warning"
      });

      if (action === "confirm") {
        storeExaminesForPrint.value = new Map();
        filters.value.top = top;
        updateDisplay(top);
      } else {
        filters.value.top = top;
        return;
      }
    } catch (error) {
      console.error(error);
    } finally {
      pageLoading.value = false;
    }
  }
  filters.value.top = top;
  updateDisplay(top);
}
function updateDisplay(top: 0 | 1 | 2 | 3 | 4 | 5) {
  pageLoading.value = true;
  filterByTopX(top);
  if (classify.value.length > 0) {
    onActiveClassifyItem(classify.value[0]);
  }

  pageLoading.value = false;
}
function onFiltersClassNameClear() {
  filters.value.className = "";
  classifyItemsRef.value?.scrollTo({ top: 0, behavior: "smooth" });
  classifyRef.value?.scrollTo({ top: 0, behavior: "smooth" });
  onSearch();
}

async function onFiltersDateChange() {
  const { startTime, endTime } = filters.value;
  pageLoading.value = true;
  await loadExamines(storePatientId.value, {
    startTime,
    endTime
  });
  pageLoading.value = false;
}
const onSearch = useDebounceFn(() => {
  filterByClassName();
  filterByTopX(filters.value.top);
  onActiveClassifyItem(classify.value[0]);
}, 300); // 300ms 的防抖延迟

function onActiveClassifyItem(classifyItem: ClassifyItem) {
  classifyItemsRef.value?.scrollTo({ top: 0, behavior: "smooth" });
  currentClassifyItem.value = classifyItem;
  viewExaminesDetails(classifyItem.itemClass);
}

function examineIsChecked(itemCode: Examine["itemCode"]): boolean {
  if (currentClassifyItem.value == null) return false;
  const { itemClass } = currentClassifyItem.value;
  const checkedItem = storeExaminesForPrint.value.get(itemClass);
  if (checkedItem == undefined) {
    return false;
  }
  return checkedItem.has(itemCode);
}

function classifyItemIsChecked(itemClass: Examine["itemClass"]): boolean {
  const item = storeExaminesForPrint.value.get(itemClass);
  const classifyItem = classify.value.find(item => item.itemClass === itemClass);

  return classifyItem?.data.size == item?.size;
}

function getIndeterminateState(itemClass: Examine["itemClass"]): boolean {
  const checkedItem = storeExaminesForPrint.value.get(itemClass);
  const targetClassifyItem = classify.value.find(item => item.itemClass === itemClass);

  if (checkedItem != undefined && targetClassifyItem) {
    return checkedItem.size > 0 && checkedItem.size < targetClassifyItem.data.size;
  }
  return false;
}
const { dialog, openDialog, closeDialog } = useDialog();
const { loadData, save } = useStoreExaminesSort();

async function onSaveSort() {
  await save();
  await loadData();
  setExaminesForPrint(storeExaminesForPrint.value);
  ElMessage.success("保存成功");
  closeDialog();
}

function onOpenSortDialog() {
  if (storeExaminesForPrint.value.size == 0) {
    ElMessage.warning("请先选择要打印的检查项目");
    return;
  }
  openDialog();
}
async function onReset() {
  pageLoading.value = true;
  resetExamines();
  clearExaminesForPrint();
  await loadExamines(storePatientId.value, {
    startTime: filters.value.startTime,
    endTime: filters.value.endTime
  });
  onActiveClassifyItem(classify.value[0]);

  pageLoading.value = false;
}
onMounted(async () => {
  await loadExamines(storePatientId.value, {
    startTime: filters.value.startTime,
    endTime: filters.value.endTime
  });
  if (classify.value.length > 0) {
    activeClassify(classify.value[0]);
  }
});
</script>

<template>
  <div class="examines-page">
    <header class="examines-page__header">
      <ul class="header__filters">
        <li class="filters__condition">
          <el-date-picker
            v-model="filters.startTime"
            type="date"
            placeholder="检查报告开始日期"
            style="width: 126px"
            @change="onFiltersDateChange"
            :clearable="false"
            :editable="false"
          />
        </li>
        <li class="filters__condition">
          <div style="width: 1rem">-</div>
          <el-date-picker
            v-model="filters.endTime"
            type="date"
            placeholder="检查报告结束日期"
            style="width: 126px"
            @change="onFiltersDateChange"
            :clearable="false"
            :editable="false"
          />
        </li>
        <li class="filters__condition">
          <div class="filter__value">
            <el-select
              :model-value="filters.top"
              @change="onChangeTop"
              placeholder="全部结果"
              style="width: 140px"
            >
              <el-option
                v-for="option in topXTimesOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </li>
        <li class="filters__condition">
          <div class="filter__value">
            <el-input
              style="width: 240px"
              v-model="filters.className"
              placeholder="搜索检查分类"
              clearable
              @clear="onFiltersClassNameClear"
              @keyup.enter="onSearch"
            ></el-input>
          </div>
        </li>
      </ul>
      <div class="operation">
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button type="primary" text bg @click="onReset">重置</el-button>
        <el-divider direction="vertical" />
        <el-button @click="onOpenSortDialog">打印排序</el-button>
      </div>
    </header>
    <!-- classify -->
    <div class="examines-page__classify" ref="classifyRef">
      <EmptyComponent
        v-if="classify.length == 0"
        description="暂无检查分类 请尝试修改查询条件"
      ></EmptyComponent>
      <template v-if="classify.length > 0">
        <div
          class="classifyItem"
          :class="{
            'classifyItem-active': currentClassifyItem?.itemClass === classifyItem.itemClass
          }"
          v-for="(classifyItem, index) in classify"
          @click="onActiveClassifyItem(classifyItem)"
        >
          <div class="classify-item__title">
            <el-checkbox
              :key="classifyItem.itemClass"
              :model-value="classifyItemIsChecked(classifyItem.itemClass)"
              @change="onSelectClassifyItem(classifyItem.itemClass, index)"
              :indeterminate="getIndeterminateState(classifyItem.itemClass)"
            />
            {{ classifyItem.itemClassName }}
          </div>
        </div>
      </template>
    </div>
    <!-- details -->
    <div class="examines-page__details" ref="classifyItemsRef">
      <EmptyComponent
        v-if="currentClassifyItem == null || examinesDetails.size == 0"
        description="暂无检查明细 请尝试修改查询条件"
      ></EmptyComponent>
      <template v-else>
        <div class="examines-detail" v-for="examine in examinesDetails">
          <div class="base-info">
            <div class="base-info-header">
              <el-checkbox
                :key="examine[0]"
                :model-value="examineIsChecked(examine[0])"
                @change="onSelectDetail(examine[0], examine[1])"
              />
              <div class="title">
                {{ examine[1][0].itemName }}
              </div>
            </div>
          </div>
          <LazyLoadComponent
            style="height: 100%"
            :asyncComponent="examineDetailTableComponent"
            :data="examine[1]"
          />
        </div>
      </template>
    </div>
  </div>
  <el-dialog v-model="dialog.visible" title="排序" width="50%">
    <ExaminesPrintSortComponent />
    <template #footer>
      <el-button @click="onSaveSort">保存</el-button>
    </template>
  </el-dialog>
</template>
<style lang="less" scoped>
.examines-page {
  height: 100%;
  display: grid;
  grid-template-columns: 260px 1fr;
  grid-template-rows: 48px 1fr;
  grid-template-areas:
    "header header"
    "classify details";
  gap: 10px;
  box-sizing: border-box;
  .examines-page__header {
    grid-area: header;
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 0 10px;
    border-radius: 4px;
    .header__filters {
      display: flex;
      align-items: center;
      gap: 10px;
      .filters__condition {
        display: flex;
        align-items: center;
      }
    }
  }

  .examines-page__classify {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: auto;
    border-radius: 4px;
    grid-area: classify;
    gap: 10px 0;
    box-sizing: border-box;
    padding: 10px;
    background-color: hsl(0, 0%, 98%);
    :deep(.el-checkbox) {
      width: 20px;
      height: 20px;
      transform: translateY(3px);
      .el-checkbox__inner {
        display: flex;
        width: 20px;
        height: 20px;
        &::after {
          height: 9px;
          transform: rotate(45deg) scaleY(1) translateX(3px);
        }
        &::before {
          transform: translateY(3px) scale(0.5);
        }
      }
    }
    .classifyItem {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 14px;
      background-color: #fff;
      color: #272727;
      border-radius: 4px;
      outline: 1px solid #e4e7ed;
      &:hover {
        cursor: pointer;
        background-color: var(--el-color-primary-light-5);
      }
    }
    .classifyItem-active {
      background-color: var(--el-color-primary-light-7);
    }
  }
  .examines-page__details {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: auto;
    grid-area: details;
    background-color: hsl(0, 0%, 98%);
    border-radius: 4px;
    box-sizing: border-box;
    padding: 10px;
    gap: 10px;
    .examines-detail {
      height: fit-content;
      width: fit-content;
      margin: 0;
      display: grid;
      grid-template-rows: 32px auto;
      gap: 10px;
      outline: 1px solid #e4e7ed;
      padding: 10px;
      border-radius: 8px;
      background-color: #fff;
      // min-width: 100%;

      box-sizing: border-box;

      .base-info {
        display: flex;
        align-items: center;
        gap: 10px;

        .base-info-header {
          display: flex;
          align-items: center;
          gap: 10px;
          flex-shrink: 0;
          font-size: 1.2rem;

          :deep(.el-checkbox) {
            width: 20px;
            height: 20px;

            .el-checkbox__inner {
              width: 20px;
              height: 20px;
              &::after {
                height: 9px;
                transform: rotate(45deg) scaleY(1) translateX(3px);
              }
            }
          }
          .title {
            padding: 2px;
            border-radius: 4px;
            background-color: var(--el-color-primary-light-9);
          }
        }
      }
    }
  }
}
</style>
