<script lang="ts"></script>

<script lang="ts" setup>
import { requestEditPatientMedicalRecords } from "@/apis/patients";

import { useStorePatient } from "@/store/patient";

const patientId = computed<number>(() => storePatientId.value);

const { storePatient, storePatientId } = useStorePatient();

const medicalRecords = computed(() => storePatient.value.case);

const onSaveMedicalRecordsBtnClick = async () => {
  try {
    await requestEditPatientMedicalRecords(patientId.value, medicalRecords.value);
    ElMessage({
      message: "保存成功！",
      type: "success"
    });
  } catch (error) {
    ElMessage.error("保存失败!");
  }
};
</script>

<template>
  <div class="medical-record-wrap">
    <div class="inputs-wrap">
      <div class="item">
        <span>家族史</span>
        <el-input
          v-model="medicalRecords.family"
          placeholder="请输入家族史"
          clearable
          :rows="4"
          type="textarea"
        />
      </div>
      <div class="item">
        <span>过敏史</span>
        <el-input
          v-model="medicalRecords.irritability"
          placeholder="请输入过敏史"
          clearable
          :rows="5"
          type="textarea"
        />
      </div>
      <div class="item">
        <span>药物史</span>
        <el-input
          v-model="medicalRecords.medicine"
          placeholder="请输入药物史"
          clearable
          :rows="5"
          type="textarea"
        />
      </div>
      <div class="item">
        <span>现病史</span>
        <el-input
          v-model="medicalRecords.now"
          placeholder="请输入现病史"
          clearable
          :rows="5"
          type="textarea"
        />
      </div>
      <div class="item">
        <span>既往史</span>
        <el-input
          v-model="medicalRecords.past"
          placeholder="请输入既往史"
          clearable
          :rows="5"
          type="textarea"
        />
      </div>
    </div>
    <el-button class="btn" type="primary" size="default" @click="onSaveMedicalRecordsBtnClick"
      >保存</el-button
    >
  </div>
</template>

<style lang="less" scoped>
.medical-record-wrap {
  height: 100%;
  display: flex;
  gap: 5rem;
  box-sizing: border-box;
  .inputs-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    .item {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      font-weight: bold;
    }
  }
}
</style>
