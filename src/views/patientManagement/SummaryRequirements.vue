<script lang="ts">
interface Filter {
  patientName: string;
  date: [DateModelType, DateModelType];
  patientPhone: string;
}
</script>

<script lang="ts" setup>
import {
  requestGetDemands,
  CreateDemand,
  ResponseCreateDemand,
  requestCreateDemand
} from "@/apis/demands";
import Page from "@/components/Page.vue";
import moment from "moment";
import { DateModelType } from "element-plus";

const router = useRouter();

const defaultTime = ref<[Date, Date]>([
  new Date(2000, 1, 1, 0, 0, 0),
  new Date(2000, 2, 1, 23, 59, 59)
]);

const summaryRequirements = ref<ResponseCreateDemand[]>([]);
const filter = reactive<Filter>({
  patientName: "",
  date: ["", ""],
  patientPhone: ""
});

const createDemandForm = ref<CreateDemand>({
  patientId: 123,
  content:
    "测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求测试需求"
});

const summaryRequirementsToFilter = computed(() => {
  return summaryRequirements.value.filter(ele => {
    const patientName = filter.patientName ? ele.patientName.includes(filter.patientName) : true;
    const patientPhone = filter.patientPhone
      ? ele.patientPhone.includes(filter.patientPhone)
      : true;
    const dateItem =
      filter.date && filter.date[0]
        ? moment(ele.createTime).isBetween(
            moment(filter.date[0]),
            moment(filter.date[1]),
            null,
            "[]"
          )
        : true;
    return patientName && dateItem && patientPhone;
  });
});

const goPatientSummaryRequirement = () => {
  router.push({
    name: "PatientSummaryRequirement",
    query: {
      patientPhone: 15924335685,
      patientName: "王先生"
    }
  });
};
const onResetFilterBtnClick = () => {
  (filter.date = ["", ""]), (filter.patientName = "");
  filter.patientPhone = "";
};

const createDemand = async () => {
  requestCreateDemand(createDemandForm.value);
};

// const currentChange = (a: any, oldCurrentRow) => {
//   console.log(a);
//   console.log(oldCurrentRow);
// };
const onCheckPatientRecord = (row: ResponseCreateDemand) => {
  router.push({
    name: "PatientSummaryRequirement",
    query: {
      patientPhone: row.patientPhone,
      patientName: row.patientName,
      patientId: row.patientId
    }
  });
};
onMounted(async () => {
  const { data } = await requestGetDemands();
  summaryRequirements.value = data;
});
</script>

<template>
  <page child-route-name="PatientSummaryRequirement">
    <div class="summary-requirements-wrap">
      <div class="summary-requirements__header">
        <el-date-picker
          v-model="filter.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          unlink-panels
          :default-time="defaultTime"
          style="min-width: 380px"
        />
        <el-input v-model="filter.patientName" placeholder="请输入病人姓名" clearable />
        <el-input v-model="filter.patientPhone" placeholder="请输入病人手机号" clearable />
        <div class="button-group">
          <el-button @click="onResetFilterBtnClick">重置</el-button>
        </div>
      </div>
      <el-table style="flex: 1" :data="summaryRequirementsToFilter" border stripe>
        <el-table-column prop="patientName" label="病人姓名" />
        <el-table-column prop="patientPhone" label="病人手机号" />
        <el-table-column prop="createTime" label="创建日期" />
        <el-table-column label="内容" width="180">
          <template #default="scope">
            <el-popover effect="light" trigger="hover" placement="top" :width="400">
              <template #default>
                <div>
                  <div style="font-weight: bold; font-size: 0.2rem">内容:</div>
                  {{ scope.row.content }}
                </div>
              </template>
              <template #reference>
                {{
                  scope.row.content.length > 10
                    ? scope.row.content.substr(0, 10) + "...."
                    : scope.row.content
                }}
              </template>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button type="primary" text bg size="small" @click="onCheckPatientRecord(row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </page>
</template>

<style lang="less" scoped>
.summary-requirements-wrap {
  width: 100%;
  height: 100%;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  box-sizing: border-box;
  .summary-requirements__header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 1rem;

    .button-group {
      display: flex;
    }
  }
}
</style>
