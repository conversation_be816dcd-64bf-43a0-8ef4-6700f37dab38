<script lang="ts" setup>
import * as icons from "@element-plus/icons-vue";
import type { ElForm } from "element-plus";
import {
  requetGetMenus,
  requetCreatedMenu,
  requetUpdateMenu,
  requestDeleteMenu
} from "@/apis/menus";
interface Menu {
  id: number;
  name: string;
  path?: string;
  routeName: string;
  sort: number;
  type: number;
  typeStr: string;
  url?: string;
  children: Menu[];
  icon: string;
}

interface MenuItem {
  id?: number;
  icon: string;
  name: string;
  parentId?: number;
  path?: string;
  routeName: string;
  sort: number;
  type: number;
  url?: string;
}

//表单验证规则
const rules = {
  name: [
    {
      required: true,
      message: "请输入名称",
      trigger: "blur"
    }
  ],
  routeName: [
    {
      required: true,
      message: "请输入路由名称",
      trigger: "blur"
    }
  ],
  icon: [
    {
      required: true,
      message: "请选择图标",
      trigger: "blur"
    }
  ],
  path: [
    {
      required: true,
      message: "请输入文件路径",
      trigger: "blur"
    }
  ],
  url: [
    {
      required: true,
      message: "请输入url",
      trigger: "blur"
    }
  ]
};
const menuTypeRadioConfig = [
  { label: "目录", value: 1, disableMenuTypes: [2, 4] },
  { label: "菜单", value: 2, disableMenuTypes: [2, 4] },
  { label: "模块", value: 4, disableMenuTypes: [0, 1] },
  { label: "按钮", value: 3, disableMenuTypes: [1] }
];
const menus = ref<Menu[]>([]);
const menuForm = ref<MenuItem>({
  icon: "",
  name: "",
  parentId: 0,
  path: "",
  routeName: "",
  sort: 1,
  type: 1,
  url: ""
});
const ruleFormRef = ref<InstanceType<typeof ElForm>>();
const parentMenu = ref<{ id: number; type: number; children: Menu[] }>({
  id: 0,
  type: 1,
  children: []
});
const dialogConfig = ref({
  isVisible: false,
  type: 0, //0是添加 1是编辑 2是添加子级
  title: "添加"
});
let menuToUpdate = ref<Menu>();
//递归删除指定项
const deleteMenu = (data: Array<any>, id: number) => {
  let result = data.filter(x => x.id !== id);
  result.forEach(x => x.children && (x.children = deleteMenu(x.children, id)));
  return result;
};
const vaildForm = () => ruleFormRef.value?.validate();

const getButtonName = (type: number) => {
  switch (type) {
    case 1:
      return "子级";
    case 2:
      return "模块";
    case 4:
      return "按钮";
  }
  return "";
};
const getTagTypeByRowType = (type: number) => {
  switch (type) {
    case 1:
      return "primary";
    case 2:
      return "warning";
    case 3:
      return "success";
  }
};

const createdMenus = () => {
  const { name, url, path, routeName, type, icon, sort } = menuForm.value;
  vaildForm()
    ?.then(() => {
      requetCreatedMenu({
        parentId: parentMenu.value.id,
        name,
        url,
        path,
        routeName,
        type,
        icon,
        sort
      }).then(({ data }) => {
        dialogConfig.value.isVisible = false;
        ElMessage({
          message: "已添加",
          type: "success"
        });
        parentMenu.value.children.push(data);
      });
    })
    .catch(e => {
      throw new Error(`表单填写错误:${e}`);
    });
};
const upDatedMenu = () => {
  //菜单表单有两种类型，新建的时候没有id但是其实
  if (!menuForm.value.id) {
    return;
  }
  const { id, name, routeName, url, path, icon, sort } = menuForm.value;
  vaildForm()
    ?.then(() => {
      const req: {
        id: number;
        icon: string;
        name: string;
        path?: string;
        routeName?: string;
        sort: number;
        url?: string;
      } = {
        id,
        name,
        url,
        path,
        icon,
        sort
      };
      if (menuForm.value.type != 4) {
        req.routeName = routeName;
      }
      requetUpdateMenu(req).then(({ data }) => {
        const { name, icon, sort, url, path } = data;
        ElMessage.success("已修改");
        if (menuToUpdate.value) {
          menuToUpdate.value.name = name;
          menuToUpdate.value.icon = icon;
          menuToUpdate.value.sort = sort;
          menuToUpdate.value.url = url;
          menuToUpdate.value.path = path;
        }
        dialogConfig.value.isVisible = false;
      });
    })
    .catch(e => {
      throw new Error(`表单填写错误:${e}`);
    });
};
//打开添加根菜单的弹窗
const onAddRootMenuClick = () => {
  menuForm.value = {
    icon: "Apple",
    name: "",
    parentId: 0,
    path: "",
    routeName: "",
    sort: 1,
    type: 1,
    url: ""
  };
  parentMenu.value = {
    id: 0,
    type: 1,
    children: menus.value
  };
  dialogConfig.value = {
    type: 0,
    title: "添加",
    isVisible: true
  };
};

const onOpenAddChildrenDialogClick = (menu: Menu) => {
  parentMenu.value = {
    id: menu.id,
    type: menu.type,
    children: menu.children
  };
  menuForm.value = {
    id: 0,
    sort: 1,
    name: "",
    icon: "Apple",
    path: "",
    url: "",
    routeName: menu.type == 4 ? menu.routeName : "",
    type: 0
  };
  switch (parentMenu.value.type) {
    //case 父级设置子级
    case 1:
      menuForm.value.type = 1;
      break;
    case 2:
      menuForm.value.type = 4;
      break;
    case 4:
      menuForm.value.type = 3;
      break;
    default:
      throw new Error(`父级类型不能为${parentMenu.value.type}`);
  }
  dialogConfig.value = {
    type: 2,
    title: "添加",
    isVisible: true
  };
};
const onOpenEditMenuDialogClick = (menu: Menu) => {
  menuToUpdate.value = menu;
  menuForm.value = JSON.parse(JSON.stringify(menu));
  dialogConfig.value = {
    type: 1,
    title: "编辑菜单",
    isVisible: true
  };
};

const onRemoveMenu = ({ id, name }: { id: number; name: string }) => {
  ElMessageBox.confirm(`是否删除:${name}`, "删除提示", {
    confirmButtonText: "删除",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      requestDeleteMenu(id)
        .then(() => {
          menus.value = deleteMenu(menus.value, id);
          ElMessage({
            type: "success",
            message: `已删除: ${name}`
          });
        })
        .catch(e => {
          console.log(e);
        });
    })
    .catch(() => {});
};
const onSaveMenuClick = () => {
  switch (dialogConfig.value.type) {
    case 0:
      //0 添加根目录
      createdMenus();
      break;
    case 1:
      //1 编辑
      upDatedMenu();
      break;
    default:
      //2 4添加子级
      createdMenus();
      break;
  }
  ruleFormRef.value?.clearValidate();
};
const getMenus = () => {
  requetGetMenus().then(({ data }) => {
    menus.value = data;
  });
};

watch(
  () => menuForm.value.type,
  () => {
    ruleFormRef.value?.clearValidate();
  },
  { deep: true }
);

onMounted(() => {
  getMenus();
});
</script>

<template>
  <div class="menu-page">
    <div class="toolbar">
      <el-button type="primary" :icon="icons.Plus" @click="onAddRootMenuClick">根目录</el-button>
    </div>

    <el-table :data="menus" height="100%" width="100" row-key="id" border>
      <el-table-column prop="name" label="名称" sortable />
      <el-table-column prop="icon" label="图标" width="56px">
        <template #default="scope">
          <el-icon :size="16" style="color: #777">
            <component :is="icons[scope.row.icon as keyof typeof icons]" />
          </el-icon>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="类型" sortable width="80px">
        <template #default="scope">
          <el-tag class="ml-2" :type="getTagTypeByRowType(scope.row.type)">
            {{ scope.row.typeStr }}</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column prop="sort" label="排序" width="80px" sortable />
      <el-table-column prop="path" label="文件路径" />
      <el-table-column prop="url" label="url" />
      <el-table-column label="操作" width="260px">
        <template #default="append">
          <el-button
            :icon="icons.Plus"
            :type="append.row.type == 1 ? 'warning' : append.row.type == 2 ? 'primary' : 'success'"
            plain
            @click="onOpenAddChildrenDialogClick(append.row)"
            v-if="append.row.type != 3"
          >
            {{ getButtonName(append.row.type) }}
          </el-button>
          <el-button @click="onOpenEditMenuDialogClick(append.row)">
            编辑
            <!-- 编辑 -->
          </el-button>
          <el-button
            type="danger"
            plain
            @click="onRemoveMenu(append.row)"
            v-if="!append.row.children[0]"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="dialogConfig.isVisible"
      :title="dialogConfig.title"
      width="60%"
      :close-on-click-modal="false"
      destroy-on-close
      draggable
    >
      <el-form :model="menuForm" ref="ruleFormRef" :rules="rules" style="max-width: 460px">
        <el-form-item label="类型" label-width="100px">
          <el-radio-group v-model="menuForm.type" :disabled="dialogConfig.type == 1">
            <el-radio-button
              v-for="item in menuTypeRadioConfig"
              :label="item.value"
              :disabled="item.disableMenuTypes.includes(parentMenu.type)"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="名称" label-width="100px" prop="name">
          <el-input v-model="menuForm.name" key="nameInput" />
        </el-form-item>
        <template v-if="menuForm.type == 2 || menuForm.type == 4">
          <el-form-item label="文件路径" label-width="100px" prop="path">
            <el-input v-model="menuForm.path" />
          </el-form-item>
          <el-form-item label="url" label-width="100px" prop="url">
            <el-input v-model="menuForm.url" />
          </el-form-item>
        </template>

        <el-form-item
          label="路由名称"
          label-width="100px"
          v-if="menuForm.type != 3"
          prop="routeName"
        >
          <el-input v-model="menuForm.routeName" />
        </el-form-item>
        <template v-if="menuForm.type == 1 || menuForm.type == 2">
          <el-form-item label="图标" label-width="100px" prop="icon">
            <el-select
              v-model="menuForm.icon"
              placeholder="菜单前面的图标"
              popper-class="menu-page-menu-icon-select-box"
            >
              <template #prefix>
                <el-icon :size="16" style="color: #888">
                  <component :is="icons[menuForm.icon as keyof typeof icons]" />
                </el-icon>
              </template>
              <el-option
                class="icon-option"
                v-for="icon in icons"
                :key="icon.name"
                :label="icon.name"
                :value="icon.name as string"
              >
                <el-icon :title="icon.name" :size="30">
                  <component :is="icon" />
                </el-icon>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序" label-width="100px">
            <el-input-number v-model="menuForm.sort" :min="1" />
          </el-form-item>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogConfig.isVisible = false">取消</el-button>
          <el-button type="primary" @click="onSaveMenuClick">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.menu-page {
  display: grid;
  grid-template-rows: minmax(40px, 5vh) auto;
  overflow: hidden;
  .toolbar {
    display: flex;
    align-items: center;
    padding: 0 0.5vw;
    background-color: var(--el-color-info-light-9);
  }
  .icon-option {
    display: inline-block;
    height: auto;
    padding: 10px 11px 0px;
  }
}
</style>
<style lang="less">
.menu-page-menu-icon-select-box {
  width: 990px;
  @media screen and (max-width: 1366px) {
    width: 670px;
  }
  .el-select-dropdown__list {
    display: grid;
    grid-template-columns: repeat(auto-fill, 34px);
    grid-auto-rows: 34px;
    gap: 0.5vw;
    .el-select-dropdown__item {
      padding: 0 !important;
      height: 100% !important;
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
