<script lang="ts" setup>
import { ref, computed } from "vue";
import { useStore } from "@/store";
import { requestReadNotification, requestReadAllNotification } from "@/apis/notifications";

const store = useStore();
const activeName = ref("");

const notifications = computed(() => store.state.notifications);
const readedOfAll = computed(() => notifications.value.every(item => item.readed));

const onClearAllNotificationBtnClick = () => {
  if (!readedOfAll.value)
    requestReadAllNotification().then(() => {
      store.commit("readAllNotification");
    });
};
const onCollapseChange = (id: any) => {
  if (id) {
    const notification = notifications.value.find(item => item.id == id);
    if (notification && !notification.readed) {
      requestReadNotification(id).then(() => {
        store.commit("readNotification", id);
      });
    }
  }
};
</script>
<template>
  <div class="notification-page">
    <template v-if="notifications.length > 0">
      <el-badge class="clear-notifications-btn-wrap" is-dot :hidden="readedOfAll">
        <el-button
          class="clear-notifications-btn"
          text
          bg
          type="primary"
          @click="onClearAllNotificationBtnClick"
          >清除未读</el-button
        ></el-badge
      >

      <el-scrollbar class="notifications-wrap">
        <el-collapse v-model="activeName" accordion @change="onCollapseChange">
          <el-collapse-item
            class="notification"
            v-for="notification in notifications"
            :name="notification.id"
          >
            <template #title>
              <span class="notification__title" style="flex: 1">{{ notification.title }}</span>
              <el-badge class="notification__time-wrap" is-dot :hidden="notification.readed"
                ><span class="notification__time">{{ notification.createTime }}</span></el-badge
              >
            </template>
            <div>
              {{ notification.content }}
            </div>
          </el-collapse-item></el-collapse
        >
      </el-scrollbar>
    </template>
    <el-empty class="empty" v-else description="暂无通知"></el-empty>
  </div>
</template>
<style lang="less" scoped>
.notification-page {
  display: grid;
  grid-template-rows: 40px auto;
  grid-gap: 10px;
  .clear-notifications-btn-wrap {
    justify-self: right;
  }
  .notifications-wrap {
    overflow: hidden;
  }
  .notification {
    :deep(.el-collapse-item__header) {
      line-height: normal;
    }
    &__title {
      font-weight: 600;
      font-size: 16px;
    }
    &__time-wrap {
      margin-right: 10px;
    }
    &__time {
      color: #909399;
      padding-right: 5px;
    }
  }
  .empty {
    grid-row: 1 / 3;
  }
}
</style>
