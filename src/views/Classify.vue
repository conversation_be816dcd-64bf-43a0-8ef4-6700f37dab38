<script lang="ts"></script>

<script lang="ts" setup>
import {
  requestCreateArticleClass,
  requestDeleteArticleClass,
  requestGetArticleClasses,
  requestUpdateArticleClass,
  ResponseGetArticleClass
} from "@/apis/articleClasses";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";

const search = reactive({
  name: ""
});

const articleClassifies = ref<ResponseGetArticleClass[]>([]);

const currentPage = ref(1);
const ROWS = ref(10);
const isShowEditDialog = ref(false);
const rules = {
  name: [
    {
      required: true,
      message: "请输入登内容",
      trigger: "blur"
    }
  ]
};
const editForm = ref<{
  name: string;
  id?: number;
}>({
  name: "",
  id: undefined
});
const editFormRef = ref<FormInstance>();
const articleClassifiesToSearch = computed(() => {
  return articleClassifies.value.filter(ele => {
    return ele.name.includes(search.name);
  });
});

const articleClassifiesToPagination = computed(() => {
  const startIndex = (currentPage.value - 1) * ROWS.value;
  const endIndex = currentPage.value * ROWS.value;
  return articleClassifiesToSearch.value.slice(startIndex, endIndex);
});

const onCreatClassityBtnClick = () => {
  editFormRef.value?.clearValidate();
  editForm.value.name = "";
  editForm.value.id = undefined;

  isShowEditDialog.value = true;
};
const onResetSearchBtnClick = () => {
  search.name = "";
};
const onEditClassifyBtnClick = (row: ResponseGetArticleClass) => {
  editForm.value.name = row.name;
  editForm.value.id = row.id;
  isShowEditDialog.value = true;
};

const onConfirmEditFormBtnClick = async () => {
  const res = await editFormRef.value?.validate();
  if (res) {
    if (editForm.value.id) {
      try {
        const { data } = await requestUpdateArticleClass({
          id: editForm.value.id,
          name: editForm.value.name
        });
        const index = articleClassifies.value.findIndex(ele => ele.id == editForm.value.id);
        if (index != -1) {
          articleClassifies.value[index].name = data.name;
        }

        ElMessage({
          message: "修改成功！",
          type: "success"
        });
      } catch (error) {
        console.log(error);

        ElMessage.error("修改失败!");
      }
    } else {
      try {
        const { data } = await requestCreateArticleClass(editForm.value.name);
        articleClassifies.value.push({
          id: data.id,
          name: data.name
        });
        ElMessage({
          message: "添加成功！",
          type: "success"
        });
      } catch (error) {
        ElMessage.error("添加失败!");
      }
    }
    isShowEditDialog.value = false;
  }
};
const onDeleteClassifyBtnClick = async (row: ResponseGetArticleClass) => {
  await ElMessageBox.confirm("确定删除此分类吗？", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });

  try {
    await requestDeleteArticleClass(row.id);
    const index = articleClassifies.value.findIndex(ele => ele.id == row.id);
    console.log("index", index);

    if (index != -1) {
      articleClassifies.value.splice(index, 1);
    }

    ElMessage({
      message: "删除成功！",
      type: "success"
    });
  } catch (error) {
    ElMessage.error("删除失败!");
  }
};
onMounted(async () => {
  const { data } = await requestGetArticleClasses();
  articleClassifies.value = data;
});
</script>

<template>
  <div class="classify-wrap">
    <div class="search">
      <el-input style="width: 25rem" v-model="search.name" placeholder="请输入分类名称" clearable />
      <el-button type="primary" size="default" @click="onCreatClassityBtnClick">添加分类</el-button>
      <el-button @click="onResetSearchBtnClick">重置</el-button>
    </div>
    <el-table style="flex: 1" :data="articleClassifiesToPagination">
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="name" label="名称" />
      <el-table-column label="操作" align="center" width="240">
        <template #default="{ row }">
          <el-button type="primary" text bg plain @click="onEditClassifyBtnClick(row)"
            >编辑</el-button
          >
          <el-button type="danger" text bg plain @click="onDeleteClassifyBtnClick(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      layout="prev, pager, next"
      :total="articleClassifiesToSearch.length"
      :page-size="ROWS"
      v-model:current-page="currentPage"
    />
    <el-dialog title="编辑分类" v-model="isShowEditDialog" width="40%">
      <div class="form">
        <el-form
          :model="editForm"
          ref="editFormRef"
          :rules="rules"
          label-width="80px"
          :inline="false"
        >
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="editForm.name" clearable />
          </el-form-item>
        </el-form>
        <div class="btn" slot="footer">
          <el-button @click="isShowEditDialog = false">取消</el-button>
          <el-button type="primary" @click="onConfirmEditFormBtnClick">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.classify-wrap {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  .search {
    display: flex;
    gap: 1rem;
  }
  .pagination {
    align-self: center;
  }
  .form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    .btn {
      display: inline-block;
      align-self: flex-end;
    }
  }
}
</style>
