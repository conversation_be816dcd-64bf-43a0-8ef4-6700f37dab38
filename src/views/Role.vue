<script lang="ts">
import type { Role } from "@/composables/useRole";
import type { ElTree, ElForm } from "element-plus";
</script>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { useRole, useRoles } from "@/composables/useRole";
import { useMenus } from "@/composables/useMenu";
import { onMounted, ref, computed } from "vue";
const DIALOG_TYPE_ADD = 0;
const DIALOG_TYPE_EDIT = 1;
const roleFormRules = {
  name: [
    {
      required: true,
      message: "请输入名称",
      trigger: "blur"
    }
  ]
};

const { role, createRole, updateRole, deleteRole, setRole, DEFAULT_ROLE } = useRole();
const { roles, rolesTotal, rolesFilter, getRoles, addRole, removeRole, editRole } = useRoles();
const { meuns, getMenus } = useMenus();
const dialogVisible = ref(false);
const dialogType = ref(DIALOG_TYPE_ADD);
const menuTreeRef = ref<InstanceType<typeof ElTree>>();
const roleFormRef = ref<InstanceType<typeof ElForm>>();

const dialogTitle = computed(() => {
  switch (dialogType.value) {
    case DIALOG_TYPE_ADD:
      return "添加角色";

    case DIALOG_TYPE_EDIT:
      return "编辑角色";
  }
  return "";
});

const onTableEditClick = (role: Role) => {
  dialogType.value = DIALOG_TYPE_EDIT;
  setRole(role);
  dialogVisible.value = true;
};
const onTableRemoveClick = (role: Role) => {
  ElMessageBox.confirm("是否永久删除该角色？", "Warning", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    setRole(role);
    deleteRole().then(() => {
      removeRole(role);
      ElMessage({
        message: "删除成功",
        type: "success"
      });
    });
  });
};
const onTreeCheckChange = () => {
  const keys =
    menuTreeRef.value?.getCheckedKeys(false).concat(menuTreeRef.value?.getHalfCheckedKeys()) || [];
  const menuIds = keys.map(item => parseInt(item.toString()));
  role.menuIds = menuIds;
};
const onDialogOpened = () => {
  menuTreeRef.value?.setCheckedKeys([], true);
  for (const menuId of role.menuIds) {
    const node = menuTreeRef.value?.getNode(menuId);
    if (node?.isLeaf) {
      menuTreeRef.value?.setChecked(node, true, true);
    }
  }
};
const onDialogSubmitClick = async () => {
  try {
    // 表单验证
    await roleFormRef.value?.validate();

    // 根据类型执行不同的操作
    if (dialogType.value === DIALOG_TYPE_ADD) {
      await createRole();
      addRole(role);
      ElMessage({ message: "添加成功", type: "success" });
    } else if (dialogType.value === DIALOG_TYPE_EDIT) {
      await updateRole();
      editRole(role);
      ElMessage({ message: "编辑成功", type: "success" });
    }

    // 关闭对话框
    dialogVisible.value = false;
  } catch (err: any) {
    ElMessage({
      message: err.message || "操作失败",
      type: "error"
    });
  }
};
const onAddRoleClick = () => {
  dialogType.value = DIALOG_TYPE_ADD;
  setRole(DEFAULT_ROLE);
  dialogVisible.value = true;
};
const onSearchInput = (v: string) => {
  rolesFilter.page = 1;
  rolesFilter.name = v;
};

onMounted(() => {
  getRoles();
  getMenus();
});
</script>
<template>
  <div class="default-page role-page">
    <div class="search-wrap">
      <span class="title">角色名称</span
      ><el-input
        class="name-input"
        :model-value="rolesFilter.name"
        clearable
        :max="10"
        @input="onSearchInput"
      />
    </div>
    <div class="tools-wrap">
      <el-button type="primary" @click="onAddRoleClick">添加角色</el-button>
    </div>
    <el-table :data="roles" :border="true" width="100%" height="100%">
      <el-table-column prop="id" label="Id" width="100px" />
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="isAdmin" label="是否管理员">
        <template #default="{ row }">
          <el-tag v-if="row.isAdmin">是</el-tag>
          <el-tag v-else type="info">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200px" header-align="center" align="center">
        <template #default="scope">
          <el-button type="primary" text bg @click="onTableEditClick(scope.row)">编辑</el-button>
          <el-button type="danger" text bg @click="onTableRemoveClick(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      layout="prev, pager, next"
      :total="rolesTotal"
      :page-size="rolesFilter.rows"
      v-model:current-page="rolesFilter.page"
    ></el-pagination>
    <el-dialog
      v-model="dialogVisible"
      @opened="onDialogOpened"
      :title="dialogTitle"
      :close-on-click-modal="false"
    >
      <el-form
        class="role-form"
        :model="role"
        :rules="roleFormRules"
        ref="roleFormRef"
        label-width="120px"
      >
        <el-form-item label="Id" v-if="dialogType == DIALOG_TYPE_EDIT">
          <el-input v-model="role.id" disabled />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="role.name" />
        </el-form-item>
        <el-form-item label="是否管理员" prop="isAdmin">
          <el-switch v-model="role.isAdmin" />
        </el-form-item>
        <el-form-item label="权限">
          <el-card shadow="never" class="permissions-wrap">
            <el-scrollbar class="permissions-scrollbar">
              <el-tree
                ref="menuTreeRef"
                :data="meuns"
                node-key="id"
                default-expand-all
                empty-text="暂无数据"
                show-checkbox
                highlight-current
                @check-change="onTreeCheckChange"
              >
                <template v-slot="{ data }">
                  <span :data-roleId="data.id">
                    {{ data.name }}
                  </span>
                </template>
              </el-tree>
            </el-scrollbar>
            <!-- card body -->
          </el-card>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="onDialogSubmitClick">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="less" scoped>
.role-page {
  .search-wrap {
    .title {
      margin-right: 10px;
    }
    .name-input {
      width: 200px;
    }
  }

  .role-form {
    .permissions-wrap {
      width: 100%;
      .permissions-scrollbar {
        :deep(.el-scrollbar__wrap) {
          max-height: 400px;
        }
      }
    }
  }
  @media screen and(max-width:1366px) {
    .role-form {
      .permissions-wrap {
        .permissions-scrollbar {
          :deep(.el-scrollbar__wrap) {
            max-height: 180px;
          }
        }
      }
    }
  }
  @media screen and(max-width:1600px) {
    .role-form {
      .permissions-wrap {
        .permissions-scrollbar {
          :deep(.el-scrollbar__wrap) {
            max-height: 230px;
          }
        }
      }
    }
  }
}
</style>
