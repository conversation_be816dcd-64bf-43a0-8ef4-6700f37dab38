<script lang="ts" setup>
import { SummaryTemplate } from "@/types/summaryTemplate";
import { useDialog } from "@/composables/useDialog";
import { useModeAddOrEdit } from "@/composables/useModeAddOrEdit";
import { useSummaryTemplates } from "@/models/useSummaryTemplates";
import { useSummaryTemplate } from "./useSummaryTemplate";
import WangEdit from "@/components/WangEdit/Index.vue";
import { FormInstance, FormRules } from "element-plus";

defineOptions({
  name: "SummaryTemplate"
});
const {
  dialog: summaryTemplateDialog,
  openDialog: openSummaryTemplateDialog,
  closeDialog: closeSummaryTemplateDialog
} = useDialog();

const { setModeToAdd, setModeToEdit, modeIsAdd, modeIsEdit } = useModeAddOrEdit();

//#region 小结模板列表和过滤
const filterCondition = ref("");
function resetFilters() {
  filterCondition.value = "";
}

const { summaryTemplates, loadSummaryTemplates } = useSummaryTemplates();
const summaryTemplatesFiltered = computed(() => {
  if (summaryTemplates.value.length == 0) {
    return [];
  }
  return summaryTemplates.value.filter(
    s => s.content.includes(filterCondition.value) || s.title.includes(filterCondition.value)
  );
});

function onResetFilters() {
  resetFilters();
  loadSummaryTemplates();
  ElMessage.success("已重置");
}
//#endregion

//#region 小结模板
const {
  summaryTemplate,
  setSummaryTemplate,
  resetSummaryTemplate,
  saveAddSummaryTemplate,
  saveEditSummaryTemplate,
  removeSummaryTemplate
} = useSummaryTemplate();

const summaryTemplateRef = ref<FormInstance>();
const summaryTemplateFormRules = computed<FormRules>(_ => {
  return {
    title: [{ required: true, message: "请输入标题" }],
    content: [
      {
        trigger: "blur",
        validator: (_, v, cb) => {
          if (!v || v == "<p><br></p>") {
            cb("请输入内容");
          } else {
            cb();
          }
        }
      }
    ]
  };
});
function onAddSummaryTemplate() {
  setModeToAdd();
  openSummaryTemplateDialog();
}

function onEditSummaryTemplate(template: SummaryTemplate) {
  setModeToEdit();
  openSummaryTemplateDialog();
  setSummaryTemplate(template);
}

async function onRemoveSummaryTemplate({ id }: SummaryTemplate) {
  try {
    await ElMessageBox.confirm("删除不可逆 是否继续", "警告", {
      confirmButtonText: "删除",
      cancelButtonText: "取消操作",
      type: "warning"
    });
    try {
      await removeSummaryTemplate(id);
      await loadSummaryTemplates();
      ElMessage.success("已删除");
    } catch (error) {
      ElMessage.error(`${error}`);
    }
  } catch (error) {}
}

const saveButtonIsLoading = ref(false);
async function onSaveSummaryTemplate() {
  const v = await summaryTemplateRef?.value?.validate();
  if (!v) return;

  saveButtonIsLoading.value = true;
  switch (true) {
    case modeIsAdd():
      try {
        await saveAddSummaryTemplate(summaryTemplate.value);
        saveButtonIsLoading.value = false;
        closeSummaryTemplateDialog();
        ElMessage.success("已添加模板");
        loadSummaryTemplates();
      } catch (error) {
        ElMessage.error(`${error}`);
      }
      break;

    case modeIsEdit():
      try {
        await saveEditSummaryTemplate(summaryTemplate.value);
        saveButtonIsLoading.value = false;
        closeSummaryTemplateDialog();
        ElMessage.success("已保存模板");
        loadSummaryTemplates();
      } catch (error) {
        ElMessage.error(`${error}`);
      }

      break;
  }
}
//#endregion

function onSummaryTemplateDialogClose() {
  resetSummaryTemplate();
}

function onCancelSummaryTemplateDialog() {
  closeSummaryTemplateDialog();
}

onMounted(() => {
  loadSummaryTemplates();
});
</script>

<template>
  <div class="summary-template-page">
    <div class="summary-template-page__header">
      <div class="page__header__filters">
        <div class="page__header__filter">
          <el-input
            v-model="filterCondition"
            placeholder="请输入模板标题"
            clearable
            @clear="resetFilters"
            style="width: 220px"
          />
        </div>
      </div>

      <el-button type="primary" text bg @click="onResetFilters">重置</el-button>
    </div>
    <div class="summary-template-page__tool">
      <el-button type="primary" @click="onAddSummaryTemplate"> 新建 </el-button>
    </div>
    <el-table :data="summaryTemplatesFiltered" style="width: 100%" height="100%">
      <el-table-column prop="title" label="模板标题" width="180" />
      <el-table-column prop="content" label="模板内容" min-width="180">
        <template #default="{ row }">
          <PreviewWangEditor :modelValue="row.content"></PreviewWangEditor>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template #default="{ row }">
          <el-button bg text type="primary" @click="onEditSummaryTemplate(row)">编辑</el-button>
          <el-button type="danger" bg text @click="onRemoveSummaryTemplate(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="summaryTemplateDialog.visible"
      :title="summaryTemplateDialog.title"
      width="800"
      :close-on-click-modal="false"
      destroy-on-close
      @close="onSummaryTemplateDialogClose"
      :show-close="!saveButtonIsLoading"
      :close-on-press-escape="false"
    >
      <div class="summary-template-dialog__body">
        <el-form
          label-width="auto"
          :model="summaryTemplate"
          ref="summaryTemplateRef"
          :rules="summaryTemplateFormRules"
        >
          <el-form-item label="标题" prop="title">
            <el-input v-model="summaryTemplate.title"> </el-input>
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <WangEdit style="min-height: 301px; height: 5rem" v-model="summaryTemplate.content" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onCancelSummaryTemplateDialog" :disabled="saveButtonIsLoading"
            >取消</el-button
          >
          <el-button type="primary" :loading="saveButtonIsLoading" @click="onSaveSummaryTemplate">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.summary-template-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  display: grid;
  grid-template-rows: 32px 48px 1fr;
  gap: 10px 0;
  .summary-template-page__header {
    display: flex;
    gap: 10px;
    .page__header__filters {
      display: flex;
    }
  }
  .summary-template-page__tool {
    display: flex;
    align-items: center;
    flex: 0 0 40px;
    padding: 4px;
    background-color: #f5f7fa;
  }
  .summary-template-dialog__body {
    height: 6rem;
    width: 100%;
  }
  :deep(.phone-warp) {
    max-width: 700px;
  }
}
</style>
