import { SummaryTemplate, SummaryTemplates, PreSummaryTemplate } from "@/types/summaryTemplate";
import {
  requestAddSummaryTemplate,
  requestEditSummaryTemplate,
  requestDeleteSummaryTemplate
} from "@/apis/summaryTemplate";

const defaultData: SummaryTemplate = {
  id: 0,
  title: "",
  content: ""
};

export const useSummaryTemplate = () => {
  const summaryTemplate = ref<SummaryTemplate>(JSON.parse(JSON.stringify(defaultData)));

  function resetSummaryTemplate() {
    summaryTemplate.value = JSON.parse(JSON.stringify(defaultData));
  }
  function setSummaryTemplate(s: SummaryTemplate) {
    summaryTemplate.value = s;
  }

  function saveAddSummaryTemplate(p: PreSummaryTemplate) {
    return requestAddSummaryTemplate(p);
  }

  function saveEditSummaryTemplate(s: SummaryTemplate) {
    return requestEditSummaryTemplate(s);
  }

  function removeSummaryTemplate(id: number) {
    return requestDeleteSummaryTemplate(id);
  }

  return {
    summaryTemplate,
    setSummaryTemplate,
    resetSummaryTemplate,
    saveAddSummaryTemplate,
    saveEditSummaryTemplate,
    removeSummaryTemplate
  };
};
