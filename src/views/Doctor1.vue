<script lang="ts">
interface Doctor {
  id: number;
  userName: string;
  name: string;
  password: string;
  headPortrait: string;
  roleId: number;
  phone: string | null;
}

interface DoctorFilter {
  page: number;
  row: number;
  name?: string;
  roleId?: number;
  userName?: string;
}

enum Operation {
  添加,
  修改
}
</script>
<script lang="ts" setup>
import { useRoles } from "@/composables/useRole";
import {
  requestGetDoctors,
  requestCreateDoctor,
  requestUpdateDoctor,
  requestDeleteDoctor
} from "@/apis/doctors";
import { validatorPhone } from "@/utils/validationRules";
import { FormInstance } from "element-plus";

const DEFAULT_DOCTOR = readonly<Doctor>({
  id: 0,
  userName: "",
  name: "",
  password: "",
  headPortrait: "/default-headportrait.jpg",
  roleId: 0,
  phone: ""
});

const { roles, getRoles } = useRoles();

const formRef = ref<FormInstance>();

const pagination = reactive({
  page: 1,
  row: 10,
  total: 0
});

const doctorFilter = reactive<DoctorFilter>({
  ...pagination,
  roleId: undefined,
  userName: "",
  name: ""
});

const dialog = reactive({
  operation: Operation.添加,
  visible: false,
  isUpdatePassword: false
});

const doctorForm = ref<Doctor>({ ...DEFAULT_DOCTOR });

const doctors = ref<Doctor[]>([]);

const rules = {
  userName: [
    {
      required: true,
      message: "请输入登录账号",
      trigger: "blur"
    }
  ],
  name: [
    {
      required: true,
      message: "请输入用户名",
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: "blur"
    },
    {
      min: 6,
      max: 11,
      message: "密码长度在6-11位之间",
      trigger: "blur"
    }
  ],
  roleId: [{ required: true, message: "请选择用户角色", trigger: "blur" }],
  phone: [
    {
      validator: (rule: any, value: string, callback: (err?: Error) => void) => {
        if (value) {
          validatorPhone(value) ? callback() : callback(new Error("手机号码格式不正确"));
        }
        callback();
      },
      message: "手机号码格式错误",
      trigger: "blur"
    }
  ]
};

const query = () => {
  doctorFilter.page = 1;
  getDoctors();
};
const resetQuery = () => {
  doctorFilter.page = 1;
  doctorFilter.roleId = undefined;
  doctorFilter.userName = "";
  doctorFilter.name = "";
  getDoctors();
};

const getDoctors = () => {
  requestGetDoctors(doctorFilter).then(res => {
    const { data } = res;
    doctors.value = data.doctors;
    pagination.total = data.total;
  });
};

const onCreateDoctorClick = () => {
  formRef.value?.clearValidate();
  doctorForm.value = { ...DEFAULT_DOCTOR };
  doctorForm.value.roleId = roles.value.length > 0 ? roles.value[0].id : 0;
  dialog.operation = Operation.添加;
  dialog.visible = true;
  dialog.isUpdatePassword = true;
};
const onEditClick = (doctor: Doctor) => {
  formRef.value?.clearValidate();
  doctorForm.value = { ...doctor };
  dialog.operation = Operation.修改;
  dialog.isUpdatePassword = false;
  dialog.visible = true;
};
const onSubmitClick = () => {
  formRef.value
    ?.validate()
    .then(() => {
      switch (dialog.operation) {
        case Operation.添加:
          requestCreateDoctor(doctorForm.value).then(res => {
            const { data } = res;
            if (doctors.value.length < doctorFilter.row) doctors.value.push(data);
            ElMessage.success("创建成功");
            dialog.visible = false;
          });
          break;
        case Operation.修改:
          requestUpdateDoctor(doctorForm.value).then(res => {
            const { data } = res;
            const index = doctors.value.findIndex(d => d.id == data.id);
            index > 0 ? doctors.value.splice(index, 1, { ...data }) : null;
            ElMessage.success("保存成功");
            dialog.visible = false;
          });
          break;
      }
    })
    .catch(() => {
      ElMessage.warning("请先验证输入");
    });
};
const onDeleteClick = (id: number) => {
  ElMessageBox.confirm("该操作将永久删除该条数据，是否继续？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    requestDeleteDoctor(id).then(_ => {
      getDoctors();
      ElMessage["success"]("删除成功");
    });
  });
};

const onCurrentPageChange = (page: number) => {
  getDoctors();
};

onMounted(() => {
  getRoles();
  getDoctors();
});
</script>
<template>
  <div class="default-page doctor-page">
    <div class="doctor-page__header">
      <div class="header-filter">
        <div class="header-filter__item">
          <div class="header-filter__item__label">角色</div>
          <el-select
            v-model="doctorFilter.roleId"
            class="header-filter__item__content"
            placeholder="请选择角色"
          >
            <el-option v-for="item in roles" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </div>
        <div class="header-filter__item">
          <div class="header-filter__item__label">账号</div>
          <el-input
            class="header-filter__item__content"
            v-model="doctorFilter.userName"
            placeholder="请输入账号"
            clearable
          ></el-input>
        </div>
        <div class="header-filter__item">
          <div class="header-filter__item__label">用户名</div>
          <el-input
            class="input header-filter__item__content"
            v-model="doctorFilter.name"
            placeholder="请输入用户名"
            clearable
          ></el-input>
        </div>
      </div>
      <div class="button-group">
        <el-button type="primary" @click="query">查询</el-button>
        <el-button @click="resetQuery" style="margin: 0">重置</el-button>
      </div>
    </div>
    <div class="operation">
      <el-button type="primary" @click="onCreateDoctorClick"> 添加医生 </el-button>
    </div>
    <el-table :data="doctors" :border="true" width="100%" height="100%">
      <el-table-column prop="userName" label="账号" />
      <el-table-column prop="name" label="用户名" />
      <el-table-column prop="roleId" label="角色">
        <template #default="scope">
          <el-tag>{{ roles.find(r => r.id == scope.row.roleId)?.name }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="手机号码" />
      <el-table-column label="操作" width="180">
        <template #default="scope">
          <el-button type="primary" @click="onEditClick(scope.row)"> 编辑 </el-button>
          <el-button type="danger" @click="onDeleteClick(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      layout="prev, pager, next"
      :total="pagination.total"
      :page-size="doctorFilter.row"
      v-model:current-page="doctorFilter.page"
      @current-change="onCurrentPageChange"
    ></el-pagination>
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.operation == Operation.添加 ? '添加' : '编辑'"
      width="30%"
    >
      <el-form ref="formRef" :model="doctorForm" :rules="rules" label-width="80px">
        <el-form-item label="账号" prop="userName">
          <el-input
            v-model="doctorForm.userName"
            :disabled="dialog.operation == Operation.修改"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户名" prop="name">
          <el-input v-model="doctorForm.name"></el-input>
        </el-form-item>
        <el-form-item
          v-if="dialog.isUpdatePassword"
          :label="dialog.operation == Operation.添加 ? '密码' : '新密码'"
          prop="password"
        >
          <el-input v-model="doctorForm.password" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="doctorForm.phone" />
        </el-form-item>
        <el-form-item label="角色" prop="roleId">
          <el-select v-model="doctorForm.roleId" placeholder="请选择角色">
            <el-option v-for="item in roles" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="修改密码" v-if="dialog.operation != Operation.添加">
          <el-switch v-model="dialog.isUpdatePassword" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="onSubmitClick">{{
            dialog.operation == Operation.添加 ? "创建医生" : "保存修改"
          }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.doctor-page {
  gap: 10px;
  .doctor-page__header {
    width: 100%;
    display: flex;
    gap: 20px;
    .header-filter {
      display: flex;
      gap: 10px;
      align-items: center;
      .header-filter__item {
        display: flex;
        align-items: center;
        gap: 20px;

        .header-filter__item__label {
          display: flex;
          flex-shrink: 0;
          align-items: center;
        }
        .header-filter__item__content {
        }
        .el-select {
          width: 160px;
        }
      }
    }
    .button-group {
      display: flex;
      gap: 10px;
    }
  }
  :deep(.el-overlay) {
    text-align: start;
  }
}
@media screen and (max-width: 1230px) {
  .doctor-page {
    grid-template-rows: 100px 50px auto 50px;
  }
}
</style>
