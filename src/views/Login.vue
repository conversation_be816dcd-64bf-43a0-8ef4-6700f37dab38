<script lang="ts" setup>
import { ref, reactive } from "vue";
import { ElForm } from "element-plus";
import { requestDoctorLogin, WhoIM } from "@/apis/account";
import { useRouter, useRoute } from "vue-router";
import token from "@/utils/token";
import moment from "moment";

const route = useRoute();
const router = useRouter();
const logging = ref(false);
const fromRef = ref<InstanceType<typeof ElForm>>();
const user = reactive({
  userName: "",
  password: ""
});

const formRules = {
  userName: [
    {
      required: true,
      message: "请输入用户名",
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: "请输入密码",
      trigger: "blur"
    },
    {
      min: 6,
      max: 11,
      message: "密码长度在6-11位之间",
      trigger: "blur"
    }
  ]
};

const onLoginClick = () => {
  fromRef.value?.validate()?.then(() => {
    logging.value = true;
    requestDoctorLogin(user.userName, user.password, WhoIM.医生)
      .then(res => {
        const { access_token, refresh_token, expires_in } = res.data;
        token.update(access_token, refresh_token, moment().add(expires_in, "s").toDate());

        const redirectQuery = route.query["redirect"] as string;

        router.push({
          path: redirectQuery || "/home"
        });
      })
      .finally(() => {
        logging.value = false;
      });
  });
};
</script>

<template>
  <div class="login-page">
    <img class="loginInsert_img" src="@/assets/login_insert.png" alt="" />
    <img class="loginLogo_img" src="@/assets/login_logo.png" alt="" />
    <div class="login-from">
      <h3>VIP体检系统</h3>
      <el-form
        @keyup.enter.native="onLoginClick"
        size="large"
        label-position="top"
        ref="fromRef"
        :model="user"
        status-icon
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="user.userName"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="user.password"
            type="password"
            autocomplete="off"
            show-password
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            class="login-btn"
            :loading="logging"
            type="primary"
            color="#00612F"
            @click="onLoginClick"
            >登录</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<style lang="less" scoped>
.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: url("@/assets/login_bg.png") no-repeat center;
  background-size: cover;
  position: relative;

  .loginInsert_img {
    position: absolute;
    top: 30vh;
    left: 11rem;
  }

  .loginLogo_img {
    position: absolute;
    right: 7rem;
    top: 4rem;
  }

  .login-from {
    width: 400px;
    position: absolute;
    z-index: 10;
    right: 12.9rem;
    top: 30vh;
    h3 {
      font-size: 3.8rem;
      color: #006130;
      font-family: Arial-Black;
    }
    .login-btn {
      width: 100%;
    }
  }
}
</style>
