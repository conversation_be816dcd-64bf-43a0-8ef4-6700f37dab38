import { Patient } from "@/apis/patients";

export type OutReportFile = File;
export type OutReportsFiles = OutReportFile[];

export type OutReportLink = string;
export type OutReportsLinks = OutReportLink[];

export interface OutReport {
  id?: number;
  file: OutReportFile | null;
  link: OutReportLink;
}

export type OutReports = OutReport[];

export interface UserOutReport extends OutReport {
  patientId: Patient["id"]; //0为无效
}

export interface UserOutReports {
  patientId: Patient["id"]; //0为无效
  outReports: OutReports;
}
