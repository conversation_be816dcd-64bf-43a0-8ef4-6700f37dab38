import type { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";

declare module "@vue/runtime-core" {
  export interface ComponentCustomProperties {
    $message: typeof ElMessage
    $messageBox: typeof ElMessageBox
  }
}

declare global {
  const ElMessage: typeof import("element-plus/es")["ElMessage"];
  const ElMessageBox: typeof import("element-plus/es")["ElMessageBox"];
 
} 
declare global {
 
  export type { FormInstance, FormRules } from "element-plus";
   
}
 
