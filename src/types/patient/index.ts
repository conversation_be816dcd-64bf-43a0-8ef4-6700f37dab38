export type PatientId = Patient["id"];

export interface Patient {
  address: string;
  birthday: string;
  id: number;
  name: string;
  age: number;
  sex: string;
  idCard: string;
  phone: string;
  source: string;
  createTime: string;
  case: MedicalRecord;
  doctorName: string | null;
  lastFollowUpTime: string | null;
  nextFollowUpTime: string | null;
  returnVisitTime: string | null;
  patientIds: string[];
}
interface MedicalRecord {
  family: string;
  irritability: string;
  medicine: string;
  now: string;
  past: string;
}
