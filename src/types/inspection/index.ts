// 检验
export interface Inspection {
  id: number;
  compositeItemSn: string;
  execDeptName: string;
  highValue: string | null;
  inpatientOrdNo: string | null;
  itemCode: string;
  itemName: string;
  itemNameCn: string;
  itemNameCode: string;
  itemNameEn: string;
  itemUnit: string;
  itemValue: string;
  labReportLid: string;
  labReportSn: number;
  lowValue: string | null;
  normalFlagName: string;
  normalRefValueText: string;
  orderLid: string;
  orderPersonName: string;
  patientDomain: string;
  patientLid: string;
  patientName: string;
  phenomenonPerformance: string | null;
  reportDate: string;
  reporterId: string;
  reporterName: string;
  requestDate: string;
  requestNo: string;
  reviewDate: string;
  reviewerId: string;
  reviewerName: string;
  sampleNo: string;
  sampleTypeName: string;
  submittingPersonId: string;
  submittingPersonName: string;
  testResults: string | null;
  visitOrdNo: string;
  visitTimes: number;
  visitTypeCode: string;
  visitTypeName: string;
  warnHighValue: string | null;
  warnLowValue: string | null;
}

export type Inspections = Inspection[];
// 检验明细
export interface InspectionDetail extends Inspection {}
export type InspectionDetails = InspectionDetail[];

/**   将检查详情转换为预览检验详情。*/

// 检验明细打印
export interface InspectionDetailForReport extends InspectionDetail {
  sort: number;
}

export const validateUnusualItem = <T extends Inspection>({
  normalFlagName: unusualItem
}: T): boolean => unusualItem == "↑" || unusualItem == "↓";

export function filterInspectionDetailsTop(
  viewInspectionDetails: Map<
    InspectionDetail["itemNameCn"] | InspectionDetail["itemNameCode"],
    InspectionDetail[]
  >,
  top: number
) {
  // 不限次数
  if (top == 0) {
    return viewInspectionDetails;
  }
  const result = new Map<
    InspectionDetail["itemNameCn"] | InspectionDetail["itemNameCode"],
    InspectionDetail[]
  >();
  viewInspectionDetails.forEach((values, key) => {
    const slicedValues = values.slice(0, top);
    result.set(key, slicedValues);
  });
  return result;
}
