export interface Examine {
  id: number;
  itemCode: string;
  orderPerson: string;
  filePath: string | null;
  examinationDate: string | null;
  seedescex: string | null;
  sickbedNo: string | null;
  orderTime: string;
  itemClass: string;
  examReportLid: string | null;
  patientDomainId: string;
  imagUrl: string;
  patientLid: string;
  rportDoctorName: string | null;
  patientName: string;
  orderDept: string;
  reportDate: string;
  orderDeptName: string;
  functionRequestId: string;
  itemName: string;
  ipseqnoText: string | null;
  orderPersonName: string;
  execDepartmentName: string;
  resultDescex: string | null;
  orderLid: string;
  patientDomain: string;
  reportDoctor: string | null;
  itemClassName: string | null;
  status: string;
  visitTypeCode: string;
  visitTypeName: string;
}

export type Examines = Examine[];
export interface ExamineDetail extends Examine {}

export type ExamineDetails = ExamineDetail[];
export type ExamineDetailKey = ExamineDetail["itemCode"];
export type ExamineDetailValue = ExamineDetails;

// 检查明细打印
export interface ExamineDetailForReport extends ExamineDetail {
  sort: number;
}
