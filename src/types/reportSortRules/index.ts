export enum RuleType {
  "检查" = 0,
  "检验" = 1
}

export type SortRules = SortRule[];

export interface SortRule {
  id?: number;
  ruleType: RuleType;
  detail: SortRuleDetail[];
}

export interface InspectionsSortRule extends SortRule {
  id: number;
  ruleType: RuleType.检验;
  detail: SortRuleDetail[];
}

export interface ExamineSortRule extends SortRule {
  id: number;
  ruleType: RuleType.检查;
  detail: SortRuleDetail[];
}

export interface SortRuleDetail {
  itemName: string;
  itemCode: string;
  sort: number;
  isOld: boolean;
}
