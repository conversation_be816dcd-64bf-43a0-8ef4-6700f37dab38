import { request } from "@/utils/request";

export interface RoleReponseData {
  id: number;
  name: string;
  isAdmin: boolean;
  menuIds: number[];
}

export const requestRoles = () => {
  return request.get<RoleReponseData[]>("/roles");
};

export const requestCreateRole = (data: { name: string; isAdmin: boolean; menuIds: number[] }) => {
  return request.post<RoleReponseData>("/roles", data);
};

export const requestUpdateRole = (data: {
  id: number;
  name: string;
  isAdmin: boolean;
  menuIds: number[];
}) => {
  return request.put<RoleReponseData>(`/roles/${data.id}`, data);
};

export const requestDeleteRole = (id: number) => {
  return request.delete(`/roles/${id}`);
};
