import { request } from "@/utils/request";
import moment from "moment";

export interface TaskReponseData {
  id: number;
  name: string;
  status: number;
  content: string;
  summary: string | null;
  executionTime: Date;
  doctorNo: string;
  createTime: Date;
  userId: number;
  isAudit: boolean;
  isNotifyDoctor: boolean;
  statusTxt: string;
}
export interface DoctorTaskStatistical {
  doctorNo: string;
  doctorName: string;
  taskQuantity: string;
  completedQuantity: string;
  completedProgress: string;
}

export const requestTasks = (query: {
  page: number;
  row: number;
  status?: number;
  doctorNo?: string;
  name?: string;
  userId?: number;
}) => {
  return request.get<{
    tasks: TaskReponseData[];
    total: number;
  }>("/followUpTask", {
    params: query
  });
};

export const requestCreateTasks = (data: {
  userId: number;
  tasks: {
    name: string;
    content: string;
    executionTime: Date;
    isNotifyDoctor: boolean;
  }[];
}) => {
  return request.post<TaskReponseData[]>(`/followUpTask/${data.userId}`, {
    tasks: data.tasks
  });
};

export const requestUpdateTask = (task: {
  id: number;
  name: string;
  content: string;
  executionTime: Date;
  isNotifyDoctor: boolean;
}) => {
  return request.put(`/followUpTask/${task.id}`, {
    name: task.name,
    content: task.content,
    executionTime: task.executionTime,
    isNotifyDoctor: task.isNotifyDoctor
  });
};

export const requestStopTask = (ids: number[]) => {
  return request.put(`/followUpTask/stop`, ids);
};

export const requestTryStartTask = (ids: number[]) => {
  return request.put(`/followUpTask/start`, ids);
};

export const requestDeleteTask = (id: number) => {
  return request.delete(`/followUpTask/${id}`);
};

export const requestExecuteTask = (taskId: number) => {
  return request.put(`/followUpTask/execute/${taskId}`);
};

export const requestSubmitTask = (id: number, summary: string) => {
  return request.put(`/followUpTask/complete/${id}`, { summary });
};

//获取管理员随访统计
export const requestStatisticsTask = (date: [Date, Date]) => {
  return request.get(
    `/followUpTask/statistical?StartTime=${moment(date[0]).format(
      "YYYY-MM-DD"
    )} 00:00:00&EndTime=${moment(date[1]).format("YYYY-MM-DD")} 23:59:59`
  );
};
//获取医生随访统计

export const requestGetDoctorTasksStatisical = (data: {
  doctorNo: string;
  startDate: Date;
  endDate: Date;
  patientName?: string;
}) => {
  let params = `DoctorNo=${data.doctorNo}&ExcuteTime=${moment(data.startDate).format(
    "YYYY-MM-DD"
  )}%2000:00:00&ExcuteTime=${moment(data.endDate).format("YYYY-MM-DD")}%2023:59:59`;

  if (data.patientName) {
    params += `&PatientName=${data.patientName}`;
  }
  return request.get(`/followUpTask/DoctorStatistical?${params}`);
};

/**
 * 审核一个任务
 * @param taskId 任务Id
 */
export const requestAuditTask = (taskId: number) => {
  return request.put(`/followUpTask/audit/${taskId}`);
};
