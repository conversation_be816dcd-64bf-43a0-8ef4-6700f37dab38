import { request } from "@/utils/request";
import { SummaryTemplate, PreSummaryTemplate } from "@/types/summaryTemplate";

export const requestGetSummaryTemplates = async (): Promise<SummaryTemplate[]> => {
  const { data } = await request("/SummaryRecordTemplates");
  return data;
};

export const requestAddSummaryTemplate = async (
  preSummaryTemplate: PreSummaryTemplate
): Promise<SummaryTemplate> => {
  const { data } = await request.post<SummaryTemplate>(
    "/SummaryRecordTemplates",
    preSummaryTemplate
  );
  return data;
};

export const requestEditSummaryTemplate = async (
  summaryTemplate: SummaryTemplate
): Promise<SummaryTemplate> => {
  const { data } = await request.put<SummaryTemplate>("/SummaryRecordTemplates", summaryTemplate);
  return data;
};

export const requestDeleteSummaryTemplate = async (id: SummaryTemplate["id"]) => {
  return await request.delete<SummaryTemplate>(`/SummaryRecordTemplates/${id}`);
};
