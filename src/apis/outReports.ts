import { UserOutReports, OutReportsLinks, UserOutReport, OutReportLink } from "@/types/outReports";
import { request } from "@/utils/request";
import { filesToFormData, fileToFormData } from "@/utils/index";

export const requestGetOutReportsByPatient = async (patientId: number): Promise<UserOutReports> => {
  const { data } = await request.get<DTO.OutReports>(`/OutReports/patient/${patientId}`);
  return Mapper.responseOutReportsToUserOutReports(data);
};

export async function requestUploadFile(file: File): Promise<OutReportLink> {
  const req = filesToFormData([file]);
  const res = await request.post("/OutReports/images", req);
  return res.data[0];
}

export async function requestAddOutReport({
  patientId,
  link
}: UserOutReport): Promise<DTO.OutReports> {
  const { data } = await request.post<DTO.OutReports>("/OutReports", {
    patientId,
    title: "",
    time: new Date(),
    imageUrls: [link]
  });
  return data;
}

export const requestDeleteOutReport = async (reportId: number) => {
  await request.delete(`/OutReports/${reportId}`);
};

namespace DTO {
  export interface OutReport {
    id: number;
    imageUrls: string[];
    patientId: number;
    time: Date;
    title: string;
  }
  export type OutReports = OutReport[];

  export interface PreUserOutReports {
    imageUrls: string[];
    patientId: number;
    time: Date;
    title: string;
  }
}

namespace Mapper {
  export function fileToFromData(files: File[]) {
    return files.reduce((result, f) => {
      result.append(f.name, f);
      return result;
    }, new FormData());
  }
  export function userOutReportToAPIOutReports(userOutReport: UserOutReports): DTO.OutReport {
    return {
      id: 0,
      imageUrls: [],
      patientId: userOutReport.patientId,
      time: new Date(),
      title: ""
    };
  }

  export function responseOutReportsToUserOutReports(_outReports: DTO.OutReports): UserOutReports {
    let result: UserOutReports = {
      patientId: 0,
      outReports: []
    };
    if (_outReports.length <= 0) {
      return result;
    }
    result.patientId = _outReports[0].patientId;
    result.outReports = _outReports.reduce<UserOutReports["outReports"]>((res, item) => {
      res.push({
        id: item.id,
        file: null,
        link: item.imageUrls[0]
      });
      return res;
    }, []);

    return result;
  }

  // function apiUserOutReportsToUserOutReports(apiUserOutReport: DTO.UserOutReport): UserOutReports {
  //   if (!Array.isArray(apiUserOutReport.imageUrls)) {
  //     return {
  //       patientId: apiUserOutReport.patientId, //0为无效
  //       outReports: []
  //     };
  //   }
  //   apiUserOutReport.imageUrls.reduce<OutReports>((result, item) => {
  //     result.push({
  //       id: 0,
  //       link: item
  //     });
  //     return result;
  //   }, []);
  //   return {
  //     patientId: apiUserOutReport.patientId, //0为无效
  //     outReports: []
  //   };
  // }
}
