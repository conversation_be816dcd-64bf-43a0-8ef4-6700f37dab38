import { request } from "@/utils/request";

export interface DoctorsRequestParams {
  page: number;
  row: number;
  name?: string;
  roleId?: number;
  userName?: string;
}

export interface DoctorResponseData {
  id: number;
  userName: string;
  name: string;
  password: string;
  headPortrait: string;
  roleId: number;
  isAdmin: boolean;
  phone: string | null;
}

export const requestDoctor = () => {
  return request.get<DoctorResponseData>("/doctors/self");
};

export const requestCreateDoctor = (data: {
  userName: string;
  password: string;
  name: string;
  headPortrait: string;
  roleId: number;
  phone: string | null;
}) => {
  return request.post<DoctorResponseData>("/doctors", data);
};

export const requestUpdateDoctor = (data: {
  id: number;
  userName: string;
  password: string;
  name: string;
  headPortrait: string;
  roleId: number;
  phone: string | null;
}) => {
  return request.put<DoctorResponseData>(`/doctors/${data.id}`, data);
};

export const requestDeleteDoctor = (id: number) => {
  return request.delete(`/doctors/${id}`);
};

export const requestGetDoctors = (filter: DoctorsRequestParams) => {
  return request.get<{ doctors: DoctorResponseData[]; total: number }>("/doctors", {
    params: filter
  });
};
