import { request } from "@/utils/request";
import {
  type SortRule,
  RuleType,
  InspectionsSortRule,
  ExamineSortRule
} from "@/types/reportSortRules";

export namespace OrderingRulesApi {
   interface BackSortRule {
    id:number
    ruleType:number
    detail:BackSortRuleDetail[]
   }
   interface BackSortRuleDetail {
    itemCode:string
    itemName:string
    sort:number
   }
  export const get = async ():Promise<BackSortRule[]> => {
    const { data } = await request.get<BackSortRule[]>("/orderingRules"); 
    return data;
  };
  export const add = async (rule: Omit<SortRule, "id">) => {
    const ruleToAdd = { ...rule };
    if ("id" in ruleToAdd) {
      delete ruleToAdd.id;
    }
    await request.post("/orderingRules", ruleToAdd);
  };

  export const modify = async (rule: SortRule) => {
    await request.put("/orderingRules", rule);
  };
  export const save = async (rule: SortRule) => {
    await request.post("/orderingRules/Save", rule);
  };
  export const deleteRule = async (id: SortRule["id"]) => {
    await request.delete(`/orderingRules/${id}`);
  };

  export const getInspectionsRules = async (): Promise<InspectionsSortRule | null> => {
    const rules: BackSortRule[] = await OrderingRulesApi.get();
 
    const result = rules.find(r => r.ruleType === RuleType.检验);
    if (result) {
      result.detail = result.detail.map(item => ({
        ...item,
        isOld: true
      }));
    }
    return (result as InspectionsSortRule) || null;
  };
  export const getExaminesRules = async (): Promise<ExamineSortRule | null> => {
    const rules: BackSortRule[] = await OrderingRulesApi.get();
    const result = rules.find(r => r.ruleType === RuleType.检查);
    if (result) {
      result.detail = result.detail.map(item => ({
        ...item,
        isOld: true
      }));
    }
    return (result as ExamineSortRule) || null;
  };

  export const saveInspectionsRules = async (rule: InspectionsSortRule) => {
    await save(rule);
  };
  export const saveExaminesRules = async (rule: ExamineSortRule) => {
    await save(rule);
  };
}
