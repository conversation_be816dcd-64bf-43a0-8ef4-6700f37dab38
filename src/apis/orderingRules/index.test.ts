import { describe, it, expect, beforeEach, vi } from 'vitest';
import { OrderingRulesApi } from './index';
import { request } from '@/utils/request';
import { RuleType, type SortRule, type InspectionsSortRule, type ExamineSortRule } from '@/types/reportSortRules';

vi.mock('@/utils/request');

describe('OrderingRulesApi', () => {
  const mockRules: SortRule[] = [
    {
      id: 1,
      ruleType: RuleType.检验,
      detail: [
        { itemName: '检验项目1', itemCode: 'test1', sort: 1, isOld: false },
        { itemName: '检验项目2', itemCode: 'test2', sort: 2, isOld: false }
      ]
    },
    {
      id: 2,
      ruleType: RuleType.检查,
      detail: [
        { itemName: '检查项目1', itemCode: 'exam1', sort: 1, isOld: false },
        { itemName: '检查项目2', itemCode: 'exam2', sort: 2, isOld: false }
      ]
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('get方法', () => {
    it('应该成功获取所有规则', async () => {
      (request.get as ReturnType<typeof vi.fn>).mockResolvedValue({ data: mockRules });
      const result = await OrderingRulesApi.get();
      expect(result).toEqual(mockRules);
      expect(request.get).toHaveBeenCalledWith('/orderingRules');
    });
  });

  describe('add方法', () => {
    it('应该成功添加新规则', async () => {
      const newRule = { ruleType: RuleType.检验, detail: [] };
      await OrderingRulesApi.add(newRule);
      expect(request.post).toHaveBeenCalledWith('/orderingRules', newRule);
    });

    it('添加规则时应该移除id字段', async () => {
      const ruleWithId = { id: '123', ruleType: RuleType.检验, detail: [] };
      await OrderingRulesApi.add(ruleWithId);
      expect(request.post).toHaveBeenCalledWith('/orderingRules', { ruleType: RuleType.检验, detail: [] });
    });
  });

  describe('modify方法', () => {
    it('应该成功修改规则', async () => {
      const rule = mockRules[0];
      await OrderingRulesApi.modify(rule);
      expect(request.put).toHaveBeenCalledWith('/orderingRules', rule);
    });
  });

  describe('save方法', () => {
    it('应该成功保存规则', async () => {
      const rule = mockRules[0];
      await OrderingRulesApi.save(rule);
      expect(request.post).toHaveBeenCalledWith('/orderingRules/Save', rule);
    });
  });

  describe('deleteRule方法', () => {
    it('应该成功删除规则', async () => {
      const ruleId = 1;
      await OrderingRulesApi.deleteRule(ruleId);
      expect(request.delete).toHaveBeenCalledWith(`/orderingRules/${ruleId}`);
    });
  });

  describe('getInspectionsRules方法', () => {
    it('应该返回检验规则并标记detail为isOld', async () => {
      (request.get as ReturnType<typeof vi.fn>).mockResolvedValue({ data: mockRules });
      const result = await OrderingRulesApi.getInspectionsRules();
      expect(result).toEqual({
        ...mockRules[0],
        detail: mockRules[0].detail.map(item => ({ ...item, isOld: true }))
      });
    });

    it('当没有检验规则时应该返回null', async () => {
      (request.get as ReturnType<typeof vi.fn>).mockResolvedValue({ data: [mockRules[1]] });
      const result = await OrderingRulesApi.getInspectionsRules();
      expect(result).toBeNull();
    });
  });

  describe('getExaminesRules方法', () => {
    it('应该返回检查规则并标记detail为isOld', async () => {
      (request.get as ReturnType<typeof vi.fn>).mockResolvedValue({ data: mockRules });
      const result = await OrderingRulesApi.getExaminesRules();
      expect(result).toEqual({
        ...mockRules[1],
        detail: mockRules[1].detail.map(item => ({ ...item, isOld: true }))
      });
    });

    it('当没有检查规则时应该返回null', async () => {
      (request.get as ReturnType<typeof vi.fn>).mockResolvedValue({ data: [mockRules[0]] });
      const result = await OrderingRulesApi.getExaminesRules();
      expect(result).toBeNull();
    });
  });

  describe('saveInspectionsRules方法', () => {
    it('应该成功保存检验规则', async () => {
      const rule = mockRules[0] as InspectionsSortRule;
      await OrderingRulesApi.saveInspectionsRules(rule);
      expect(request.post).toHaveBeenCalledWith('/orderingRules/Save', rule);
    });
  });

  describe('saveExaminesRules方法', () => {
    it('应该成功保存检查规则', async () => {
      const rule = mockRules[1] as ExamineSortRule;
      await OrderingRulesApi.saveExaminesRules(rule);
      expect(request.post).toHaveBeenCalledWith('/orderingRules/Save', rule);
    });
  });
});
