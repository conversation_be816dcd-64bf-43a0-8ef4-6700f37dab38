import { request } from "@/utils/request";
import { Inspections, Inspection } from "@/types/inspection";
import { Examine, type ExamineDetails } from "@/types/examine";
import { safeParseInt } from "@/utils/number";

// 检查数据提供者
export namespace ExaminationDataProvider {
  export const getExaminations = async (
    patientId: number,
    { startTime, endTime }: { startTime: Date; endTime: Date }
  ): Promise<Examine[]> => {
    const { data } = await request.get<ResponseExamine[]>(`/inspection-check/checks/${patientId}`, {
      params: {
        patientId,
        startTime,
        endTime
      }
    });
    if (data.length == 0) return [];
    const raw = getHasItemClassAndReportDate(data);
    raw.sort((a, b) => safeParseInt(a.itemCode) - safeParseInt(b.itemCode));
    raw.sort((a, b) => a.reportDate.localeCompare(b.reportDate));
    const examines = raw.map((d, i) => ({ ...d, id: i + 1, itemClass: d.itemClass ?? "" }));
    return examines;
  };
  const getHasItemClassAndReportDate = <T extends Examine>(examines: ResponseExamine[]): T[] => {
    return examines.filter(e => e.itemClass && e.reportDate) as T[];
  };

  interface ResponseExamine {
    itemCode: string;
    orderPerson: string;
    filePath: string | null;
    examinationDate: string | null;
    seedescex: string | null;
    sickbedNo: string | null;
    orderTime: string;
    itemClass: string | null;
    examReportLid: string | null;
    patientDomainId: string;
    imagUrl: string;
    patientLid: string;
    rportDoctorName: string | null;
    patientName: string;
    orderDept: string;
    reportDate: string | null;
    orderDeptName: string;
    functionRequestId: string;
    itemName: string;
    ipseqnoText: string | null;
    orderPersonName: string;
    execDepartmentName: string;
    resultDescex: string | null;
    orderLid: string;
    patientDomain: string;
    reportDoctor: string | null;
    itemClassName: string | null;
    status: string;
    visitTypeCode: string;
    visitTypeName: string;
  }
}

// export const getExamineDetailsApi = async (
//   patientId: number,
//   { startTime, endTime }: { startTime: Date; endTime: Date }
// ): Promise<ExamineDetails> => {
//   if (startTime.getTime() > endTime.getTime()) {
//     throw new Error("开始时间不能晚于结束时间");
//   }
//   try {
//     const { data } = await request.get<ResponseExamine[]>(`/inspection-check/checks/${patientId}`, {
//       params: {
//         patientId,
//         startTime,
//         endTime
//       }
//     });
//     if (data.length == 0) return [];
//     data.sort((a, b) => Number(a.itemCode) - Number(b.itemCode));
//     const examines = data.map((d, i) => ({ id: i + 1, ...d, itemClass: d.itemClass ?? "" }));
//     return examines;
//   } catch (error) {
//     console.error("获取检查数据失败", error);
//     throw error;
//   }
// };

interface ResponseInspections {
  compositeItemSn: string;
  execDeptName: string;
  highValue: string | null;
  inpatientOrdNo: string | null;
  itemCode: string;
  itemName: string;
  itemNameCn: string;
  itemNameCode: string;
  itemNameEn: string;
  itemUnit: string;
  itemValue: string;
  labReportLid: string;
  labReportSn: number;
  lowValue: string | null;
  normalFlagName: string;
  normalRefValueText: string;
  orderLid: string;
  orderPersonName: string;
  patientDomain: string;
  patientLid: string;
  patientName: string;
  phenomenonPerformance: string | null;
  reportDate: string;
  reporterId: string;
  reporterName: string;
  requestDate: string;
  requestNo: string;
  reviewDate: string;
  reviewerId: string;
  reviewerName: string;
  sampleNo: string;
  sampleTypeName: string;
  submittingPersonId: string;
  submittingPersonName: string;
  testResults: string | null;
  visitOrdNo: string;
  visitTimes: number;
  visitTypeCode: string;
  visitTypeName: string;
  warnHighValue: string | null;
  warnLowValue: string | null;
}
/**
 * 检验信息。
 *
 * 该函数通过发送HTTP GET请求到服务器，获取指定患者在指定时间范围内的检查记录。
 * 使用axios的request.get方法进行网络请求，并通过URL参数传递患者ID和时间范围。
 * @returns 返回从服务器获取的检查记录数据。
 */
export const getInspectionsApi = async (
  patientId: number,
  { startTime, endTime }: { startTime: Date; endTime: Date }
): Promise<Inspections> => {
  // 校验开始时间和结束时间的顺序
  if (startTime.getTime() > endTime.getTime()) {
    throw new Error("开始时间不能晚于结束时间");
  }
  try {
    const { data } = await request.get<ResponseInspections[]>(
      `/inspection-check/inspections/${patientId}`,
      {
        params: { startTime, endTime }
      }
    );
    if (data.length == 0) return [];
    data.sort((a, b) => Number(a.itemNameCode) - Number(b.itemNameCode));
    data.sort((a, b) => a.reportDate.localeCompare(b.reportDate));
    const inspections = data.map((d, i) => ({ id: i + 1, ...d }));
    return inspections;
    // return inspectionsFilterByDate(inspections, startTime, endTime);
  } catch (error) {
    console.error("获取检验数据失败", error);
    throw error;
  }
};
