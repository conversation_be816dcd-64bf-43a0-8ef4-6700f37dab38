import type { Reports, Report, BeforeReport } from "@/types/reports";
import { request } from "@/utils/request";
import { fileToFormData } from "@/utils/index";

export const requestUploadReportFile = async (file: File): Promise<{ url: string }> => {
  const { data } = await request.post("/reports/pdf", fileToFormData(file));
  return data;
  // return await request.post("/reports/pdf");
};

// export const requestUploadReportFile = async (file: { name: string; file: File }) => {
//   await request.post("/reports/pdf", fileToFormData(file));
//   // return await request.post("/reports/pdf");
// };

export const requestAddReports = async (beforeReport: BeforeReport) => {
  return await request.post("/reports", beforeReport);
};

export const requestGetReports = async () => {
  return await request.get<Reports>("/reports");
};

/** 获取自上传的健康报告
 * @param patientId  患者id */
export const requestGetReportsByPatient = async (patientId: Report["patientId"]) => {
  return await request.get<Reports>(`/reports/patient/${patientId}`);
};

export const requestDeleteReports = async (reportId: Report["id"]) => {
  return await request.delete(`/reports/${reportId}`);
};
