import { request } from "@/utils/request";
export interface MedicalRecord {
  family: string;
  irritability: string;
  medicine: string;
  now: string;
  past: string;
}

export interface Patient {
  address: string;
  birthday: string;
  id: number;
  name: string;
  age: number;
  sex: string;
  idCard: string;
  phone: string;
  source: string;
  createTime: string;
  case: MedicalRecord;
  doctorName: string | null;
  lastFollowUpTime: string | null;
  nextFollowUpTime: string | null;
  returnVisitTime: string | null;
  patientIds: string[];
}

// 获取病人列表
export async function getPatients(req: {
  page: number;
  rows: number;
  filter?: string;
}): Promise<{ total: number; data: Patient[] }> {
  const { data } = await request.get<{ total: number; data: Patient[] }>("/patients", {
    params: req
  });
  return data;
}

// 导入
export function postPatients(data: { IdCard: string }) {
  return request.post("/patients", data);
}

// 修改病人信息
export function putPatients(data: { Id: number; Phone: string }) {
  const { Id, ...newData } = data;
  return request.put(`/patients/${Id}`, newData);
}

// 导入前的预览
export function getPreview(data: { IdCard?: string; personName?: string; telePhone?: string }) {
  return request.get(`/patients/preview`, {
    params: data
  });
}

//根据Id获取病人信息
export async function getPatientById(id: number): Promise<Patient> {
  const { data } = await request.get<Patient>(`/patients/${id}`);
  return data;
}

//转移病人
export const requestGetTransferPatients = (data: {
  isTransferAll: boolean;
  targetDoctorNo: string;
  fromDoctorNo: string;
  patientIds: number[];
}) => {
  return request.put(`/patients/transfer/${data.targetDoctorNo}`, data);
};

//设置复诊时间
export const requestReturnVisitTime = (id: number, visitTime: string) => {
  const data = {
    time: visitTime
  };
  return request.put(`/patients/returnVisitTime/${id}`, data);
};

/**
 * 删除病人
 * @param {number} id 病人id
 */
export const requestDeletePatient = (id: number) => {
  return request.delete(`/patients/deletePatient/${id}`);
};

/**
 * 修改病人病历
 * @param id
 * @param data
 * @returns
 */
export const requestEditPatientMedicalRecords = (id: number, data: MedicalRecord) => {
  return request.put(`/patients/case/${id}`, {
    ...data
  });
};
