import { request } from "@/utils/request";

export interface CreateDemand {
  patientId: number;
  content: string;
}

export interface ResponseCreateDemand {
  id: number;
  patientName: string;
  patientPhone: string;
  content: string;
  createTime: string;
  patientId: string;
}

/**
 * 创建需求
 * @param data
 * @returns
 */
export const requestCreateDemand = (data: CreateDemand) => {
  return request.post<ResponseCreateDemand>("/demands", data);
};

/**
 * 获取需求列表
 * @returns
 */
export const requestGetDemands = () => {
  return request.get<ResponseCreateDemand[]>("/demands");
};

/**
 * 根据病人手机号获取病人需求
 * @param phone
 * @returns
 */
export const requestGetDemandsByPhone = async (phone: string) => {
  const res = await request.get<ResponseCreateDemand[]>("/demands");
  return res.data.filter(ele => ele.patientPhone == phone);
};
