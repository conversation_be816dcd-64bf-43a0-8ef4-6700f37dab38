import { request } from "@/utils/request";

interface AddSummaryRecord {
  patientId: number;
  content: string;
  time: Date;
}

interface AddReturnAddSummaryRecord {
  id: number;
  content: string;
  time: Date;
}

interface EditSummaryRecord {
  content: string;
  time: Date;
}

export const requestGetSummaryRecord = (patientId: number) => {
  // 不想删，留着复习
  //   return new Promise<AddReturnAddSummaryRecord>((resolve, rejects) => {
  //     resolve({
  //       data: [
  //         {
  //           id: 1,
  //           content: "content1",
  //           time: new Date()
  //         },
  //         {
  //           id: 2,
  //           content: "content2",
  //           time: new Date()
  //         },
  //         {
  //           id: 3,
  //           content: "content3",
  //           time: new Date()
  //         },
  //         {
  //           id: 4,
  //           content: "content4",
  //           time: new Date()
  //         },
  //         {
  //           id: 5,
  //           content: "content5",
  //           time: new Date()
  //         }
  //       ],
  //       status: 200
  //     });
  //   });

  return request.get<AddReturnAddSummaryRecord[]>(`/SummaryRecords/${patientId}`);
};

/**
 * 新增小结
 * @param data
 * @returns
 */
export const requestAddSummaryRecord = (data: AddSummaryRecord) => {
  //   return new Promise((resolve, rejects) => {
  //     resolve({
  //       data: {
  //         id: 123,
  //         content: "content1",
  //         time: new Date()
  //       },
  //       status: 200
  //     });
  //   });
  return request.post<AddReturnAddSummaryRecord>(`/SummaryRecords`, data);
};

/**
 * 修改小结
 * @param id
 * @returns
 */
export const requestEditSummaryRecord = (id: number, data: EditSummaryRecord) => {
  return request.put(`/SummaryRecords/${id}`, data);
};

/**
 * 删除小结
 * @param id
 * @returns
 */
export const requestDeleteSummaryRecord = (id: number) => {
  return request.delete(`/SummaryRecords/${id}`);
};
