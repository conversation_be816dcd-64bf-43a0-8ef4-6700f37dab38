import type { Menu } from "./menus";
import { request } from "@/utils/request";
export interface DoctorLoginResponseData {
  access_token: string;
  expires_in: number;
  refresh_token: string;
  scope: string;
  token_type: string;
}

export enum WhoIM {
  "用户" = "patient",
  "医生" = "doctor"
}

export interface RefreshCertificateResponseData extends DoctorLoginResponseData {}

export interface AccountOfDoctorResponseData {
  id: number;
  userName: string;
  name: string;
  headPortrait: string;
  roleId: number;
  isAdmin: boolean;
}

export interface NotificationResponseData {
  id: number;
  title: string;
  content: string;
  createTime: Date;
  readed: boolean;
}

export const requestDoctorLogin = (userName: string, password: string, clientId: WhoIM) => {
  return request.post<DoctorLoginResponseData>("/token", {
    userName,
    password,
    clientId
  });
};

export const requestGetAccountOfDoctor = () => {
  return request.get<AccountOfDoctorResponseData>("/doctors/self");
};

export const requestGetAccountMenus = () => {
  return request.get<Menu[]>("/account/menus");
};

export const requestGetNotifications = () => {
  return request.get<NotificationResponseData[]>("/account/notifications");
};

/**
 * 更新凭证
 * @param refreshToken
 * @returns
 */
export const requestRefreshCertificate = (refreshToken: string) => {
  return request.post<{
    access_token: string;
    expires_in: number;
    refresh_token: string;
  }>("/token/certificate", {
    clientId: "doctor",
    refreshToken
  });
};
