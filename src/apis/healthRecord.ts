import { request } from "@/utils/request";

interface AddandUpdateOrganMsg {
  patientId: number;
  abnormalInformation?: string;
}

interface GetPatientOrganMsg {
  name: string;
  abnormalInformation?: string;
}

interface DeleteOrgan {
  patientId: number;
}

// 添加或更新异常部位
export const requestAddandUpdateOrgan = (name: string, data: AddandUpdateOrganMsg) => {
  return request.post<AddandUpdateOrganMsg>(`/aberrantSites/${name}`, data);
};

// 根据病人Id获取异常部位列表
export const requestGetPatientOrganInfo = (patientId: number) => {
  return request.get<GetPatientOrganMsg[]>(`aberrantSites/${patientId}`);
};

// 取消异常部位 name是部位的名字，data传对象，里面是病人的ID
export const requestDeleteOrgan = (name: string, data: DeleteOrgan) => {
  return request.delete(`aberrantSites/${name}`, { data });
};
