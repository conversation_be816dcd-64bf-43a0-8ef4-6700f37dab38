import { request } from "@/utils/request";

export interface RequestGetArticles {
  page: number;
  rows: number;
  classId?: number;
  title?: string;
  state?: number;
}

export interface Article {
  id: number;
  title: string;
  description: string;
  authorName: string;
  classId: number;
  className: string;
  content: string;
  state: number;
  coverThePath: string;
  createTime: string;
  //附件
  pdfUrl: string;
}

export interface ResponseGetArticles {
  articles: Article[];
  count: number;
}

interface CreateArticle {
  authorId: number;
  title: string;
  description: string;
  classId: number;
  content: string;
  coverThePath: string;
  pdfUrl: string;
}

interface ResponseCreateArticle {
  id: number;
  title: string;
  description: string;
  authorName: string;
  classId: number;
  className: string;
  content: string;
  state: number;
  coverThePath: string;
  createTime: string;
  pdfUrl: string;
}

interface UpdateArticle extends CreateArticle {
  id: number;
}

/**
 * 获取文章列表
 * @param params
 * @returns
 */
export const requestGetArticles = (params: RequestGetArticles) => {
  return request.get<ResponseGetArticles>("/articles", {
    params
  });
};

/**
 * 创建文章
 * @param data
 * @returns
 */
export const requestCreateArticle = (data: CreateArticle) => {
  return request.post<ResponseCreateArticle>("/articles", data);
};

/**
 * 修改文章
 * @param data
 * @returns
 */
export const requestUpdateArticle = (data: UpdateArticle) => {
  return request.put<ResponseCreateArticle>("/articles", data);
};

/**
 * 删除文章
 * @param id
 * @returns
 */
export const requestDeleteArticle = (id: number) => {
  return request.delete<ResponseCreateArticle>(`/articles/${id}`, {
    params: {
      id
    }
  });
};

/**
 * 修改文章状态
 * @param params
 * @returns
 */
export const requestChangeArticleState = (params: { id: number; publish: boolean }) => {
  return request.put<ResponseCreateArticle>(
    `/articles/state/${params.id}?publish=${params.publish}`
  );
};

/**
 * 上传图片
 * @param file
 * @returns
 */
export const requestUplaodImage = (file: File) => {
  const fromData = new FormData();
  fromData.append("formFile", file);
  return request.post<{
    url: string;
  }>("/articles/images", fromData, {
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/**
 * 上传附件
 * @param file
 * @returns
 */
export const requestUploadPDF = (file: File) => {
  const fromData = new FormData();
  fromData.append("formFile", file);
  return request.post<{
    url: string;
  }>("/articles/pdf", fromData, {
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
