import { request } from "@/utils/request";

export interface MessageTemplateResponse {
  id: number;
  name: string;
  content: string;
  createTime: string;
}

export interface CreateMessageTemplateRequest {
  name: string;
  content: string;
}

export interface UpdateMessageTemplateRequest {
  id: number;
  name: string;
  content: string;
}

export const requestMessageTemplates = () => {
  return request.get<MessageTemplateResponse[]>("/messageTemplates");
};

export const requestCreateMessageTemplate = (params: CreateMessageTemplateRequest) => {
  return request.post<MessageTemplateResponse>("/messageTemplates", params);
};

export const requestUpdateMessageTemplate = (params: UpdateMessageTemplateRequest) => {
  return request.put<MessageTemplateResponse>(`/messageTemplates/${params.id}`, params);
};

export const requestDeleteMessageTemplate = (id: number) => {
  return request.delete(`/messageTemplates/${id}`);
};
