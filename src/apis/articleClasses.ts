import { request } from "@/utils/request";

export interface ResponseGetArticleClass {
  id: number;
  name: string;
}

/**
 * 获取文章类型列表
 * @returns
 */
export const requestGetArticleClasses = () => {
  return request.get<ResponseGetArticleClass[]>("/articleClasses");
};

/**
 * 创建文章类型
 * @param name
 * @returns
 */
export const requestCreateArticleClass = (name: string) => {
  return request.post<ResponseGetArticleClass>("/articleClasses", { name });
};

/**
 * 修改文章分类名称
 * @param data
 * @returns
 */
export const requestUpdateArticleClass = (data: { id: number; name: string }) => {
  return request.put<ResponseGetArticleClass>(`/articleClasses/id?id=${data.id}`, {
    name: data.name
  });
};

/**
 * 删除
 * @param id
 * @returns
 */
export const requestDeleteArticleClass = (id: number) => {
  return request.delete<ResponseGetArticleClass>(`/articleClasses/id?id=${id}`);
};
