import { request } from "@/utils/request";

export interface Menu {
  parentId?: number;
  id: number;
  name: string;
  path?: string;
  routeName: string;
  sort: number;
  type: number;
  typeStr: string;
  url?: string;
  children: Menu[];
  icon: string;
}

export const requetGetMenus = () => {
  return request.get<Menu[]>("/menus");
};

export const requetCreatedMenu = (req: {
  icon?: string;
  name: string;
  parentId: number;
  path?: string;
  routeName: string;
  sort: number;
  type: number;
  url?: string;
}) => {
  return request.post<Menu>("/menus", req);
};

export const requetUpdateMenu = (menu: {
  id: number;
  icon: string;
  name: string;
  path?: string;
  routeName?: string;
  sort: number;
  url?: string;
}) => {
  const { id, ...data } = menu;
  return request.put<Menu>(`/menus/${id}`, data);
};

export const requestDeleteMenu = (id: number) => {
  return request.delete(`/menus/${id}`);
};
