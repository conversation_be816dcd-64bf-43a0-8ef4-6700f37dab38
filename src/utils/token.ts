class Token {
  accessToken?: string;
  refreshToken?: string;
  expiration?: Date;

  constructor() {
    const accessToken = localStorage.getItem("accessToken");
    const refreshToken = localStorage.getItem("refreshToken");
    const expiration = localStorage.getItem("expiration");

    this.accessToken = accessToken ?? undefined;
    this.refreshToken = refreshToken ?? undefined;
    if (expiration) {
      this.expiration = new Date(expiration);
    }
  }

  get hasRefreshToken() {
    return this.refreshToken ? true : false;
  }

  get expired() {
    if (!this.accessToken) return true;
    if (this.expiration && this.expiration < new Date()) return true;
    return false;
  }

  update(accessToken: string, refreshToken: string, expiration: Date) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    this.expiration = expiration;
    localStorage.setItem("accessToken", accessToken);
    localStorage.setItem("refreshToken", refreshToken);
    localStorage.setItem("expiration", expiration.toString());
  }

  clear() {
    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("expiration");
    this.accessToken = undefined;
    this.refreshToken = undefined;
    this.expiration = undefined;
  }
}

export default new Token();
