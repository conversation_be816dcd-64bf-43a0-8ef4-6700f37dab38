import { differenceInYears, isBefore, isValid, parse, setYear, startOfDay } from "date-fns";

/**
 * 将生日转换为年龄
 * @param birthday 生日字符串或日期对象，格式为 "yyyy-MM-dd HH:mm:ss" 或 Date
 * @returns 年龄（整数）
 */

export function getAge(birthday: string | Date): number {
  let birthDate: Date;

  if (birthday instanceof Date) {
    birthDate = birthday;
  } else if (typeof birthday === "string") {
    birthDate = parse(birthday, "yyyy-MM-dd HH:mm:ss", new Date());
    if (!isValid(birthDate)) {
      throw new Error("无效的生日日期");
    }
  } else {
    throw new Error("无效的生日日期类型");
  }

  // 获取当前日期
  const currentDate = new Date();

  // 计算年龄
  const years = differenceInYears(currentDate, birthDate);
  return years;
  // if (isBirthdayPassed(birthDate)) {
  //   return years + 1;
  // } else {
  //   return years;
  // }
}

function isBirthdayPassed(birthday: Date): boolean {
  // 获取当前日期，并将时间部分设置为 00:00:00
  const today = startOfDay(new Date());

  // 获取今年的生日日期
  const currentYear = today.getFullYear();
  const thisYearBirthday = startOfDay(setYear(birthday, currentYear));

  // 判断今年的生日是否已经过了
  return isBefore(thisYearBirthday, today);
}
