import { ElMessage } from "element-plus";
import type { AxiosError } from "axios";
import axios from "axios";
import { router } from "@/router";
import { createThrottle } from "@/utils/throttle";
import token from "./token";

const throttle = createThrottle(1000);

export const request = axios.create({
  baseURL: `${import.meta.env.VITE_APP_API_URL}/api`,
  timeout: 20000
});
export const requestOfIdentity = axios.create({
  baseURL: `${import.meta.env.VITE_APP_API_URL}/api`,
  timeout: 20000
});

// request.interceptors.request.use(
//   config => {
//     if (!expiresToken()) {
//       const { accessToken } = getTokenOfLocalStorage();
//       config.headers = {
//         Authorization: "Bearer " + accessToken
//       };
//     }
//     return config;
//   },
//   err => {
//     console.error(`axios interceptor request error: ${err}`);
//     router.push({
//       name: "login",
//       query: {
//         redirect: router.currentRoute.value.fullPath
//       }
//     });
//   }
// );

request.interceptors.request.use(
  async config => {
    if (token.accessToken) {
      config.headers.Authorization = "Bearer " + token.accessToken;
    }
    return config;
  },
  err => {
    console.error(`axios interceptor request error: ${err}`);
    router.push({
      name: "login",
      query: {
        redirect: router.currentRoute.value.fullPath
      }
    });
  }
);

request.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // return reponseError401Handler();
          return throttle.start(() => {
            reponseError401Handler();
          });
        case 400:
          return reponseError400Handler(error);
        case 500:
          return reponseError500Handler(error);
      }
    }
    return Promise.reject(error);
  }
);

const reponseError401Handler = () => {
  ElMessage["error"]("凭证已过期,请重新登录");
  router.push({
    name: "login"
  });
};

// const reponseError401Handler = (error: any) => {
//   const { config } = error;
//   const requestCount = parseInt(config.requestCount ?? 1);
//   const { refreshToken } = getTokenOfLocalStorage();
//   if (!refreshToken || requestCount > 2) {
//     throttle.start(() => {
//       ElMessage["error"]("凭证已过期,请重新登录");
//       router.push({
//         name: "login",
//         query: {
//           redirect: router.currentRoute.value.fullPath
//         }
//       });
//     });
//     return Promise.reject(error);
//   }

//   config.requestCount = requestCount + 1;
//   var result = throttle
//     .start(() => {
//       return requestRefreshToken(refreshToken as string, WhoIM.医生).then(
//         token => {
//           const { access_token, refresh_token, expires_in } = token;
//           saveTokenToLocalStorage(access_token, refresh_token, expires_in);
//           return request(config);
//         }
//       );
//     })
//     .catch(err => {
//       return new Promise(resolve => {
//         setTimeout(() => {
//           resolve(request(config));
//         }, 1500);
//       });
//     });
//   return result;
// };

const reponseError400Handler = (error: AxiosError<{ message?: string; status?: number }, any>) => {
  const { response } = error;
  throttle.start(() => {
    if (response?.data.status == 400) {
      ElMessage["error"]("参数验证失败");
    } else {
      ElMessage["error"](response?.data.message);
    }
  });
  return Promise.reject(error);
};

const reponseError500Handler = (error: any) => {
  throttle.start(() => {
    ElMessage["error"]("服务器异常,请稍后重试");
  });
  return Promise.reject(error);
};
