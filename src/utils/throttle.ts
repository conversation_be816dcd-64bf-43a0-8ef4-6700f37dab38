export const createThrottle = (delay: number) => {
  let prev = Date.now() - delay;

  const start = (fn: Function) => {
    const now = Date.now();
    if (now - prev > delay) {
      prev = Date.now();
      const result = fn();
      if (result instanceof Promise) {
        return result;
      } else {
        return Promise.resolve(result);
      }
    } else {
      return Promise.reject();
    }
  };

  return {
    start
  };
};
