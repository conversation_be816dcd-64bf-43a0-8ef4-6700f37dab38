export const groupedToMap = <T, K extends keyof T>(data: T[], key: K): Map<T[K], T[]> => {
  return data.reduce((result, item) => {
    const keyValue: T[K] = item[key];
    const items = result.get(keyValue);
    if (!items) {
      result.set(keyValue, [item]);
    } else {
      items.push(item);
    }
    return result;
  }, new Map<T[K], T[]>());
};

export function deepClone<T>(item: T, map = new WeakMap<object, any>()): T {
  if (item === null || item === undefined) {
    return item;
  }

  if (typeof item !== "object") {
    return item;
  }

  if (item instanceof Date) {
    return new Date(item.getTime()) as T;
  }

  if (item instanceof RegExp) {
    return new RegExp(item.source, item.flags) as T;
  }

  if (map.has(item)) {
    return map.get(item);
  }

  if (Array.isArray(item)) {
    const arrCopy: any[] = [];
    map.set(item, arrCopy);
    item.forEach((val, idx) => {
      arrCopy[idx] = deepClone(val, map);
    });
    return arrCopy as T;
  }

  if (item instanceof Map) {
    const mapCopy = new Map();
    map.set(item, mapCopy);
    item.forEach((value, key) => {
      mapCopy.set(key, deepClone(value, map));
    });
    return mapCopy as T;
  }

  if (item instanceof Set) {
    const setCopy = new Set();
    map.set(item, setCopy);
    item.forEach(value => {
      setCopy.add(deepClone(value, map));
    });
    return setCopy as T;
  }

  const result = {} as { [key: string]: any };
  map.set(item, result);
  Object.keys(item).forEach(key => {
    result[key] = deepClone((item as any)[key], map);
  });
  return result as T;
}
