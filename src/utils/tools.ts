export const deepCloning = (obj: Object) => {
  return JSON.parse(JSON.stringify(obj));
};

export const dateSubT = (times: string) => {
  let date = times.split("T")[0];
  return date;
};

export const generateUniqueId = () => {
  var d = new Date().getTime();
  if (window.performance && typeof window.performance.now === "function") {
    d += performance.now();
  }
  var uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (d + Math.random() * 16) % 16 | 0; // d是随机种子
    d = Math.floor(d / 16);
    return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
  });
  return uuid;
};

export const sleep = (time: number) => {
  return new Promise(resolve => setTimeout(resolve, time));
};
