import { endOfDay, startOfDay, subYears } from "date-fns";
export const arrayToFormData = (payload: { name: string; file: File }[]): FormData => {
  const formData = new FormData();
  payload.forEach(({ name, file }) => {
    formData.append(name, file);
  });
  return formData;
};

export const fileToFormData = (file: File): FormData => {
  const formData = new FormData();
  // formData.append(`${name || file.name}`, file);

  formData.append("formFile", file);
  return formData;
};

export const filesToFormData = (files: File[]): FormData => {
  const formData = new FormData();
  files.forEach(f => {
    formData.append("formFiles", f);
  });

  return formData;
};

export const urlToFullUrl = (url: string) => {
  return `${import.meta.env.VITE_APP_API_URL}${url}`;
};

export const getTimeForSomeYear = (year: number = 1): Date => {
  return endOfDay(subYears(startOfDay(new Date()), year));
};
