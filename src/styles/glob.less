html {
  height: 100%;
  width: 100%;

  font-size: clamp(14px, 64.5%, 64.5%) !important;
}

html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
}

 

.default-page {
  overflow: hidden;
  display: grid;
  grid-template-rows: 50px 50px auto 50px;

  &>*:nth-child(1) {
    align-self: center;
  }

  &>*:nth-child(2) {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    padding: 0 10px;
    height: 100%;
  }

  &>*:nth-child(4) {
    align-self: center;
    justify-self: center;
  }
}

.follow-up-task-dialog {
  height: 75%;
  display: flex;
  flex-direction: column;

  .el-dialog__body {
    height: inherit;
    display: flex;
    gap: 10px;
  }
}

