import { createApp } from "vue";
import { router } from "./router";
import { store, key } from "./store";
import "element-plus/dist/index.css";
import App from "./App.vue";
import print from "vue3-print-nb";
import axios from "axios";
import "./styles/glob.less";

import VxeTable from "vxe-table";
import "vxe-table/lib/style.css";

import VxeUI from "vxe-pc-ui";
import "vxe-pc-ui/lib/style.css";

import VXETablePluginExportXLSX from "vxe-table-plugin-export-xlsx";
import ExcelJS from "exceljs";

import VXETablePluginExportPDF from "vxe-table-plugin-export-pdf";
import { jsPDF } from "jspdf";

const app = createApp(App);
axios.get("/appsettings.json").then(({ data }) => {
  window.config = data;
});
VxeTable.use(VXETablePluginExportXLSX, {
  ExcelJS
});
VxeTable.use(VXETablePluginExportPDF, {
  jsPDF
});

app.use(store, key).use(router).use(VxeUI).use(VxeTable).use(print);

app.mount("#app");
