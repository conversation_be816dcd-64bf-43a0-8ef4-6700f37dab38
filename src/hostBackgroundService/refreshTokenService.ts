import token from "@/utils/token";
import * as tokenService from "@/service/TokenService";
import moment from "moment";

const getDiffByNowTime = (date: Date) => {
  return date.valueOf() - new Date().valueOf();
};
const sleep = (time: number) => {
  return new Promise(resolve => setTimeout(resolve, time));
};
const handle = async () => {
  if (token.expired && token.hasRefreshToken) {
    try {
      const { accessToken, refreshToken, expiration } = await tokenService.refresh(
        token.refreshToken!
      );

      const expirationUp10Mins = moment(expiration).subtract(10, "minute").toDate();

      token.update(accessToken, refreshToken, expirationUp10Mins);

      await sleep(getDiffByNowTime(expirationUp10Mins));
      // await sleep(5000);
    } catch (err) {
      token.clear();
      console.error(err);
      await sleep(10000);
    }
  } else {
    await sleep(10000);
  }
  handle();
};

export default {
  start() {
    handle();
  }
};
