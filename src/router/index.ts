import { createRouter, createWebHashHistory } from "vue-router";
import token from "@/utils/token";

export const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: "/",
      name: "layout",
      component: () => import("@/layout/Index.vue"),
      meta: { auth: true },
      redirect: "/home",
      children: [
        {
          path: "/home",
          name: "home",
          component: () => import("@/views/Home.vue"),
          meta: { title: "首页" }
        },
        {
          path: "/notification",
          name: "notification",
          meta: { title: "通知" },
          component: () => import("@/views/Notification.vue")
        },

        {
          path: ":pathMatch(.*)*",
          name: "404",
          component: () => import("@/views/404.vue")
        }
      ]
    },
    {
      path: "/login",
      name: "login",
      component: () => import("@/views/Login.vue")
    }
  ]
});

router.beforeEach((to, from) => {
  const auth = to.matched.some(item => item.meta.auth);
  if (auth) {
    const { accessToken } = token;
    if (!accessToken)
      return {
        name: "login",
        query: {
          redirect: to.fullPath
        }
      };
  }
});
