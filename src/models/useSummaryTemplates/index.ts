import { requestGetSummaryTemplates } from "@/apis/summaryTemplate";
import { SummaryTemplates } from "@/types/summaryTemplate";

export const useSummaryTemplates = () => {
  const summaryTemplates = ref<SummaryTemplates>([]);

  async function loadSummaryTemplates() {
    const result = await requestGetSummaryTemplates();
    summaryTemplates.value = result;
  }
  return {
    summaryTemplates,
    loadSummaryTemplates
  };
};
