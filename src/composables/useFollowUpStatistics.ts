import { requestStatisticsTask, DoctorTaskStatistical } from "@/apis/followUpTasks";

interface TaskStatisticsResponse {
  taskTotal: number;
  doctorTaskStatisticals: DoctorTaskStatistical[];
}

export default function useFollowUpStatistics() {
  const taskStatistics = ref<TaskStatisticsResponse>({
    taskTotal: 0,
    doctorTaskStatisticals: <DoctorTaskStatistical[]>[]
  });
  const getTaskStatistics = (date: [Date, Date]) => {
    requestStatisticsTask(date).then(r => {
      const data = r.data;
      taskStatistics.value.taskTotal = data.taskTotal;
      taskStatistics.value.doctorTaskStatisticals = data.doctorTaskStatisticals;
    });
  };
  const getTaskStatisticsTopFive = (): DoctorTaskStatistical[] => {
    const res = taskStatistics.value.doctorTaskStatisticals.slice(0, 5);
    if (res) {
      return res;
    } else {
      return [];
    }
  };
  // onMounted(() => {
  //   getTaskStatistics([new Date(), new Date()]);
  // });

  return {
    taskStatistics,
    getTaskStatistics,
    getTaskStatisticsTopFive
  };
}
