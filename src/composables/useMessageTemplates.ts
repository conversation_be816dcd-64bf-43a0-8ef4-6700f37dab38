export interface MessageTemplateItem {
  id: number;
  name: string;
  content: string;
  createTime: string;
}

import { ref, onMounted, reactive, computed, watch } from "vue";
import {
  requestMessageTemplates,
  requestCreateMessageTemplate,
  requestUpdateMessageTemplate,
  requestDeleteMessageTemplate
} from "@/apis/messageTemplate";

export default function useMessageTemplates() {
  const messageTemplates = ref<MessageTemplateItem[]>([]);
  const pagination = reactive({ page: 1, rows: 10 });
  const nameSearchQuery = ref("");

  const messageTemplateOfFilter = computed(() =>
    messageTemplates.value.filter(item => item.name.includes(nameSearchQuery.value))
  );
  const messageTemplateOfPagination = computed(() => {
    const start = (pagination.page - 1) * pagination.rows;
    const end = start + pagination.rows;
    const data = messageTemplateOfFilter.value.slice(start, end);
    return data;
  });
  const total = computed(() => messageTemplateOfFilter.value.length);

  watch(nameSearchQuery, () => (pagination.page = 1));

  const getMessageTemplates = () => {
    requestMessageTemplates().then(res => {
      messageTemplates.value = res.data;
    });
  };

  const addMessageTemplate = (data: { name: string; content: string }) => {
    return requestCreateMessageTemplate(data).then(res => {
      messageTemplates.value.unshift(res.data);
    });
  };

  const editMessageTemplate = (data: { id: number; name: string; content: string }) => {
    return requestUpdateMessageTemplate(data).then(res => {
      const index = messageTemplates.value.findIndex(v => v.id == data.id);
      messageTemplates.value.splice(index, 1, res.data);
    });
  };

  const removeMessageTemplate = (id: number) => {
    return requestDeleteMessageTemplate(id).then(() => {
      const index = messageTemplates.value.findIndex(v => v.id == id);
      messageTemplates.value.splice(index, 1);
    });
  };

  onMounted(getMessageTemplates);
  return {
    messageTemplates: messageTemplateOfPagination,
    pagination,
    total,
    nameSearchQuery,
    getMessageTemplates,
    addMessageTemplate,
    editMessageTemplate,
    removeMessageTemplate
  };
}
