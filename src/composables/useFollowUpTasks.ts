export interface Task {
  id: number;
  name: string;
  doctorNo: string;
  userId: number;
  status: number;
  content: string;
  summary: string | null;
  createTime: Date;
  executionTime: Date;
  isAudit: boolean;
  isNotifyDoctor: boolean;
  statusTxt: string;
}

export enum TaskStatus {
  待审核 = 1,
  已执行 = 2,
  死信 = 3,
  停止 = 4
}

export interface TasksFilter {
  name: string;
  status: number;
  doctorNo: string;
  doctorName: string;
  userId: number;
  userName: string;
}

import { reactive, ref, watch } from "vue";
import { store } from "./../store";

import {
  requestTasks,
  requestUpdateTask,
  requestStopTask,
  requestTryStartTask,
  requestDeleteTask,
  requestSubmitTask,
  requestExecuteTask
} from "@/apis/followUpTasks";

export default function useFollowUpTasks() {
  const tasks = ref<Task[]>([]);
  const tasksTotal = ref<number>(0);
  const pagation = reactive({
    page: 1,
    row: 10
  });
  const tasksFilter = reactive<TasksFilter>({
    name: "",
    status: 0,
    doctorNo: "",
    doctorName: "",
    userId: 0,
    userName: ""
  });
  const deepCloning = (obj: Object) => JSON.parse(JSON.stringify(obj));
  const getTasks = () => {
    requestTasks({ ...pagation, ...tasksFilter }).then(({ data }) => {
      const { total: resTotal, tasks: resTasks } = data;
      const currentDoctorId = store.state.doctor.userName;

      if (resTasks.length == 0) {
        tasks.value = [];
        tasksTotal.value = resTotal;
        return;
      }
      resTasks.forEach(item => {
        const disabled = ref<boolean>(true);
        if (currentDoctorId == item.doctorNo) {
          disabled.value = false;
        }
        if (!store.state.doctor.isAdmin) {
          tasks.value = resTasks.filter((t: any) => t.doctorNo == store.state.doctor.userName);
          tasksTotal.value = tasks.value.length;
        } else {
          tasks.value = resTasks.map(task => ({
            ...task,
            butDisabled: disabled.value
          }));
          tasksTotal.value = resTotal;
        }
      });

      // tasks.value = resTasks
      //   .filter(t => t.doctorNo == currentDoctorId)
      //   .map(task => ({ ...task, butDisabled: true }));
      // tasksTotal.value = tasks.value.length;
    });
  };

  const editTask = (task: Task) => {
    return requestUpdateTask({
      ...task,
      executionTime: new Date(task.executionTime)
    }).then(res => {
      const { data, status } = res;
      if (status == 200) {
        const index = tasks.value.findIndex(t => t.id == data.id);
        tasks.value.splice(index, 1, deepCloning(data));
      }
    });
  };

  const stopTasks = (ids: number[]) => {
    return requestStopTask(ids).then(res => {
      const { status } = res;
      if (status == 200) {
        ids.forEach(id => {
          const index = tasks.value.findIndex(t => t.id == id);
          tasks.value[index].status = TaskStatus.停止;
        });
      }
    });
  };

  const tryStartTasks = (ids: number[]) => {
    return requestTryStartTask(ids).then(res => {
      const { status } = res;
      if (status == 200) {
        ids.forEach(id => {
          const index = tasks.value.findIndex(t => t.id == id);
          tasks.value[index].status = TaskStatus.待审核;
        });
      }
    });
  };

  const deleteTask = (id: number) => {
    return requestDeleteTask(id).then(res => {
      const { status } = res;
      if (status == 200) {
        const index = tasks.value.findIndex(t => t.id == id);
        if (index != -1) tasks.value.splice(index, 1);
      }
    });
  };

  const submitTask = (id: number, summary: string) => {
    return requestSubmitTask(id, summary).then(res => {
      const { status } = res;
      if (status == 200) {
        const task = tasks.value.find(t => t.id == id);
        const index = tasks.value.findIndex(t => t.id == id);
        if (index != -1 && task) {
          task.status = 5;
          tasks.value.splice(index, 1, deepCloning(task));
        }
      }
    });
  };

  const sendTask = (id: number) => {
    return requestExecuteTask(id).then(res => {
      // console.log(res)
      getTasks();
    });
  };

  watch(pagation, () => {
    getTasks();
  });

  watch(tasksFilter, () => {
    getTasks();
  });

  return {
    tasks,
    pagation,
    tasksFilter,
    tasksTotal,
    getTasks,
    editTask,
    deleteTask,
    stopTasks,
    tryStartTasks,
    submitTask,
    sendTask
  };
}
