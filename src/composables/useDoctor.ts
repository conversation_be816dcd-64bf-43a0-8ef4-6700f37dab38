import { reactive, ref } from "vue";
import {
  requestCreateDoctor,
  requestUpdateDoctor,
  requestDoctor,
  requestDeleteDoctor,
  requestGetDoctors
} from "@/apis/doctors";

export interface Doctor {
  id: number;
  userName: string;
  name: string;
  password: string;
  headPortrait: string;
  roleId: number;
  phone: string | null;
}

export interface DoctorFilter {
  page: number;
  row: number;
  name: string;
  roleId: number;
  userName: string;
  phone: string;
}

const DEFAULT_DOCTOR: Doctor = Object.freeze({
  id: 0,
  userName: "default",
  name: "default",
  password: "123",
  headPortrait: "/default-headportrait.jpg",
  roleId: 0,
  phone: ""
});

export function useDoctor() {
  const doctor = reactive({ ...DEFAULT_DOCTOR });

  const createDoctor = () => {
    return requestCreateDoctor(doctor).then(res => {
      setDoctor(res.data);
    });
  };

  const updateDoctor = () => {
    return requestUpdateDoctor(doctor).then(res => {
      setDoctor(res.data);
    });
  };

  const deleteDoctor = () => {
    return requestDeleteDoctor(doctor.id).then(() => {
      setDoctor(DEFAULT_DOCTOR);
    });
  };

  const setDoctor = (result: Doctor) => {
    const { id, userName, name, password, headPortrait, roleId } = result;
    doctor.id = id;
    doctor.userName = userName;
    doctor.name = name;
    doctor.password = password;
    doctor.headPortrait = headPortrait;
    doctor.roleId = roleId;
  };

  return {
    DEFAULT_DOCTOR,
    doctor,
    setDoctor,
    createDoctor,
    updateDoctor,
    deleteDoctor
  };
}

export function useDoctors() {
  const doctorData = ref<Doctor[]>([]);

  const doctorsFilter = reactive<DoctorFilter>({
    page: 1,
    row: 10,
    name: "",
    roleId: 0,
    userName: "",
    phone: ""
  });

  const doctorsTotal = ref(0);

  const getDoctors = () => {
    requestGetDoctors(doctorsFilter).then(res => {
      doctorData.value = res.data.doctors;
      doctorsTotal.value = res.data.total;
    });
  };

  const addDoctor = (doctor: Doctor) => {
    doctorData.value.push(doctor);
  };

  const editDoctor = (doctor: Doctor) => {
    const doctorOfEdit = doctorData.value.find(d => d.id == doctor.id);
    if (doctorOfEdit) {
      doctorOfEdit.name = doctor.name;
      doctorOfEdit.password = doctor.password;
      doctorOfEdit.headPortrait = doctor.headPortrait;
      doctorOfEdit.roleId = doctor.roleId;
    }
  };

  const removeDoctor = (id: number) => {
    const index = doctorData.value.findIndex(d => d.id == id);
    if (index >= 0) {
      doctorData.value.splice(index, 1);
    }
  };

  return {
    doctorData,
    doctorsFilter,
    doctorsTotal,
    getDoctors,
    addDoctor,
    editDoctor,
    removeDoctor
  };
}
