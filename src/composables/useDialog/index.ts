interface Dialog {
  visible: boolean;
  title: string;
}

export const useDialog = (title?: string) => {
  /** 对话框 */
  const dialog = ref<Dialog>({
    visible: false,
    title: title || ""
  });

  /** 设置对话框的标题
   * @param title 标题文本 */
  function setTitle(title: string) {
    dialog.value.title = title;
  }

  /** 打开对话框
   * @param title 对话框标题 */
  function openDialog() {
    dialog.value = {
      visible: true,
      title: dialog.value.title
    };
  }

  /** 关闭对话框
   * @param title 对话框标题 */
  function closeDialog() {
    dialog.value = {
      visible: false,
      title: dialog.value.title
    };
  }

  return {
    dialog,

    setTitle,
    openDialog,
    closeDialog
  };
};
