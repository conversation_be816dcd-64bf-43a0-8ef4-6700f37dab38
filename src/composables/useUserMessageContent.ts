export interface User {
  name: string;
  sex: string;
  age: number;
  idCard: string;
}

export interface MessageParam {
  label: string;
  value: string;
}

export default function useMessageContent(user: User, messageTemplateContent: Ref<string>) {
  const messageParams = ref<MessageParam[]>([]);

  const messageContent = computed(() => {
    let content = messageTemplateContent.value;

    for (const userProperty in user) {
      content = content.replace(`\$<${userProperty}>`, user[userProperty as keyof User] as string);
    }
    content = messageParams.value.reduce((conetnt, param) => {
      if (param.value) {
        return conetnt.replace(`\${${param.label}}`, ` ${param.value} `);
      } else {
        return conetnt;
      }
    }, content);

    return content;
  });

  watch(messageTemplateContent, v => {
    messageParams.value =
      v.match(/(?<=\${).+?(?=})/g)?.map(item => ({
        label: item,
        value: ""
      })) ?? [];
  });

  return {
    messageParams,
    messageContent
  };
}
