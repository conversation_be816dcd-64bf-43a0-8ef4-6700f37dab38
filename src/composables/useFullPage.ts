export const useFullPage = () => {
  const pageData = reactive({
    currentPage: 1,
    pageSize: 20,
    allList: [] as any[], //全部数据
    searchList: [] as any[], //搜索后的数据
    viewList: [] as any[] //绑定到视图的数据
  });

  // 搜索
  const searchResult = (condition: string) => {
    pageData.currentPage = 1;
    let searchList = pageData.allList.filter(item => {
      // return item.person_name.indexOf(condition) !=-1
      console.log(condition);
      console.log(item.itemCode);

      let a = new Function("item", "condition", `return ${condition}`)(item, condition);
      console.log(a);

      return a;
    });
    console.log(searchList);
    pageData.searchList = searchList;
    searchFullPage();
  };

  // 当前页改变的回调函数
  const handleCurrentChange = () => {
    console.log(pageData.allList);
    searchFullPage();
  };
  // 把搜索后的数据进行分页
  const searchFullPage = () => {
    let start = (pageData.currentPage - 1) * pageData.pageSize;
    let end = pageData.currentPage * pageData.pageSize;
    pageData.viewList = pageData.searchList.slice(start, end);
  };

  // 初始化
  const pageInit = (list: any[]) => {
    pageData.allList = list;
    pageData.searchList = JSON.parse(JSON.stringify(list));
    searchFullPage();
  };
  return {
    pageData,
    handleCurrentChange,
    searchResult,
    pageInit
  };
};
