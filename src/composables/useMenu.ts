import { ref } from "vue";
import { requetGetMenus } from "@/apis/menus";

export interface Menu {
  id: number;
  name: string;
  url?: string;
  path?: string;
  type: number;
  icon?: string;
  sort?: number;
  children: Menu[];
}

export function useMenus() {
  const meuns = ref<Menu[]>([]);

  const getMenus = () => {
    return requetGetMenus().then(res => {
      meuns.value = res.data;
    });
  };

  return {
    meuns,
    getMenus
  };
}
