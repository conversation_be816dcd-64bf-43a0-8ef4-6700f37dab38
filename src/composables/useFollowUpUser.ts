export interface FollowUpUser {
  id: number;
  name: string;
  sex: string;
  age: number;
  idCard: string;
  phone: string;
  createTime: string;
  source: string;
}

import { getPatientById } from "@/apis/patients";

export default function useFollowUpUser(id: Ref<number>) {
  const followUpUser = reactive<FollowUpUser>({
    id: 0,
    name: "",
    sex: "男",
    age: 0,
    idCard: "",
    phone: "",
    createTime: new Date().toDateString(),
    source: ""
  });

  const getFollowUpUser = async () => {
    const patient = await getPatientById(id.value);

    followUpUser.id = patient.id;
    followUpUser.name = patient.name;
    followUpUser.sex = patient.sex;
    followUpUser.idCard = patient.idCard;
    followUpUser.phone = patient.phone;
    followUpUser.source = patient.source;
    followUpUser.createTime = patient.createTime;
  };

  watch(id, getFollowUpUser);

  onMounted(() => {
    console.log("getFollowUpUser");
    getFollowUpUser();
  });
  return {
    followUpUser,
    getFollowUpUser
  };
}
