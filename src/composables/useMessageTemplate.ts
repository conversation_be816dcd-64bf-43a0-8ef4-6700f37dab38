import { reactive } from "vue";
import {
  requestCreateMessageTemplate,
  requestUpdateMessageTemplate,
  requestDeleteMessageTemplate,
  MessageTemplateResponse
} from "@/apis/messageTemplate";

export interface MessageTemplate {
  id: number;
  name: string;
  content: string;
  createTime: string;
}

export interface MessageTemplateFilter {
  page: number;
  row: number;
  name: string;
}

const DEFAULT_MESSAGE_TEMPLATE = Object.freeze<MessageTemplate>({
  id: 0,
  name: "",
  content: "",
  createTime: ""
});

export default function useMessageTemplate() {
  const messageTemplate = reactive({
    ...DEFAULT_MESSAGE_TEMPLATE
  });

  const createMessageTemplate = (params: MessageTemplate) => {
    return requestCreateMessageTemplate(params).then(res => {
      setMessageTemplate(res.data);
    });
  };

  const updateMessageTemplate = (params: MessageTemplate) => {
    return requestUpdateMessageTemplate(params).then(res => {
      setMessageTemplate(res.data);
    });
  };

  const deleteMessageTemplate = (id: number) => {
    return requestDeleteMessageTemplate(id).then(() => {
      setMessageTemplate(DEFAULT_MESSAGE_TEMPLATE);
    });
  };

  const setMessageTemplate = (data: MessageTemplateResponse) => {
    messageTemplate.id = data.id;
    messageTemplate.name = data.name;
    messageTemplate.content = data.content;
    messageTemplate.createTime = data.createTime;
  };

  return {
    DEFAULT_MESSAGE_TEMPLATE,
    messageTemplate,
    createMessageTemplate,
    updateMessageTemplate,
    deleteMessageTemplate,
    setMessageTemplate
  };
}
