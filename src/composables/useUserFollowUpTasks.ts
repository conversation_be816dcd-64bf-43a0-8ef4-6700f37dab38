export interface FollowUpTaskItem {
  id: number;
  name: string;
  doctorNo: string;
  userId: number;
  status: number;
  content: string;
  summary: string | null;
  createTime: Date;
  executionTime: Date;
  isNotifyDoctor: boolean;
}

import type { FollowUpUser } from "@/composables/useFollowUpUser";
import { ref, watch } from "vue";
import { requestCreateTasks, requestTasks } from "@/apis/followUpTasks";
import moment from "moment";

const ROWS = 10;
export default function useUserFollowUpTasksOfScroll(followUpUser: FollowUpUser) {
  const followUpTasks = ref<FollowUpTaskItem[]>([]);
  const page = ref(1);

  const pushFollowUpTask = (followUpTask: FollowUpTaskItem) => {
    if (followUpTasks.value.some(t => t.id == followUpTask.id)) {
      return;
    }
    followUpTasks.value.push(followUpTask);
    followUpTasks.value.sort((cur: FollowUpTaskItem, next: FollowUpTaskItem) => {
      return moment(next.createTime).valueOf() - moment(cur.createTime).valueOf();
    });
  };

  const nextFollowUpTasks = () => {
    if (followUpUser.id == 0) return;
    return requestTasks({
      page: page.value,
      row: ROWS,
      userId: followUpUser.id
    }).then(res => {
      if (res.data.tasks.length > 0) page.value += 1;
      const data = res.data.tasks;
      for (const item of data) {
        pushFollowUpTask(item);
      }
    });
  };

  const addUserFollowUpTasks = (
    data: {
      name: string;
      content: string;
      executionTime: Date;
      isNotifyDoctor: boolean;
    }[]
  ) => {
    return requestCreateTasks({
      userId: followUpUser.id,
      tasks: data
    }).then(res => {
      res.data.forEach(item => pushFollowUpTask(item));
    });
  };

  watch(followUpUser, () => {
    page.value = 1;
    followUpTasks.value = [];
    nextFollowUpTasks();
  });

  return {
    followUpTasks,
    nextFollowUpTasks,
    addUserFollowUpTasks
  };
}
