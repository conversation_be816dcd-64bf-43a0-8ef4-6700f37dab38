export enum Mode {
  "编辑" = "EDIT",
  "新增" = "ADD"
}
export const useModeAddOrEdit = () => {
  // 模式
  const mode = ref(Mode.新增);

  /** 设置模式
   * @param  newMode 模式
   */
  function setMode(newMode: Mode) {
    mode.value = newMode;
  }

  function modeIsEdit() {
    return mode.value === Mode.编辑;
  }
  function modeIsAdd() {
    return mode.value === Mode.新增;
  }
  function setModeToAdd() {
    mode.value = Mode.新增;
  }
  function setModeToEdit() {
    mode.value = Mode.编辑;
  }

  return {
    mode,
    modeIsEdit,
    modeIsAdd,
    setModeToAdd,
    setModeToEdit,
    setMode
  };
};
