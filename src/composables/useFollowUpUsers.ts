export interface FollowUpUserItem {
  id: number;
  name: string;
  age: number;
  sex: string;
  idCard: string;
  phone: string;
  source: string;
  createTime: string;
  doctorName: string;
  nextFollowUpTime: Date | null;
  lastFollowUpTime: Date | null;
}

import { birthdayConversionAge } from "@/utils/validationRules";
import { computed, onMounted, reactive, ref, watch } from "vue";
import { getPatients } from "@/apis/patients";

export default function useFollowUpUsers() {
  const followUpUsers = ref<FollowUpUserItem[]>([]);
  const followUpUsersTotal = ref(0);
  const pagination = reactive({
    page: 1,
    rows: 10
  });
  const filters = reactive({
    name: "",
    idCard: ""
  });
  const total = computed(() => followUpUsersTotal.value);

  const getFollowUpUsers = () => {
    return getPatients({
      page: pagination.page,
      rows: pagination.rows,
      filter: filters.name
    }).then(({ data: users, total }) => {
      // const { data: users, total } = data;
      followUpUsers.value = users.map((f: any) => ({
        ...f,
        age: birthdayConversionAge(f.birthday),
        patientIdToStr: f.patientIds.join("、")
      }));
      // users.forEach((item: any) => {
      //   followUpUsers.value.push(
      //     Object.assign({}, item, { age: birthdayConversionAge(item.birthday) })
      //   );
      // });

      followUpUsersTotal.value = total;
    });
  };

  watch(
    () => filters,
    () => {
      if (pagination.page == 1) getFollowUpUsers();
      else pagination.page = 1;
    },
    { deep: true }
  );

  watch(() => pagination, getFollowUpUsers, { deep: true });

  onMounted(() => {
    getFollowUpUsers();
  });

  return {
    followUpUsers,
    total,
    pagination,
    filters,
    getFollowUpUsers
  };
}
