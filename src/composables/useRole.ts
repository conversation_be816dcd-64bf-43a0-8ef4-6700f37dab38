import {
  requestRoles,
  requestCreateRole,
  requestUpdateRole,
  requestDeleteRole
} from "@/apis/roles";
import { reactive, ref, computed } from "vue";

export interface Role {
  id: number;
  name: string;
  isAdmin: boolean;
  menuIds: number[];
}

export interface rolesFilter {
  name: string;
  page: number;
  rows: number;
}

const DEFAULT_ROLE: Role = Object.freeze({
  id: 0,
  name: "",
  isAdmin: false,
  menuIds: []
});

export function useRole() {
  const role = reactive<Role>({ ...DEFAULT_ROLE });

  const updateRole = () => {
    return requestUpdateRole(role).then(res => {
      setRole(res.data);
    });
  };

  const createRole = () => {
    return requestCreateRole(role).then(res => {
      setRole(res.data);
    });
  };

  const deleteRole = () => {
    return requestDeleteRole(role.id).then(() => {
      setRole(DEFAULT_ROLE);
    });
  };

  const setRole = (result: Role) => {
    const { id, name, isAdmin, menuIds } = result;
    role.id = id;
    role.name = name;
    role.menuIds = menuIds;
    role.isAdmin = isAdmin;
  };

  return {
    DEFAULT_ROLE,
    role,
    setRole,
    createRole,
    updateRole,
    deleteRole
  };
}

export function useRoles() {
  const rolesData = ref<Role[]>([]);

  const rolesFilter = reactive<rolesFilter>({
    name: "",
    page: 1,
    rows: 15
  });

  const roles = computed(() => {
    const { page, rows } = rolesFilter;
    const start = (page - 1) * rows;
    const end = start + rows;
    return rolesData.value.filter(role => role.name.includes(rolesFilter.name)).slice(start, end);
  });

  const rolesTotal = computed(
    () => rolesData.value.filter(role => role.name.includes(rolesFilter.name)).length
  );

  const removeRole = (role: Role) => {
    const index = rolesData.value.findIndex(item => item.id == role.id);
    if (index != -1) rolesData.value.splice(index, 1);
  };

  const addRole = (role: Role) => {
    rolesData.value.push(role);
  };

  const editRole = (role: Role) => {
    const roleOfEdit = roles.value.find(r => r.id == role.id);
    if (roleOfEdit) {
      roleOfEdit.name = role.name;
      roleOfEdit.isAdmin = role.isAdmin;
      roleOfEdit.menuIds = role.menuIds;
    }
  };

  const getRoles = () => {
    requestRoles().then(res => {
      rolesData.value = res.data;
    });
  };

  return {
    roles,
    rolesTotal,
    removeRole,
    addRole,
    editRole,
    rolesFilter,
    getRoles
  };
}
