/// <reference types="vite/client" />
declare module "vue3-print-nb";
interface ImportMetaEnv {
  readonly VITE_APP_API_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

declare interface Window {
  config: {
    readonly WangEditApiUrl: string;
  };
}

declare module "*.vue" {
  import { DefineComponent } from "vue";
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>;
  export default component;
}
declare module "element-plus/dist/locale/zh-cn.mjs";
