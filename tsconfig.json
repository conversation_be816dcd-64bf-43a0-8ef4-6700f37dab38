{"compilerOptions": {"target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "strict": true, "noImplicitAny": false, "jsx": "preserve", "skipLibCheck": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["element-plus/global", "vite/client"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}