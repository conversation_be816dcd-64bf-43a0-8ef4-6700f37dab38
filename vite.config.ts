/// <reference types="vitest" />
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import vueDevTools from "vite-plugin-vue-devtools";
// import { VxeResolver } from "@vxecli/import-unplugin-vue-components";

// https://vitejs.dev/config/
export default defineConfig({
  // define: { __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: JSON.stringify(true) },
  server: {
    host: true,
    port: 3000
  },
  test: {
    environment: "jsdom"
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes("node_modules")) {
            return "vendor";
          }
        }
      }
    }
  },
  resolve: {
    alias: [
      {
        find: "@",
        replacement: resolve(__dirname, "src")
      }
    ]
  },
  plugins: [
    vue(),
    vueDevTools(),
    AutoImport({
      resolvers: [ElementPlusResolver()],
      imports: ["vue", "vue-router"]
    }),
    Components({
      resolvers: [
        ElementPlusResolver()
        // VxeResolver({
        //   libraryName: "vxe-pc-ui"
        //   // importStyle: true
        // }),
        // VxeResolver({
        //   libraryName: "vxe-table"
        //   // importStyle: true
        // })
      ]
    })
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          hack: `true;@import '${resolve("src/styles/glob.less")}';`
        }
      }
    }
  },
  optimizeDeps: {
    include: [
      "element-plus/es",
      "element-plus/es/components/loading/style/css",
      "element-plus/es/components/config-provider/style/css",
      "element-plus/es/components/divider/style/css",
      "element-plus/es/components/icon/style/css",
      "element-plus/es/components/scrollbar/style/css",
      "element-plus/es/components/avatar/style/css",
      "element-plus/es/components/image/style/css",
      "element-plus/es/components/dialog/style/css",
      "element-plus/es/components/form/style/css",
      "element-plus/es/components/upload/style/css",
      "element-plus/es/components/form-item/style/css",
      "element-plus/es/components/pagination/style/css",
      "element-plus/es/components/button/style/css",
      "element-plus/es/components/switch/style/css",
      "element-plus/es/components/input/style/css",
      "element-plus/es/components/table/style/css",
      "element-plus/es/components/table-column/style/css",
      "element-plus/es/components/empty/style/css",
      "element-plus/es/components/collapse/style/css",
      "element-plus/es/components/collapse-item/style/css",
      "element-plus/es/components/badge/style/css",
      "element-plus/es/components/select/style/css",
      "element-plus/es/components/option/style/css",
      "element-plus/es/components/tag/style/css",
      "element-plus/es/components/message/style/css",
      "element-plus/es/components/message-box/style/css",
      "element-plus/es/components/input-number/style/css",
      "element-plus/es/components/radio-group/style/css",
      "element-plus/es/components/radio-button/style/css",
      "element-plus/es/components/card/style/css",
      "element-plus/es/components/tree/style/css",
      "element-plus/es/components/date-picker/style/css",
      "element-plus/es/components/infinite-scroll/style/css",
      "element-plus/es/components/timeline/style/css",
      "element-plus/es/components/timeline-item/style/css",
      "element-plus/es/components/popover/style/css",
      "element-plus/es/components/progress/style/css",
      "element-plus/es/components/radio/style/css",
      "element-plus/es/components/checkbox/style/css",
      "element-plus/es/components/checkbox-group/style/css",
      "element-plus/es/components/tabs/style/css",
      "element-plus/es/components/tab-pane/style/css",
      "element-plus/es/components/breadcrumb/style/css",
      "element-plus/es/components/breadcrumb-item/style/css",
      "element-plus/es/components/skeleton/style/css",
      "element-plus/es/components/dropdown/style/css",
      "element-plus/es/components/dropdown-menu/style/css",
      "element-plus/es/components/dropdown-item/style/css",
      "element-plus/es/components/menu/style/css",
      "element-plus/es/components/menu-item/style/css",
      "element-plus/es/components/sub-menu/style/css"
    ]
  }
});
